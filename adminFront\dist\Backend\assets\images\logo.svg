<svg xmlns="http://www.w3.org/2000/svg" width="140" height="40.485" viewBox="0 0 140 40.485">
  <g id="Group_5" data-name="Group 5" transform="translate(-355.875 -279.483)">
    <g id="Group_2" data-name="Group 2" transform="translate(355.875 279.884)">
      <path id="Path_1" data-name="Path 1" d="M355.875,299.962c.391.033.848.033,1.239.033,1.141,0,2.055-.424,2.055-2.186V280.392h3.588v17.156c0,4.567-2.447,5.741-5.154,5.741a14.644,14.644,0,0,1-1.729-.066Z" transform="translate(-355.875 -280.392)" fill="#846a52"/>
      <path id="Path_2" data-name="Path 2" d="M386.177,290.014H391.1v3.261h-4.925v6.687h6.2v3.261h-9.785V280.392h9.785v3.261h-6.2Z" transform="translate(-370.815 -280.392)" fill="#846a52"/>
      <path id="Path_3" data-name="Path 3" d="M425.782,303.223h-3.621l-.62-4.142h-4.4l-.62,4.142h-3.294l3.653-22.831h5.251Zm-8.187-7.241h3.457l-1.729-11.546Z" transform="translate(-387.949 -280.392)" fill="#846a52"/>
      <path id="Path_4" data-name="Path 4" d="M454.194,286.687v16.536h-3.229V280.392h4.5l3.685,13.666V280.392h3.2v22.831h-3.685Z" transform="translate(-409.055 -280.392)" fill="#846a52"/>
    </g>
    <g id="Group_3" data-name="Group 3" transform="translate(418.625 279.483)">
      <path id="Path_5" data-name="Path 5" d="M504.436,279.483a15.217,15.217,0,0,1,1.534,1.789h4.448v2.1h-2.531l1.151.409a31.456,31.456,0,0,1-1.329,3.579h2.709v1.994H505.74v1.994h4.576v2.019h-4.371a37.194,37.194,0,0,1,4.371,3.681l-1.483,1.508a22.629,22.629,0,0,0-3.093-2.914v7.311h-2.2v-6.723a19.351,19.351,0,0,1-3.911,4.627l-1.381-2.019a17.576,17.576,0,0,0,4.959-5.47H498.94v-2.019h4.6v-1.994h-4.908v-1.994h2.838a33.03,33.03,0,0,0-1.125-3.5l1.329-.485h-2.939v-2.1h4.474a9.184,9.184,0,0,0-1.074-1.508Zm-.844,7.874h2.019a31.921,31.921,0,0,0,1.33-3.988h-4.627A29.05,29.05,0,0,1,503.593,287.357Zm16.922-7.183,1.3,2.4a34.817,34.817,0,0,1-7.8,1.15v4.141h7.8v2.1h-2.607V302.95h-2.3V289.964h-2.889v2.2a28.98,28.98,0,0,1-.844,5.752,16.192,16.192,0,0,1-2.352,4.934l-1.713-1.815a13.631,13.631,0,0,0,1.892-3.885,25.173,25.173,0,0,0,.716-4.985V281.682A22.31,22.31,0,0,0,520.515,280.174Z" transform="translate(-498.25 -279.483)" fill="#3a4246"/>
      <path id="Path_6" data-name="Path 6" d="M570.935,279.658h1.176a22.515,22.515,0,0,0,4.78,4.218,29.005,29.005,0,0,0,5.905,2.889l-1.585,2.48a20,20,0,0,1-3.3-1.534v1.636H564.135v-1.611q-1.636,1-3.706,2.019l-1.279-2.5a28.035,28.035,0,0,0,6.6-3.579,26.883,26.883,0,0,0,3.988-4.013Zm8.666,12.5v10.89h-2.48v-1.406H565.132v1.406h-2.506v-10.89Zm-14.468,7.285H577.12v-5.087H565.132Zm.051-12.372H576.89a29.418,29.418,0,0,1-5.956-5.215,35.931,35.931,0,0,1-4.4,4.243A16.134,16.134,0,0,1,565.183,287.071Z" transform="translate(-532.309 -279.581)" fill="#3a4246"/>
      <path id="Path_7" data-name="Path 7" d="M620.461,285.67h5.6v1.687a34.373,34.373,0,0,1-2.684,4.5h2.786v1.406a24.563,24.563,0,0,1-1.022,3.911,9.273,9.273,0,0,0,2.531,2.2,8.261,8.261,0,0,0,2.48.741q1.917.257,7.515.332,4.064,0,6.084-.2l-.307,2.3q-10.761,0-13.139-.281a11.43,11.43,0,0,1-3.426-.946,7.848,7.848,0,0,1-2.581-2.25,24.309,24.309,0,0,1-2.428,3.886l-1.585-1.713a17.117,17.117,0,0,0,2.4-3.554,15.344,15.344,0,0,0,1.33-3.834H620.87v-1.892a27.989,27.989,0,0,0,2.71-4.192h-3.119Zm2.3-5.9a24.65,24.65,0,0,1,2.991,3.5l-1.584,1.611a22.377,22.377,0,0,0-2.889-3.707Zm4.908,1.712h6.391v-1.994h2.275v1.994h6.391v1.892h-6.391v1.585h7.209v1.815h-3.272l-.614,1.892h3.374v1.994h-6.774v1.406h6.467v1.789h-6.467v1.508h7.081v1.789h-7.081v2.505h-2.3v-2.505h-7.209v-1.789h7.209V293.85h-6.493V292.06h6.493v-1.406h-7V288.66h3.655l-.511-1.892h-2.94v-1.815h6.9v-1.585H627.67Zm5.317,7.184h4.269l.614-1.892h-5.394Z" transform="translate(-566.498 -279.483)" fill="#3a4246"/>
    </g>
    <g id="Group_4" data-name="Group 4" transform="translate(356.882 310.578)">
      <path id="Path_8" data-name="Path 8" d="M358.161,355.233l.866-.119a1.851,1.851,0,0,0,.312,1.138.982.982,0,0,0,.767.307,1.142,1.142,0,0,0,.624-.166.85.85,0,0,0,.361-.451,3,3,0,0,0,.1-.908v-5h.961v4.945a3.6,3.6,0,0,1-.22,1.41,1.569,1.569,0,0,1-.7.762,2.3,2.3,0,0,1-1.121.262,1.892,1.892,0,0,1-1.463-.55A2.269,2.269,0,0,1,358.161,355.233Z" transform="translate(-358.16 -350.036)" fill="#3a4246"/>
      <path id="Path_9" data-name="Path 9" d="M371.51,357.292v-7.256h5.247v.856h-4.287v2.222h4.015v.851h-4.015v2.47h4.455v.856Z" transform="translate(-365.626 -350.036)" fill="#3a4246"/>
      <path id="Path_10" data-name="Path 10" d="M385.687,357.292l2.787-7.256h1.034l2.97,7.256h-1.094l-.846-2.2H387.5l-.8,2.2Zm2.093-2.98h2.461l-.758-2.009q-.346-.916-.515-1.5a9.282,9.282,0,0,1-.391,1.386Z" transform="translate(-373.555 -350.036)" fill="#3a4246"/>
      <path id="Path_11" data-name="Path 11" d="M403.5,357.292v-7.256h.985l3.811,5.7v-5.7h.921v7.256h-.985l-3.811-5.7v5.7Z" transform="translate(-383.519 -350.036)" fill="#3a4246"/>
      <path id="Path_12" data-name="Path 12" d="M428.971,356.7l.129.788a3.3,3.3,0,0,1-.673.079,1.524,1.524,0,0,1-.752-.153.86.86,0,0,1-.376-.4,3.2,3.2,0,0,1-.109-1.052v-3.024h-.654v-.693h.654v-1.3l.886-.535v1.837h.9v.693h-.9v3.073a1.539,1.539,0,0,0,.047.49.372.372,0,0,0,.153.174.6.6,0,0,0,.3.064A2.883,2.883,0,0,0,428.971,356.7Z" transform="translate(-396.4 -350.243)" fill="#3a4246"/>
      <path id="Path_13" data-name="Path 13" d="M437.95,357.986l.92.114a2.244,2.244,0,0,1-.807,1.252,2.732,2.732,0,0,1-3.333-.265,2.775,2.775,0,0,1-.675-1.992,2.9,2.9,0,0,1,.683-2.059,2.311,2.311,0,0,1,1.772-.733,2.249,2.249,0,0,1,1.722.718,2.853,2.853,0,0,1,.668,2.019c0,.053,0,.132-.005.238h-3.92a2.019,2.019,0,0,0,.49,1.327,1.457,1.457,0,0,0,1.1.46,1.364,1.364,0,0,0,.837-.257A1.705,1.705,0,0,0,437.95,357.986Zm-2.925-1.44h2.935a1.781,1.781,0,0,0-.337-.995,1.364,1.364,0,0,0-1.1-.515,1.412,1.412,0,0,0-1.031.411A1.623,1.623,0,0,0,435.025,356.546Z" transform="translate(-400.606 -352.423)" fill="#3a4246"/>
      <path id="Path_14" data-name="Path 14" d="M451.294,357.754l.876.114a2.271,2.271,0,0,1-.735,1.418,2.141,2.141,0,0,1-1.453.513,2.257,2.257,0,0,1-1.735-.706,2.861,2.861,0,0,1-.656-2.022,3.659,3.659,0,0,1,.282-1.49,1.966,1.966,0,0,1,.859-.958,2.546,2.546,0,0,1,1.254-.319,2.181,2.181,0,0,1,1.4.433,1.984,1.984,0,0,1,.7,1.23l-.866.134a1.44,1.44,0,0,0-.438-.8,1.135,1.135,0,0,0-.76-.267,1.383,1.383,0,0,0-1.093.483,2.291,2.291,0,0,0-.42,1.527,2.371,2.371,0,0,0,.405,1.539,1.322,1.322,0,0,0,1.06.48,1.247,1.247,0,0,0,.876-.322A1.59,1.59,0,0,0,451.294,357.754Z" transform="translate(-408.176 -352.423)" fill="#3a4246"/>
      <path id="Path_15" data-name="Path 15" d="M460.4,357.292v-7.256h.891v2.6a1.99,1.99,0,0,1,1.574-.722,2.125,2.125,0,0,1,1.015.23,1.353,1.353,0,0,1,.616.636,2.933,2.933,0,0,1,.185,1.177v3.332h-.891v-3.332a1.379,1.379,0,0,0-.289-.972,1.077,1.077,0,0,0-.82-.3,1.437,1.437,0,0,0-.744.205,1.157,1.157,0,0,0-.5.556,2.558,2.558,0,0,0-.149.971v2.876Z" transform="translate(-415.339 -350.036)" fill="#3a4246"/>
      <path id="Path_16" data-name="Path 16" d="M473.881,359.679v-5.257h.8v.748a1.889,1.889,0,0,1,1.673-.866,2.2,2.2,0,0,1,.874.171,1.376,1.376,0,0,1,.6.448,1.729,1.729,0,0,1,.277.658,5.158,5.158,0,0,1,.049.866v3.232h-.891v-3.2a2.4,2.4,0,0,0-.1-.814.857.857,0,0,0-.368-.43,1.176,1.176,0,0,0-.621-.161,1.445,1.445,0,0,0-.982.361,1.784,1.784,0,0,0-.413,1.371v2.871Z" transform="translate(-422.879 -352.423)" fill="#3a4246"/>
      <path id="Path_17" data-name="Path 17" d="M486.609,357.051a2.71,2.71,0,0,1,.812-2.163,2.449,2.449,0,0,1,1.654-.584,2.358,2.358,0,0,1,1.772.71,2.7,2.7,0,0,1,.688,1.963,3.476,3.476,0,0,1-.3,1.6,2.157,2.157,0,0,1-.886.9,2.581,2.581,0,0,1-1.27.322,2.364,2.364,0,0,1-1.784-.708A2.828,2.828,0,0,1,486.609,357.051Zm.916,0a2.241,2.241,0,0,0,.441,1.512,1.467,1.467,0,0,0,2.212,0,2.294,2.294,0,0,0,.441-1.539,2.172,2.172,0,0,0-.443-1.477,1.469,1.469,0,0,0-2.21,0A2.23,2.23,0,0,0,487.525,357.051Z" transform="translate(-429.997 -352.423)" fill="#3a4246"/>
      <path id="Path_18" data-name="Path 18" d="M500.8,357.292v-7.256h.891v7.256Z" transform="translate(-437.933 -350.036)" fill="#3a4246"/>
      <path id="Path_19" data-name="Path 19" d="M505.891,357.051a2.71,2.71,0,0,1,.811-2.163,2.449,2.449,0,0,1,1.654-.584,2.358,2.358,0,0,1,1.772.71,2.7,2.7,0,0,1,.688,1.963,3.476,3.476,0,0,1-.3,1.6,2.157,2.157,0,0,1-.886.9,2.581,2.581,0,0,1-1.27.322,2.363,2.363,0,0,1-1.784-.708A2.827,2.827,0,0,1,505.891,357.051Zm.916,0a2.239,2.239,0,0,0,.441,1.512,1.467,1.467,0,0,0,2.212,0,2.294,2.294,0,0,0,.441-1.539,2.172,2.172,0,0,0-.443-1.477,1.468,1.468,0,0,0-2.209,0A2.229,2.229,0,0,0,506.807,357.051Z" transform="translate(-440.781 -352.423)" fill="#3a4246"/>
      <path id="Path_20" data-name="Path 20" d="M519.529,360.115l.866.129a.825.825,0,0,0,.3.584,1.482,1.482,0,0,0,.906.248,1.589,1.589,0,0,0,.955-.248,1.219,1.219,0,0,0,.455-.693,5.7,5.7,0,0,0,.064-1.143,1.833,1.833,0,0,1-1.455.688,1.985,1.985,0,0,1-1.678-.782,3.014,3.014,0,0,1-.594-1.876,3.49,3.49,0,0,1,.272-1.388,2.171,2.171,0,0,1,.789-.982,2.132,2.132,0,0,1,1.215-.346,1.885,1.885,0,0,1,1.534.752v-.634h.821v4.544a4.3,4.3,0,0,1-.25,1.74,1.826,1.826,0,0,1-.792.809,2.743,2.743,0,0,1-1.334.3,2.515,2.515,0,0,1-1.52-.423A1.432,1.432,0,0,1,519.529,360.115Zm.737-3.158a2.278,2.278,0,0,0,.411,1.51,1.356,1.356,0,0,0,2.059,0,2.206,2.206,0,0,0,.416-1.482,2.155,2.155,0,0,0-.428-1.455,1.325,1.325,0,0,0-1.032-.49,1.287,1.287,0,0,0-1.01.483A2.143,2.143,0,0,0,520.266,356.957Z" transform="translate(-448.309 -352.423)" fill="#3a4246"/>
      <path id="Path_21" data-name="Path 21" d="M532.927,361.854l-.1-.837a2,2,0,0,0,.509.079.971.971,0,0,0,.475-.1.8.8,0,0,0,.292-.277,4.32,4.32,0,0,0,.273-.663c.017-.05.042-.122.079-.218l-1.995-5.266h.961l1.094,3.044q.212.578.381,1.217a12.057,12.057,0,0,1,.367-1.2l1.123-3.064h.891l-2,5.346a10.556,10.556,0,0,1-.5,1.193,1.764,1.764,0,0,1-.544.646,1.281,1.281,0,0,1-.732.205A1.787,1.787,0,0,1,532.927,361.854Z" transform="translate(-455.641 -352.573)" fill="#3a4246"/>
      <path id="Path_22" data-name="Path 22" d="M555.962,357.754l.876.114a2.27,2.27,0,0,1-.735,1.418,2.142,2.142,0,0,1-1.453.513,2.256,2.256,0,0,1-1.735-.706,2.861,2.861,0,0,1-.656-2.022,3.668,3.668,0,0,1,.282-1.49,1.968,1.968,0,0,1,.859-.958,2.547,2.547,0,0,1,1.255-.319,2.182,2.182,0,0,1,1.4.433,1.985,1.985,0,0,1,.7,1.23l-.866.134a1.437,1.437,0,0,0-.438-.8,1.136,1.136,0,0,0-.76-.267,1.384,1.384,0,0,0-1.094.483,2.291,2.291,0,0,0-.42,1.527,2.37,2.37,0,0,0,.406,1.539,1.32,1.32,0,0,0,1.059.48,1.246,1.246,0,0,0,.876-.322A1.59,1.59,0,0,0,555.962,357.754Z" transform="translate(-466.713 -352.423)" fill="#3a4246"/>
      <path id="Path_23" data-name="Path 23" d="M564.314,357.051a2.709,2.709,0,0,1,.812-2.163,2.447,2.447,0,0,1,1.653-.584,2.359,2.359,0,0,1,1.772.71,2.7,2.7,0,0,1,.688,1.963,3.476,3.476,0,0,1-.3,1.6,2.152,2.152,0,0,1-.886.9,2.58,2.58,0,0,1-1.27.322,2.366,2.366,0,0,1-1.785-.708A2.828,2.828,0,0,1,564.314,357.051Zm.916,0a2.238,2.238,0,0,0,.44,1.512,1.467,1.467,0,0,0,2.213,0,2.294,2.294,0,0,0,.44-1.539,2.172,2.172,0,0,0-.443-1.477,1.469,1.469,0,0,0-2.21,0A2.228,2.228,0,0,0,565.23,357.051Z" transform="translate(-473.455 -352.423)" fill="#3a4246"/>
      <path id="Path_24" data-name="Path 24" d="M578.525,359.679v-5.257h.8v.8a2.177,2.177,0,0,1,.566-.737.989.989,0,0,1,.572-.178,1.738,1.738,0,0,1,.916.287l-.307.826a1.281,1.281,0,0,0-.654-.193.846.846,0,0,0-.525.175.951.951,0,0,0-.331.488,3.467,3.467,0,0,0-.149,1.04v2.752Z" transform="translate(-481.402 -352.423)" fill="#3a4246"/>
      <path id="Path_25" data-name="Path 25" d="M586.9,361.693v-7.271h.812v.683a2,2,0,0,1,.649-.6,1.776,1.776,0,0,1,.876-.2,2.078,2.078,0,0,1,1.188.346,2.124,2.124,0,0,1,.777.978,3.561,3.561,0,0,1,.263,1.383,3.521,3.521,0,0,1-.289,1.453,2.188,2.188,0,0,1-.842.99,2.158,2.158,0,0,1-1.16.344,1.68,1.68,0,0,1-.8-.188,1.823,1.823,0,0,1-.581-.475v2.558Zm.807-4.613a2.288,2.288,0,0,0,.41,1.5,1.26,1.26,0,0,0,1,.485,1.286,1.286,0,0,0,1.017-.5,2.386,2.386,0,0,0,.424-1.557,2.324,2.324,0,0,0-.413-1.505,1.246,1.246,0,0,0-.987-.5,1.281,1.281,0,0,0-1.007.532A2.375,2.375,0,0,0,587.7,357.081Z" transform="translate(-486.085 -352.423)" fill="#3a4246"/>
      <path id="Path_26" data-name="Path 26" d="M599.627,357.051a2.707,2.707,0,0,1,.812-2.163,2.445,2.445,0,0,1,1.653-.584,2.36,2.36,0,0,1,1.772.71,2.705,2.705,0,0,1,.688,1.963,3.474,3.474,0,0,1-.3,1.6,2.152,2.152,0,0,1-.885.9,2.577,2.577,0,0,1-1.27.322,2.365,2.365,0,0,1-1.785-.708A2.829,2.829,0,0,1,599.627,357.051Zm.915,0a2.241,2.241,0,0,0,.44,1.512,1.468,1.468,0,0,0,2.213,0,2.291,2.291,0,0,0,.44-1.539,2.172,2.172,0,0,0-.443-1.477,1.47,1.47,0,0,0-2.211,0A2.231,2.231,0,0,0,600.542,357.051Z" transform="translate(-493.204 -352.423)" fill="#3a4246"/>
      <path id="Path_27" data-name="Path 27" d="M613.838,359.679v-5.257h.8v.8a2.181,2.181,0,0,1,.566-.737.99.99,0,0,1,.572-.178,1.737,1.737,0,0,1,.916.287l-.307.826a1.278,1.278,0,0,0-.653-.193.848.848,0,0,0-.525.175.956.956,0,0,0-.331.488,3.481,3.481,0,0,0-.149,1.04v2.752Z" transform="translate(-501.152 -352.423)" fill="#3a4246"/>
      <path id="Path_28" data-name="Path 28" d="M625.257,359.031a3.324,3.324,0,0,1-.953.594,2.749,2.749,0,0,1-.982.173,1.914,1.914,0,0,1-1.332-.423,1.4,1.4,0,0,1-.465-1.082,1.465,1.465,0,0,1,.636-1.217,2.16,2.16,0,0,1,.64-.292,6.426,6.426,0,0,1,.792-.134,8.165,8.165,0,0,0,1.589-.307c0-.122.005-.2.005-.233a.983.983,0,0,0-.253-.767,1.49,1.49,0,0,0-1.015-.3,1.575,1.575,0,0,0-.928.22,1.343,1.343,0,0,0-.443.78l-.871-.119a2.162,2.162,0,0,1,.391-.9,1.714,1.714,0,0,1,.787-.53,3.507,3.507,0,0,1,1.193-.186,3.162,3.162,0,0,1,1.094.158,1.442,1.442,0,0,1,.619.4,1.417,1.417,0,0,1,.277.606,5.105,5.105,0,0,1,.044.822v1.188a12.514,12.514,0,0,0,.057,1.572,1.988,1.988,0,0,0,.225.631h-.931A1.907,1.907,0,0,1,625.257,359.031Zm-.074-1.99a6.534,6.534,0,0,1-1.455.336,3.355,3.355,0,0,0-.777.178.773.773,0,0,0-.352.29.788.788,0,0,0,.146,1.017,1.16,1.16,0,0,0,.789.238,1.836,1.836,0,0,0,.916-.225,1.386,1.386,0,0,0,.589-.616,2.147,2.147,0,0,0,.144-.891Z" transform="translate(-505.451 -352.423)" fill="#3a4246"/>
      <path id="Path_29" data-name="Path 29" d="M637.015,356.7l.128.788a3.3,3.3,0,0,1-.673.079,1.525,1.525,0,0,1-.752-.153.862.862,0,0,1-.376-.4,3.193,3.193,0,0,1-.109-1.052v-3.024h-.653v-.693h.653v-1.3l.886-.535v1.837h.9v.693h-.9v3.073a1.522,1.522,0,0,0,.048.49.369.369,0,0,0,.153.174.593.593,0,0,0,.3.064A2.879,2.879,0,0,0,637.015,356.7Z" transform="translate(-512.752 -350.243)" fill="#3a4246"/>
      <path id="Path_30" data-name="Path 30" d="M642.783,351.061v-1.025h.891v1.025Zm0,6.232v-5.257h.891v5.257Z" transform="translate(-517.34 -350.036)" fill="#3a4246"/>
      <path id="Path_31" data-name="Path 31" d="M647.818,357.051a2.71,2.71,0,0,1,.812-2.163,2.449,2.449,0,0,1,1.654-.584,2.358,2.358,0,0,1,1.772.71,2.7,2.7,0,0,1,.688,1.963,3.475,3.475,0,0,1-.3,1.6,2.157,2.157,0,0,1-.886.9,2.581,2.581,0,0,1-1.27.322,2.365,2.365,0,0,1-1.785-.708A2.829,2.829,0,0,1,647.818,357.051Zm.916,0a2.239,2.239,0,0,0,.441,1.512,1.467,1.467,0,0,0,2.213,0,2.293,2.293,0,0,0,.441-1.539,2.172,2.172,0,0,0-.443-1.477,1.469,1.469,0,0,0-2.21,0A2.229,2.229,0,0,0,648.734,357.051Z" transform="translate(-520.156 -352.423)" fill="#3a4246"/>
      <path id="Path_32" data-name="Path 32" d="M662.053,359.679v-5.257h.8v.748a1.889,1.889,0,0,1,1.673-.866,2.2,2.2,0,0,1,.874.171,1.375,1.375,0,0,1,.6.448,1.729,1.729,0,0,1,.277.658,5.161,5.161,0,0,1,.049.866v3.232h-.891v-3.2a2.4,2.4,0,0,0-.1-.814.856.856,0,0,0-.369-.43,1.173,1.173,0,0,0-.621-.161,1.445,1.445,0,0,0-.982.361,1.784,1.784,0,0,0-.413,1.371v2.871Z" transform="translate(-528.117 -352.423)" fill="#3a4246"/>
    </g>
  </g>
</svg>
