{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/template.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@nebular/theme\";\nfunction TemplateViewerComponent_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAddTemplate());\n    });\n    i0.ɵɵelement(1, \"i\", 14);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_div_7_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_7_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵelement(1, \"i\", 22);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"input\", 17);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_7_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchKeyword, $event) || (ctx_r1.searchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function TemplateViewerComponent_div_7_Template_input_input_2_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    })(\"keyup.enter\", function TemplateViewerComponent_div_7_Template_input_keyup_enter_2_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 18)(4, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_7_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    });\n    i0.ɵɵelement(5, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, TemplateViewerComponent_div_7_button_6_Template, 2, 0, \"button\", 21);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchKeyword);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n  }\n}\nfunction TemplateViewerComponent_div_8_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵelement(1, \"i\", 42);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u66AB\\u7121\\u53EF\\u9078\\u9805\\u76EE\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_8_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"label\", 44)(2, \"input\", 45);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_8_div_26_Template_input_ngModelChange_2_listener($event) {\n      const item_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r7.selected, $event) || (item_r7.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 46)(4, \"div\", 47);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 48);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"item_\", i_r8, \"\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r7.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r7.CRequirement || item_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CGroupName || item_r7.description);\n  }\n}\nfunction TemplateViewerComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"div\", 25)(3, \"div\", 26);\n    i0.ɵɵelement(4, \"i\", 27);\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"\\u65B0\\u589E\\u6A21\\u677F\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"form\", 28);\n    i0.ɵɵlistener(\"ngSubmit\", function TemplateViewerComponent_div_8_Template_form_ngSubmit_7_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveNewTemplate());\n    });\n    i0.ɵɵelementStart(8, \"div\", 29)(9, \"div\", 16)(10, \"label\", 30);\n    i0.ɵɵtext(11, \" \\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementStart(12, \"span\", 31);\n    i0.ɵɵtext(13, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"input\", 32);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_8_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newTemplate.name, $event) || (ctx_r1.newTemplate.name = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 16)(16, \"label\", 30);\n    i0.ɵɵtext(17, \"\\u6A21\\u677F\\u63CF\\u8FF0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 33);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_8_Template_input_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newTemplate.description, $event) || (ctx_r1.newTemplate.description = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 34)(20, \"label\", 30);\n    i0.ɵɵtext(21, \" \\u9078\\u64C7\\u8981\\u52A0\\u5165\\u6A21\\u677F\\u7684\\u9805\\u76EE \");\n    i0.ɵɵelementStart(22, \"span\", 31);\n    i0.ɵɵtext(23, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 35);\n    i0.ɵɵtemplate(25, TemplateViewerComponent_div_8_div_25_Template, 4, 0, \"div\", 36)(26, TemplateViewerComponent_div_8_div_26_Template, 8, 5, \"div\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 38)(28, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_8_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.cancelAddTemplate());\n    });\n    i0.ɵɵtext(29, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 40);\n    i0.ɵɵtext(31, \" \\u5132\\u5B58\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(14);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newTemplate.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newTemplate.description);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.availableData.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.availableData);\n  }\n}\nfunction TemplateViewerComponent_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"small\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u627E\\u5230 \", ctx_r1.filteredTemplates.length, \" \\u500B\\u7B26\\u5408\\u300C\", ctx_r1.searchKeyword, \"\\u300D\\u7684\\u6A21\\u677F \");\n  }\n}\nfunction TemplateViewerComponent_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"small\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\" \\u986F\\u793A\\u7B2C \", (ctx_r1.templatePagination.currentPage - 1) * ctx_r1.templatePagination.pageSize + 1, \" - \", ctx_r1.Math.min(ctx_r1.templatePagination.currentPage * ctx_r1.templatePagination.pageSize, ctx_r1.templatePagination.totalItems), \" \\u9805\\uFF0C \\u5171 \", ctx_r1.templatePagination.totalItems, \" \\u9805\\u6A21\\u677F \");\n  }\n}\nfunction TemplateViewerComponent_div_9_tr_14_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_9_tr_14_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const tpl_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(tpl_r10.TemplateID && ctx_r1.onDeleteTemplate(tpl_r10.TemplateID));\n    });\n    i0.ɵɵelement(1, \"i\", 69);\n    i0.ɵɵtext(2, \" \\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_div_9_tr_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\")(5, \"span\", 62);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\", 10)(8, \"div\", 64)(9, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_9_tr_14_Template_button_click_9_listener() {\n      const tpl_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSelectTemplate(tpl_r10));\n    });\n    i0.ɵɵelement(10, \"i\", 66);\n    i0.ɵɵtext(11, \" \\u67E5\\u770B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, TemplateViewerComponent_div_9_tr_14_button_12_Template, 3, 0, \"button\", 67);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const tpl_r10 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(tpl_r10.TemplateName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(tpl_r10.Description || \"\\u7121\\u63CF\\u8FF0\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", tpl_r10.TemplateID);\n  }\n}\nfunction TemplateViewerComponent_div_9_tr_15_small_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"small\", 62);\n    i0.ɵɵtext(1, \" \\u8ACB\\u5617\\u8A66\\u5176\\u4ED6\\u95DC\\u9375\\u5B57\\u6216 \");\n    i0.ɵɵelementStart(2, \"a\", 75);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_9_tr_15_small_6_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵtext(3, \"\\u6E05\\u9664\\u641C\\u5C0B\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_9_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 70)(2, \"div\", 71);\n    i0.ɵɵelement(3, \"i\", 72);\n    i0.ɵɵelementStart(4, \"p\", 73);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, TemplateViewerComponent_div_9_tr_15_small_6_Template, 4, 0, \"small\", 74);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.searchKeyword ? \"\\u627E\\u4E0D\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u6A21\\u677F\" : \"\\u66AB\\u7121\\u6A21\\u677F\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n  }\n}\nfunction TemplateViewerComponent_div_9_div_16_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 79)(1, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_9_div_16_li_6_Template_button_click_1_listener() {\n      const page_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(page_r15));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r15 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r15 === ctx_r1.templatePagination.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r15);\n  }\n}\nfunction TemplateViewerComponent_div_9_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"nav\", 77)(2, \"ul\", 78)(3, \"li\", 79)(4, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_9_div_16_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(ctx_r1.templatePagination.currentPage - 1));\n    });\n    i0.ɵɵelement(5, \"i\", 81);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, TemplateViewerComponent_div_9_div_16_li_6_Template, 3, 3, \"li\", 82);\n    i0.ɵɵelementStart(7, \"li\", 79)(8, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_9_div_16_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(ctx_r1.templatePagination.currentPage + 1));\n    });\n    i0.ɵɵelement(9, \"i\", 83);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getTemplatePageNumbers());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n  }\n}\nfunction TemplateViewerComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_9_div_1_Template, 3, 2, \"div\", 50)(2, TemplateViewerComponent_div_9_div_2_Template, 3, 3, \"div\", 51);\n    i0.ɵɵelementStart(3, \"div\", 52)(4, \"table\", 53)(5, \"thead\", 54)(6, \"tr\")(7, \"th\", 55);\n    i0.ɵɵtext(8, \"\\u6A21\\u677F\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\", 56);\n    i0.ɵɵtext(10, \"\\u63CF\\u8FF0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 57);\n    i0.ɵɵtext(12, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"tbody\");\n    i0.ɵɵtemplate(14, TemplateViewerComponent_div_9_tr_14_Template, 13, 3, \"tr\", 58)(15, TemplateViewerComponent_div_9_tr_15_Template, 7, 2, \"tr\", 59);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(16, TemplateViewerComponent_div_9_div_16_Template, 10, 7, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.templatePagination.totalItems > 0);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedTemplates)(\"ngForTrackBy\", ctx_r1.trackByTemplateId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.paginatedTemplates || ctx_r1.paginatedTemplates.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.templatePagination.totalPages > 1);\n  }\n}\nfunction TemplateViewerComponent_div_10_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96)(1, \"strong\");\n    i0.ɵɵtext(2, \"\\u63CF\\u8FF0\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 62);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedTemplate.Description);\n  }\n}\nfunction TemplateViewerComponent_div_10_small_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \\u7B2C \", ctx_r1.detailPagination.currentPage, \" / \", ctx_r1.detailPagination.totalPages, \" \\u9801 \");\n  }\n}\nfunction TemplateViewerComponent_div_10_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99)(1, \"div\", 100)(2, \"span\", 101);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 102)(5, \"div\", 103)(6, \"strong\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 104);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const detail_r17 = ctx.$implicit;\n    const i_r18 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((ctx_r1.detailPagination.currentPage - 1) * ctx_r1.detailPagination.pageSize + i_r18 + 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", detail_r17.FieldName, \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", detail_r17.FieldValue, \" \");\n  }\n}\nfunction TemplateViewerComponent_div_10_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_10_div_16_div_1_Template, 10, 3, \"div\", 98);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedDetails);\n  }\n}\nfunction TemplateViewerComponent_div_10_div_17_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 79)(1, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_10_div_17_li_6_Template_button_click_1_listener() {\n      const page_r21 = i0.ɵɵrestoreView(_r20).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(page_r21));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r21 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r21 === ctx_r1.detailPagination.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r21);\n  }\n}\nfunction TemplateViewerComponent_div_10_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 105)(1, \"nav\", 106)(2, \"ul\", 78)(3, \"li\", 79)(4, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_10_div_17_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(ctx_r1.detailPagination.currentPage - 1));\n    });\n    i0.ɵɵelement(5, \"i\", 81);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, TemplateViewerComponent_div_10_div_17_li_6_Template, 3, 3, \"li\", 82);\n    i0.ɵɵelementStart(7, \"li\", 79)(8, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_10_div_17_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(ctx_r1.detailPagination.currentPage + 1));\n    });\n    i0.ɵɵelement(9, \"i\", 83);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.detailPagination.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.detailPagination.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getDetailPageNumbers());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.detailPagination.currentPage === ctx_r1.detailPagination.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.detailPagination.currentPage === ctx_r1.detailPagination.totalPages);\n  }\n}\nfunction TemplateViewerComponent_div_10_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵelement(1, \"i\", 108);\n    i0.ɵɵelementStart(2, \"p\", 73);\n    i0.ɵɵtext(3, \"\\u6B64\\u6A21\\u677F\\u66AB\\u7121\\u5167\\u5BB9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"div\", 86)(2, \"h6\", 3);\n    i0.ɵɵelement(3, \"i\", 87);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_10_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTemplateDetail());\n    });\n    i0.ɵɵelement(6, \"i\", 22);\n    i0.ɵɵtext(7, \" \\u95DC\\u9589 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 89);\n    i0.ɵɵtemplate(9, TemplateViewerComponent_div_10_div_9_Template, 5, 1, \"div\", 90);\n    i0.ɵɵelementStart(10, \"div\", 91)(11, \"div\", 92)(12, \"h6\", 3);\n    i0.ɵɵelement(13, \"i\", 93);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, TemplateViewerComponent_div_10_small_15_Template, 2, 2, \"small\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, TemplateViewerComponent_div_10_div_16_Template, 2, 1, \"div\", 94)(17, TemplateViewerComponent_div_10_div_17_Template, 10, 7, \"div\", 95)(18, TemplateViewerComponent_div_10_ng_template_18_Template, 4, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const noDetails_r22 = i0.ɵɵreference(19);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \\u6A21\\u677F\\u7D30\\u7BC0\\uFF1A\", ctx_r1.selectedTemplate.TemplateName, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTemplate.Description);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \\u6A21\\u677F\\u5167\\u5BB9 (\", ctx_r1.currentTemplateDetails.length, \" \\u9805) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailPagination.totalPages > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentTemplateDetails.length > 0)(\"ngIfElse\", noDetails_r22);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailPagination.totalPages > 1);\n  }\n}\nexport class TemplateViewerComponent {\n  constructor(templateService) {\n    this.templateService = templateService;\n    this.availableData = []; // 父元件傳入的可選資料 (包含關聯主檔ID和名稱)\n    this.templateType = 1; // 模板類型，1=客變需求，從父元件傳入不可異動\n    this.showOnlyAddForm = false; // 是否只顯示新增模板表單\n    this.selectTemplate = new EventEmitter();\n    this.close = new EventEmitter(); // 關閉事件\n    // 公開Math對象供模板使用\n    this.Math = Math;\n    // 內部管理的模板資料\n    this.templates = [];\n    this.templateDetails = [];\n    this.selectedTemplate = null;\n    // 查詢功能\n    this.searchKeyword = '';\n    this.filteredTemplates = [];\n    // 分頁功能 - 模板列表\n    this.templatePagination = {\n      currentPage: 1,\n      pageSize: 10,\n      totalItems: 0,\n      totalPages: 0\n    };\n    this.paginatedTemplates = [];\n    // 分頁功能 - 模板詳情\n    this.detailPagination = {\n      currentPage: 1,\n      pageSize: 5,\n      totalItems: 0,\n      totalPages: 0\n    };\n    this.paginatedDetails = [];\n    // 新增模板表單\n    this.showAddForm = false;\n    this.newTemplate = {\n      name: '',\n      description: '',\n      selectedItems: []\n    };\n  }\n  ngOnInit() {\n    this.loadTemplates();\n    this.updateFilteredTemplates();\n    // 如果只顯示新增表單，自動打開新增模板表單\n    if (this.showOnlyAddForm) {\n      this.onAddTemplate();\n    }\n  }\n  ngOnChanges() {\n    this.updateFilteredTemplates();\n  }\n  // 載入模板列表 - 使用真實的 API 調用\n  loadTemplates() {\n    // 準備 API 請求參數\n    const getTemplateListArgs = {\n      CTemplateType: this.templateType,\n      // 1=客變需求\n      PageIndex: 1,\n      // 暫時載入第一頁\n      PageSize: 100,\n      // 暫時載入較多資料\n      CTemplateName: null // 不篩選名稱\n    };\n    console.log('GetTemplateList API 調用:', getTemplateListArgs);\n    // 調用 GetTemplateList API\n    this.templateService.apiTemplateGetTemplateListPost$Json({\n      body: getTemplateListArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // API 調用成功\n          console.log('模板列表載入成功:', response);\n          // 轉換 API 回應為內部格式\n          this.templates = response.Entries.map(item => ({\n            TemplateID: item.CTemplateId,\n            TemplateName: item.CTemplateName || '',\n            Description: `建立時間: ${item.CCreateDt ? new Date(item.CCreateDt).toLocaleDateString() : '未知'}`\n          }));\n          // 更新過濾列表\n          this.updateFilteredTemplates();\n          // TODO: 如果需要載入模板詳情，可以在這裡調用 GetTemplateById\n          this.templateDetails = [];\n        } else {\n          // API 返回錯誤\n          console.error('模板列表載入失敗:', response.Message);\n          this.templates = [];\n          this.templateDetails = [];\n          this.updateFilteredTemplates();\n        }\n      },\n      error: error => {\n        // HTTP 請求錯誤\n        console.error('GetTemplateList API 調用失敗:', error);\n        this.templates = [];\n        this.templateDetails = [];\n        this.updateFilteredTemplates();\n      }\n    });\n  }\n  // 更新過濾後的模板列表\n  updateFilteredTemplates() {\n    if (!this.searchKeyword.trim()) {\n      this.filteredTemplates = [...this.templates];\n    } else {\n      const keyword = this.searchKeyword.toLowerCase();\n      this.filteredTemplates = this.templates.filter(template => template.TemplateName.toLowerCase().includes(keyword) || template.Description && template.Description.toLowerCase().includes(keyword));\n    }\n    this.updateTemplatePagination();\n  }\n  // 更新模板分頁\n  updateTemplatePagination() {\n    this.templatePagination.totalItems = this.filteredTemplates.length;\n    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);\n    // 確保當前頁面不超過總頁數\n    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {\n      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);\n    }\n    this.updatePaginatedTemplates();\n  }\n  // 更新分頁後的模板列表\n  updatePaginatedTemplates() {\n    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;\n    const endIndex = startIndex + this.templatePagination.pageSize;\n    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);\n  }\n  // 模板分頁導航\n  goToTemplatePage(page) {\n    if (page >= 1 && page <= this.templatePagination.totalPages) {\n      this.templatePagination.currentPage = page;\n      this.updatePaginatedTemplates();\n    }\n  }\n  // 獲取模板分頁頁碼數組\n  getTemplatePageNumbers() {\n    const pages = [];\n    const totalPages = this.templatePagination.totalPages;\n    const currentPage = this.templatePagination.currentPage;\n    // 顯示當前頁面前後各2頁\n    const startPage = Math.max(1, currentPage - 2);\n    const endPage = Math.min(totalPages, currentPage + 2);\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 搜尋模板\n  onSearch() {\n    this.updateFilteredTemplates();\n  }\n  // 清除搜尋\n  clearSearch() {\n    this.searchKeyword = '';\n    this.updateFilteredTemplates();\n  }\n  // 顯示新增模板表單\n  onAddTemplate() {\n    this.showAddForm = true;\n    this.newTemplate = {\n      name: '',\n      description: '',\n      selectedItems: []\n    };\n    // 如果不是只顯示新增表單模式，才重置選擇狀態\n    // 在只顯示新增表單模式下，保持父元件傳入的選中狀態\n    if (!this.showOnlyAddForm) {\n      this.availableData.forEach(item => item.selected = false);\n    }\n  }\n  // 取消新增模板\n  cancelAddTemplate() {\n    this.showAddForm = false;\n    this.newTemplate = {\n      name: '',\n      description: '',\n      selectedItems: []\n    };\n  }\n  // 儲存新模板\n  saveNewTemplate() {\n    if (!this.newTemplate.name.trim()) {\n      alert('請輸入模板名稱');\n      return;\n    }\n    const selectedItems = this.availableData.filter(item => item.selected);\n    if (selectedItems.length === 0) {\n      alert('請至少選擇一個項目');\n      return;\n    }\n    // TODO: 替換為實際的API調用\n    this.createTemplate(this.newTemplate.name, this.newTemplate.description, selectedItems);\n  }\n  // 創建模板 - 使用真實的 API 調用\n  createTemplate(name, description, selectedItems) {\n    // 準備 API 請求資料\n    const saveTemplateArgs = {\n      CTemplateId: null,\n      // 新增時為 null\n      CTemplateName: name.trim(),\n      CTemplateType: this.templateType,\n      // 1=客變需求\n      CStatus: 1,\n      // 啟用狀態\n      Details: selectedItems.map(item => ({\n        CTemplateDetailId: null,\n        // 新增時為 null\n        CReleateId: this.getRefId(item),\n        // 關聯主檔ID\n        CReleateName: this.getFieldName(item) // 關聯名稱\n      }))\n    };\n    console.log('SaveTemplate API 調用:', saveTemplateArgs);\n    // 調用 SaveTemplate API\n    this.templateService.apiTemplateSaveTemplatePost$Json({\n      body: saveTemplateArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0) {\n          // API 調用成功\n          console.log('模板創建成功:', response);\n          // 重新載入模板列表\n          this.loadTemplates();\n          // 關閉表單\n          this.showAddForm = false;\n          alert(`模板 \"${name}\" 已成功創建！包含 ${selectedItems.length} 個項目`);\n        } else {\n          // API 返回錯誤\n          console.error('模板創建失敗:', response.Message);\n          alert(`模板創建失敗：${response.Message || '未知錯誤'}`);\n        }\n      },\n      error: error => {\n        // HTTP 請求錯誤\n        console.error('SaveTemplate API 調用失敗:', error);\n        alert('模板創建失敗，請檢查網路連線或聯繫系統管理員');\n      }\n    });\n  }\n  // 獲取關聯主檔ID的輔助方法\n  getRefId(item) {\n    return item.CRequirementID || item.ID || item.id || 0;\n  }\n  // 獲取欄位名稱的輔助方法\n  getFieldName(_item) {\n    // 根據模板類型決定欄位名稱\n    switch (this.templateType) {\n      case 1:\n        // 客變需求\n        return 'CRequirement';\n      default:\n        return 'name';\n    }\n  }\n  // 獲取欄位值的輔助方法\n  getFieldValue(item) {\n    return item.CRequirement || item.name || item.title || '';\n  }\n  // 查看模板\n  onSelectTemplate(template) {\n    this.selectedTemplate = template;\n    this.selectTemplate.emit(template);\n    this.updateDetailPagination();\n  }\n  // 更新詳情分頁\n  updateDetailPagination() {\n    const details = this.currentTemplateDetails;\n    this.detailPagination.totalItems = details.length;\n    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);\n    this.detailPagination.currentPage = 1; // 重置到第一頁\n    this.updatePaginatedDetails();\n  }\n  // 更新分頁後的詳情列表\n  updatePaginatedDetails() {\n    const details = this.currentTemplateDetails;\n    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;\n    const endIndex = startIndex + this.detailPagination.pageSize;\n    this.paginatedDetails = details.slice(startIndex, endIndex);\n  }\n  // 詳情分頁導航\n  goToDetailPage(page) {\n    if (page >= 1 && page <= this.detailPagination.totalPages) {\n      this.detailPagination.currentPage = page;\n      this.updatePaginatedDetails();\n    }\n  }\n  // 獲取詳情分頁頁碼數組\n  getDetailPageNumbers() {\n    const pages = [];\n    const totalPages = this.detailPagination.totalPages;\n    const currentPage = this.detailPagination.currentPage;\n    // 顯示當前頁面前後各2頁\n    const startPage = Math.max(1, currentPage - 2);\n    const endPage = Math.min(totalPages, currentPage + 2);\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 關閉模板查看器\n  onClose() {\n    this.close.emit();\n  }\n  // 刪除模板\n  onDeleteTemplate(templateID) {\n    if (confirm('確定刪除此模板？')) {\n      // TODO: 替換為實際的API調用\n      this.deleteTemplateById(templateID);\n    }\n  }\n  // TODO: 替換為實際的API調用\n  deleteTemplateById(templateID) {\n    // 模擬API調用 - 刪除模板\n    console.log('刪除模板 API 調用:', {\n      templateID: templateID,\n      templateType: this.templateType\n    });\n    // 刪除模板和相關詳情\n    this.templates = this.templates.filter(t => t.TemplateID !== templateID);\n    this.templateDetails = this.templateDetails.filter(d => d.TemplateID !== templateID);\n    this.updateFilteredTemplates();\n    // 如果當前查看的模板被刪除，關閉詳情\n    if (this.selectedTemplate?.TemplateID === templateID) {\n      this.selectedTemplate = null;\n    }\n    alert('模板已刪除');\n  }\n  /*\n   * API 設計說明：\n   *\n   * 1. 載入模板列表 API:\n   *    GET /api/Template/GetTemplateList\n   *    請求體: { CTemplateType: number, PageIndex: number, PageSize: number }\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\n   *\n   * 2. 創建模板 API:\n   *    POST /api/Template/SaveTemplate\n   *    請求體: {\n   *      CTemplateName: string,\n   *      CTemplateType: number,  // 1=客變需求\n   *      CStatus: number,\n   *      Details: [{\n   *        CReleateId: number,        // 關聯主檔ID\n   *        CReleateName: string       // 關聯名稱\n   *      }]\n   *    }\n   *\n   * 3. 刪除模板 API:\n   *    POST /api/Template/DeleteTemplate\n   *    請求體: { CTemplateId: number }\n   *\n   * 資料庫設計重點：\n   * - template 表：存放模板基本資訊 (CTemplateId, CTemplateName, CTemplateType, CStatus)\n   * - templatedetail 表：存放模板詳情 (CTemplateDetailId, CTemplateId, CReleateId, CReleateName)\n   * - CTemplateType 欄位用於區分不同模板類型 (1=客變需求)\n   * - CReleateId 存放關聯主檔的ID，CReleateName 存放關聯名稱\n   */\n  // 關閉模板詳情\n  closeTemplateDetail() {\n    this.selectedTemplate = null;\n  }\n  // 取得當前選中模板的詳情\n  get currentTemplateDetails() {\n    if (!this.selectedTemplate) {\n      return [];\n    }\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate.TemplateID);\n  }\n  // TrackBy函數用於優化ngFor性能\n  trackByTemplateId(index, template) {\n    return template.TemplateID || index;\n  }\n  static {\n    this.ɵfac = function TemplateViewerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateViewerComponent)(i0.ɵɵdirectiveInject(i1.TemplateService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateViewerComponent,\n      selectors: [[\"app-template-viewer\"]],\n      inputs: {\n        availableData: \"availableData\",\n        templateType: \"templateType\",\n        showOnlyAddForm: \"showOnlyAddForm\"\n      },\n      outputs: {\n        selectTemplate: \"selectTemplate\",\n        close: \"close\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 16,\n      vars: 6,\n      consts: [[\"noDetails\", \"\"], [2, \"width\", \"90vw\", \"max-width\", \"1200px\", \"height\", \"80vh\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [\"class\", \"btn btn-success btn-sm\", 3, \"click\", 4, \"ngIf\"], [2, \"overflow\", \"auto\"], [\"class\", \"search-container mb-3\", 4, \"ngIf\"], [\"class\", \"add-template-form mb-4\", 4, \"ngIf\"], [\"class\", \"template-list\", 4, \"ngIf\"], [\"class\", \"template-detail-modal\", 4, \"ngIf\"], [1, \"text-center\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-1\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"search-container\", \"mb-3\"], [1, \"input-group\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F\\u540D\\u7A31\\u6216\\u63CF\\u8FF0...\", 1, \"form-control\", 3, \"ngModelChange\", \"input\", \"keyup.enter\", \"ngModel\"], [1, \"input-group-append\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"class\", \"btn btn-outline-secondary\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [1, \"fas\", \"fa-times\"], [1, \"add-template-form\", \"mb-4\"], [1, \"form-container\"], [1, \"form-header\"], [1, \"form-title\"], [1, \"fas\", \"fa-plus\"], [1, \"form-content\", 3, \"ngSubmit\"], [1, \"input-row\"], [1, \"input-label\"], [1, \"required\"], [\"type\", \"text\", \"name\", \"templateName\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"required\", \"\", \"maxlength\", \"50\", 1, \"input-field\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"name\", \"templateDescription\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u63CF\\u8FF0\\uFF08\\u53EF\\u9078\\uFF09\", \"maxlength\", \"100\", 1, \"input-field\", 3, \"ngModelChange\", \"ngModel\"], [1, \"input-group\", \"full-width\"], [1, \"items-selector\"], [\"class\", \"empty-items\", 4, \"ngIf\"], [\"class\", \"item-option\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-actions\"], [\"type\", \"button\", 1, \"btn-cancel\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn-save\"], [1, \"empty-items\"], [1, \"fas\", \"fa-info-circle\"], [1, \"item-option\"], [1, \"item-label\"], [\"type\", \"checkbox\", 1, \"item-checkbox\", 3, \"ngModelChange\", \"ngModel\", \"name\"], [1, \"item-content\"], [1, \"item-title\"], [1, \"item-desc\"], [1, \"template-list\"], [\"class\", \"search-results-info mb-2\", 4, \"ngIf\"], [\"class\", \"pagination-info mb-2\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"table-hover\"], [1, \"thead-light\"], [\"width\", \"30%\"], [\"width\", \"50%\"], [\"width\", \"20%\", 1, \"text-center\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [4, \"ngIf\"], [\"class\", \"pagination-container mt-3\", 4, \"ngIf\"], [1, \"search-results-info\", \"mb-2\"], [1, \"text-muted\"], [1, \"pagination-info\", \"mb-2\"], [\"role\", \"group\", 1, \"btn-group\", \"btn-group-sm\"], [\"title\", \"\\u67E5\\u770B\\u8A73\\u60C5\", 1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [\"class\", \"btn btn-danger\", \"title\", \"\\u522A\\u9664\\u6A21\\u677F\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"\\u522A\\u9664\\u6A21\\u677F\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [\"colspan\", \"3\", 1, \"text-center\", \"py-4\"], [1, \"empty-state\"], [1, \"fas\", \"fa-folder-open\", \"fa-2x\", \"text-muted\", \"mb-2\"], [1, \"text-muted\", \"mb-0\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [\"href\", \"javascript:void(0)\", 3, \"click\"], [1, \"pagination-container\", \"mt-3\"], [\"aria-label\", \"\\u6A21\\u677F\\u5217\\u8868\\u5206\\u9801\"], [1, \"pagination\", \"pagination-sm\", \"justify-content-center\", \"mb-0\"], [1, \"page-item\"], [1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"page-link\", 3, \"click\"], [1, \"template-detail-modal\"], [1, \"template-detail-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"fas\", \"fa-file-alt\", \"mr-2\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"template-detail-content\"], [\"class\", \"template-description mb-3\", 4, \"ngIf\"], [1, \"template-items\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-2\"], [1, \"fas\", \"fa-list\", \"mr-1\"], [\"class\", \"detail-list\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"detail-pagination mt-3\", 4, \"ngIf\"], [1, \"template-description\", \"mb-3\"], [1, \"detail-list\"], [\"class\", \"detail-item d-flex align-items-center py-2 border-bottom\", 4, \"ngFor\", \"ngForOf\"], [1, \"detail-item\", \"d-flex\", \"align-items-center\", \"py-2\", \"border-bottom\"], [1, \"detail-index\"], [1, \"badge\", \"badge-light\"], [1, \"detail-content\", \"flex-grow-1\", \"ml-2\"], [1, \"detail-field\"], [1, \"detail-value\", \"text-muted\"], [1, \"detail-pagination\", \"mt-3\"], [\"aria-label\", \"\\u6A21\\u677F\\u8A73\\u60C5\\u5206\\u9801\"], [1, \"text-center\", \"py-3\"], [1, \"fas\", \"fa-inbox\", \"fa-2x\", \"text-muted\", \"mb-2\"]],\n      template: function TemplateViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\")(2, \"div\", 2)(3, \"h5\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, TemplateViewerComponent_button_5_Template, 3, 0, \"button\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"nb-card-body\", 5);\n          i0.ɵɵtemplate(7, TemplateViewerComponent_div_7_Template, 7, 2, \"div\", 6)(8, TemplateViewerComponent_div_8_Template, 32, 4, \"div\", 7)(9, TemplateViewerComponent_div_9_Template, 17, 6, \"div\", 8)(10, TemplateViewerComponent_div_10_Template, 20, 7, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-card-footer\")(12, \"div\", 10)(13, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_13_listener() {\n            return ctx.onClose();\n          });\n          i0.ɵɵelement(14, \"i\", 12);\n          i0.ɵɵtext(15, \"\\u95DC\\u9589 \");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.showOnlyAddForm ? \"\\u65B0\\u589E\\u6A21\\u677F\" : \"\\u6A21\\u677F\\u7BA1\\u7406\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.showOnlyAddForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.showOnlyAddForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showAddForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.selectedTemplate && !ctx.showOnlyAddForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTemplate && !ctx.showOnlyAddForm);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.CheckboxControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.MaxLengthValidator, i3.NgModel, i3.NgForm, NbCardModule, i4.NbCardComponent, i4.NbCardBodyComponent, i4.NbCardFooterComponent, i4.NbCardHeaderComponent, NbButtonModule],\n      styles: [\"nb-card-header[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  border: none;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\nnb-card-header[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);\\n}\\n\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border: none;\\n  padding: 0.75rem 1rem;\\n  font-size: 0.95rem;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  border-color: transparent;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n  font-style: italic;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border: none;\\n  background: #f8f9fa;\\n  color: #6c757d;\\n  padding: 0.75rem 1rem;\\n  transition: all 0.2s ease;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  color: #495057;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n}\\n\\n.search-results-info[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0;\\n  border-left: 3px solid #007bff;\\n  padding-left: 0.75rem;\\n  background: #f8f9ff;\\n  border-radius: 4px;\\n}\\n\\n.pagination-info[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0;\\n  border-left: 3px solid #28a745;\\n  padding-left: 0.75rem;\\n  background: #f8fff8;\\n  border-radius: 4px;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n.table[_ngcontent-%COMP%]   thead.thead-light[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n}\\n.table[_ngcontent-%COMP%]   thead.thead-light[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border: none;\\n  font-weight: 600;\\n  color: #495057;\\n  padding: 1rem 0.75rem;\\n  font-size: 0.9rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9ff;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 1rem 0.75rem;\\n  border-color: #f0f0f0;\\n  vertical-align: middle;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 600;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n\\n.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  padding: 0.375rem 0.75rem;\\n  font-size: 0.875rem;\\n  border-radius: 4px;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\\n  border: none;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);\\n  border: none;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n}\\n.empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  display: block;\\n  margin: 0 auto 1rem;\\n  opacity: 0.5;\\n}\\n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  margin-bottom: 0.5rem;\\n}\\n.empty-state[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  text-decoration: none;\\n}\\n.empty-state[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.template-detail-modal[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n  background: #fff;\\n  border: 2px solid #e9ecef;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n  animation: _ngcontent-%COMP%_slideDown 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.template-detail-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid #dee2e6;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  padding: 0.375rem 0.75rem;\\n}\\n\\n.template-detail-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%] {\\n  background: #f8f9ff;\\n  padding: 1rem;\\n  border-radius: 8px;\\n  border-left: 4px solid #007bff;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-items[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n  padding-bottom: 0.5rem;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-items[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:hover {\\n  background: #f8f9ff;\\n  border-radius: 6px;\\n  margin: 0 -0.5rem;\\n  padding-left: 0.5rem !important;\\n  padding-right: 0.5rem !important;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-index[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background: #e9ecef;\\n  color: #6c757d;\\n  font-weight: 500;\\n  padding: 0.375rem 0.5rem;\\n  border-radius: 50%;\\n  min-width: 2rem;\\n  text-align: center;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-field[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  margin-bottom: 0.25rem;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-field[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  line-height: 1.4;\\n  word-break: break-word;\\n}\\n\\n.add-template-form[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border: 1px solid #e8ecef;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid #e8ecef;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #495057;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-size: 0.9rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n@media (max-width: 768px) {\\n  .add-template-form[_ngcontent-%COMP%]   .input-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-group.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 0.5rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%]   .required[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n  margin-left: 0.125rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1rem;\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  font-size: 0.875rem;\\n  transition: all 0.2s ease;\\n  background: #ffffff;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #28a745;\\n  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]::placeholder {\\n  color: #9ca3af;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .items-selector[_ngcontent-%COMP%] {\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  background: #f9fafb;\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .empty-items[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  padding: 2rem;\\n  color: #6b7280;\\n  font-size: 0.875rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .empty-items[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #9ca3af;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]:hover {\\n  background: #f3f4f6;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.75rem;\\n  padding: 0.875rem 1rem;\\n  cursor: pointer;\\n  margin: 0;\\n  width: 100%;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%] {\\n  margin: 0;\\n  margin-top: 0.125rem;\\n  width: 1rem;\\n  height: 1rem;\\n  accent-color: #28a745;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 0.25rem;\\n  line-height: 1.4;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-desc[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6b7280;\\n  line-height: 1.3;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 0.75rem;\\n  margin-top: 1.5rem;\\n  padding-top: 1.5rem;\\n  border-top: 1px solid #e5e7eb;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%], \\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%] {\\n  padding: 0.625rem 1.25rem;\\n  border-radius: 8px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  border: none;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  min-width: 80px;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%] {\\n  background: #f3f4f6;\\n  color: #374151;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%]:hover {\\n  background: #e5e7eb;\\n  transform: translateY(-1px);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  color: white;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.25);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: none;\\n}\\n\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  border: 1px solid #dee2e6;\\n  color: #6c757d;\\n  padding: 0.375rem 0.75rem;\\n  font-size: 0.875rem;\\n  transition: all 0.2s ease;\\n  border-radius: 4px;\\n  margin: 0 0.125rem;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: #e9ecef;\\n  border-color: #adb5bd;\\n  color: #495057;\\n  transform: translateY(-1px);\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  border-color: #007bff;\\n  color: white;\\n  font-weight: 600;\\n  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  background-color: #fff;\\n  border-color: #dee2e6;\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  transform: none;\\n}\\n\\n.detail-pagination[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  font-size: 0.8rem;\\n  border-radius: 3px;\\n  margin: 0 0.0625rem;\\n}\\n.detail-pagination[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.detail-pagination[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  border-color: #28a745;\\n  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "i0", "ɵɵelementStart", "ɵɵlistener", "TemplateViewerComponent_button_5_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onAddTemplate", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "TemplateViewerComponent_div_7_button_6_Template_button_click_0_listener", "_r4", "clearSearch", "ɵɵtwoWayListener", "TemplateViewerComponent_div_7_Template_input_ngModelChange_2_listener", "$event", "_r3", "ɵɵtwoWayBindingSet", "searchKeyword", "TemplateViewerComponent_div_7_Template_input_input_2_listener", "onSearch", "TemplateViewerComponent_div_7_Template_input_keyup_enter_2_listener", "TemplateViewerComponent_div_7_Template_button_click_4_listener", "ɵɵtemplate", "TemplateViewerComponent_div_7_button_6_Template", "ɵɵadvance", "ɵɵtwoWayProperty", "ɵɵproperty", "TemplateViewerComponent_div_8_div_26_Template_input_ngModelChange_2_listener", "item_r7", "_r6", "$implicit", "selected", "ɵɵpropertyInterpolate1", "i_r8", "ɵɵtextInterpolate", "CRequirement", "name", "CGroupName", "description", "TemplateViewerComponent_div_8_Template_form_ngSubmit_7_listener", "_r5", "saveNewTemplate", "TemplateViewerComponent_div_8_Template_input_ngModelChange_14_listener", "newTemplate", "TemplateViewerComponent_div_8_Template_input_ngModelChange_18_listener", "TemplateViewerComponent_div_8_div_25_Template", "TemplateViewerComponent_div_8_div_26_Template", "TemplateViewerComponent_div_8_Template_button_click_28_listener", "cancelAddTemplate", "availableData", "length", "ɵɵtextInterpolate2", "filteredTemplates", "ɵɵtextInterpolate3", "templatePagination", "currentPage", "pageSize", "Math", "min", "totalItems", "TemplateViewerComponent_div_9_tr_14_button_12_Template_button_click_0_listener", "_r11", "tpl_r10", "TemplateID", "onDeleteTemplate", "TemplateViewerComponent_div_9_tr_14_Template_button_click_9_listener", "_r9", "onSelectTemplate", "TemplateViewerComponent_div_9_tr_14_button_12_Template", "TemplateName", "Description", "TemplateViewerComponent_div_9_tr_15_small_6_Template_a_click_2_listener", "_r12", "TemplateViewerComponent_div_9_tr_15_small_6_Template", "ɵɵtextInterpolate1", "TemplateViewerComponent_div_9_div_16_li_6_Template_button_click_1_listener", "page_r15", "_r14", "goToTemplatePage", "ɵɵclassProp", "TemplateViewerComponent_div_9_div_16_Template_button_click_4_listener", "_r13", "TemplateViewerComponent_div_9_div_16_li_6_Template", "TemplateViewerComponent_div_9_div_16_Template_button_click_8_listener", "getTemplatePageNumbers", "totalPages", "TemplateViewerComponent_div_9_div_1_Template", "TemplateViewerComponent_div_9_div_2_Template", "TemplateViewerComponent_div_9_tr_14_Template", "TemplateViewerComponent_div_9_tr_15_Template", "TemplateViewerComponent_div_9_div_16_Template", "paginatedTemplates", "trackByTemplateId", "selectedTemplate", "detailPagination", "i_r18", "detail_r17", "FieldName", "FieldValue", "TemplateViewerComponent_div_10_div_16_div_1_Template", "paginatedDetails", "TemplateViewerComponent_div_10_div_17_li_6_Template_button_click_1_listener", "page_r21", "_r20", "goToDetailPage", "TemplateViewerComponent_div_10_div_17_Template_button_click_4_listener", "_r19", "TemplateViewerComponent_div_10_div_17_li_6_Template", "TemplateViewerComponent_div_10_div_17_Template_button_click_8_listener", "getDetailPageNumbers", "TemplateViewerComponent_div_10_Template_button_click_5_listener", "_r16", "closeTemplateDetail", "TemplateViewerComponent_div_10_div_9_Template", "TemplateViewerComponent_div_10_small_15_Template", "TemplateViewerComponent_div_10_div_16_Template", "TemplateViewerComponent_div_10_div_17_Template", "TemplateViewerComponent_div_10_ng_template_18_Template", "ɵɵtemplateRefExtractor", "currentTemplateDetails", "noDetails_r22", "TemplateViewerComponent", "constructor", "templateService", "templateType", "showOnlyAddForm", "selectTemplate", "close", "templates", "templateDetails", "showAddForm", "selectedItems", "ngOnInit", "loadTemplates", "updateFilteredTemplates", "ngOnChanges", "getTemplateListArgs", "CTemplateType", "PageIndex", "PageSize", "CTemplateName", "console", "log", "apiTemplateGetTemplateListPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "CTemplateId", "CCreateDt", "Date", "toLocaleDateString", "error", "Message", "trim", "keyword", "toLowerCase", "filter", "template", "includes", "updateTemplatePagination", "ceil", "max", "updatePaginatedTemplates", "startIndex", "endIndex", "slice", "page", "pages", "startPage", "endPage", "i", "push", "for<PERSON>ach", "alert", "createTemplate", "saveTemplateArgs", "CStatus", "Details", "CTemplateDetailId", "CReleateId", "getRefId", "CReleateName", "getFieldName", "apiTemplateSaveTemplatePost$Json", "CRequirementID", "ID", "id", "_item", "getFieldValue", "title", "emit", "updateDetailPagination", "details", "updatePaginatedDetails", "onClose", "templateID", "confirm", "deleteTemplateById", "t", "d", "index", "ɵɵdirectiveInject", "i1", "TemplateService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "TemplateViewerComponent_Template", "rf", "ctx", "TemplateViewerComponent_button_5_Template", "TemplateViewerComponent_div_7_Template", "TemplateViewerComponent_div_8_Template", "TemplateViewerComponent_div_9_Template", "TemplateViewerComponent_div_10_Template", "TemplateViewerComponent_Template_button_click_13_listener", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "ɵNgNoValidate", "DefaultValueAccessor", "CheckboxControlValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "MaxLengthValidator", "NgModel", "NgForm", "i4", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit, OnChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { SaveTemplateArgs, SaveTemplateDetailArgs, TemplateGetListArgs, TemplateGetListResponse } from 'src/services/api/models';\r\n\r\n@Component({\r\n  selector: 'app-template-viewer',\r\n  templateUrl: './template-viewer.component.html',\r\n  styleUrls: ['./template-viewer.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule],\r\n})\r\nexport class TemplateViewerComponent implements OnInit, OnChanges {\r\n  @Input() availableData: any[] = []; // 父元件傳入的可選資料 (包含關聯主檔ID和名稱)\r\n  @Input() templateType: number = 1; // 模板類型，1=客變需求，從父元件傳入不可異動\r\n  @Input() showOnlyAddForm: boolean = false; // 是否只顯示新增模板表單\r\n  @Output() selectTemplate = new EventEmitter<Template>();\r\n  @Output() close = new EventEmitter<void>(); // 關閉事件\r\n\r\n  // 公開Math對象供模板使用\r\n  Math = Math;\r\n\r\n  // 內部管理的模板資料\r\n  templates: Template[] = [];\r\n  templateDetails: TemplateDetail[] = [];\r\n  selectedTemplate: Template | null = null;\r\n\r\n  // 查詢功能\r\n  searchKeyword = '';\r\n  filteredTemplates: Template[] = [];\r\n\r\n  // 分頁功能 - 模板列表\r\n  templatePagination = {\r\n    currentPage: 1,\r\n    pageSize: 10,\r\n    totalItems: 0,\r\n    totalPages: 0\r\n  };\r\n  paginatedTemplates: Template[] = [];\r\n\r\n  // 分頁功能 - 模板詳情\r\n  detailPagination = {\r\n    currentPage: 1,\r\n    pageSize: 5,\r\n    totalItems: 0,\r\n    totalPages: 0\r\n  };\r\n  paginatedDetails: TemplateDetail[] = [];\r\n\r\n  // 新增模板表單\r\n  showAddForm = false;\r\n  newTemplate = {\r\n    name: '',\r\n    description: '',\r\n    selectedItems: [] as any[]\r\n  };\r\n\r\n  constructor(private templateService: TemplateService) { }\r\n\r\n  ngOnInit() {\r\n    this.loadTemplates();\r\n    this.updateFilteredTemplates();\r\n\r\n    // 如果只顯示新增表單，自動打開新增模板表單\r\n    if (this.showOnlyAddForm) {\r\n      this.onAddTemplate();\r\n    }\r\n  }\r\n\r\n  ngOnChanges() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 載入模板列表 - 使用真實的 API 調用\r\n  loadTemplates() {\r\n    // 準備 API 請求參數\r\n    const getTemplateListArgs: TemplateGetListArgs = {\r\n      CTemplateType: this.templateType, // 1=客變需求\r\n      PageIndex: 1, // 暫時載入第一頁\r\n      PageSize: 100, // 暫時載入較多資料\r\n      CTemplateName: null // 不篩選名稱\r\n    };\r\n\r\n    console.log('GetTemplateList API 調用:', getTemplateListArgs);\r\n\r\n    // 調用 GetTemplateList API\r\n    this.templateService.apiTemplateGetTemplateListPost$Json({\r\n      body: getTemplateListArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          // API 調用成功\r\n          console.log('模板列表載入成功:', response);\r\n\r\n          // 轉換 API 回應為內部格式\r\n          this.templates = response.Entries.map(item => ({\r\n            TemplateID: item.CTemplateId,\r\n            TemplateName: item.CTemplateName || '',\r\n            Description: `建立時間: ${item.CCreateDt ? new Date(item.CCreateDt).toLocaleDateString() : '未知'}`\r\n          }));\r\n\r\n          // 更新過濾列表\r\n          this.updateFilteredTemplates();\r\n\r\n          // TODO: 如果需要載入模板詳情，可以在這裡調用 GetTemplateById\r\n          this.templateDetails = [];\r\n        } else {\r\n          // API 返回錯誤\r\n          console.error('模板列表載入失敗:', response.Message);\r\n          this.templates = [];\r\n          this.templateDetails = [];\r\n          this.updateFilteredTemplates();\r\n        }\r\n      },\r\n      error: (error) => {\r\n        // HTTP 請求錯誤\r\n        console.error('GetTemplateList API 調用失敗:', error);\r\n        this.templates = [];\r\n        this.templateDetails = [];\r\n        this.updateFilteredTemplates();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 更新過濾後的模板列表\r\n  updateFilteredTemplates() {\r\n    if (!this.searchKeyword.trim()) {\r\n      this.filteredTemplates = [...this.templates];\r\n    } else {\r\n      const keyword = this.searchKeyword.toLowerCase();\r\n      this.filteredTemplates = this.templates.filter(template =>\r\n        template.TemplateName.toLowerCase().includes(keyword) ||\r\n        (template.Description && template.Description.toLowerCase().includes(keyword))\r\n      );\r\n    }\r\n    this.updateTemplatePagination();\r\n  }\r\n\r\n  // 更新模板分頁\r\n  updateTemplatePagination() {\r\n    this.templatePagination.totalItems = this.filteredTemplates.length;\r\n    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);\r\n\r\n    // 確保當前頁面不超過總頁數\r\n    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {\r\n      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);\r\n    }\r\n\r\n    this.updatePaginatedTemplates();\r\n  }\r\n\r\n  // 更新分頁後的模板列表\r\n  updatePaginatedTemplates() {\r\n    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;\r\n    const endIndex = startIndex + this.templatePagination.pageSize;\r\n    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 模板分頁導航\r\n  goToTemplatePage(page: number) {\r\n    if (page >= 1 && page <= this.templatePagination.totalPages) {\r\n      this.templatePagination.currentPage = page;\r\n      this.updatePaginatedTemplates();\r\n    }\r\n  }\r\n\r\n  // 獲取模板分頁頁碼數組\r\n  getTemplatePageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const totalPages = this.templatePagination.totalPages;\r\n    const currentPage = this.templatePagination.currentPage;\r\n\r\n    // 顯示當前頁面前後各2頁\r\n    const startPage = Math.max(1, currentPage - 2);\r\n    const endPage = Math.min(totalPages, currentPage + 2);\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 搜尋模板\r\n  onSearch() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 清除搜尋\r\n  clearSearch() {\r\n    this.searchKeyword = '';\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n\r\n\r\n  // 顯示新增模板表單\r\n  onAddTemplate() {\r\n    this.showAddForm = true;\r\n    this.newTemplate = {\r\n      name: '',\r\n      description: '',\r\n      selectedItems: []\r\n    };\r\n    // 如果不是只顯示新增表單模式，才重置選擇狀態\r\n    // 在只顯示新增表單模式下，保持父元件傳入的選中狀態\r\n    if (!this.showOnlyAddForm) {\r\n      this.availableData.forEach(item => item.selected = false);\r\n    }\r\n  }\r\n\r\n  // 取消新增模板\r\n  cancelAddTemplate() {\r\n    this.showAddForm = false;\r\n    this.newTemplate = {\r\n      name: '',\r\n      description: '',\r\n      selectedItems: []\r\n    };\r\n  }\r\n\r\n  // 儲存新模板\r\n  saveNewTemplate() {\r\n    if (!this.newTemplate.name.trim()) {\r\n      alert('請輸入模板名稱');\r\n      return;\r\n    }\r\n\r\n    const selectedItems = this.availableData.filter(item => item.selected);\r\n    if (selectedItems.length === 0) {\r\n      alert('請至少選擇一個項目');\r\n      return;\r\n    }\r\n\r\n    // TODO: 替換為實際的API調用\r\n    this.createTemplate(this.newTemplate.name, this.newTemplate.description, selectedItems);\r\n  }\r\n\r\n  // 創建模板 - 使用真實的 API 調用\r\n  createTemplate(name: string, description: string, selectedItems: any[]) {\r\n    // 準備 API 請求資料\r\n    const saveTemplateArgs: SaveTemplateArgs = {\r\n      CTemplateId: null, // 新增時為 null\r\n      CTemplateName: name.trim(),\r\n      CTemplateType: this.templateType, // 1=客變需求\r\n      CStatus: 1, // 啟用狀態\r\n      Details: selectedItems.map(item => ({\r\n        CTemplateDetailId: null, // 新增時為 null\r\n        CReleateId: this.getRefId(item), // 關聯主檔ID\r\n        CReleateName: this.getFieldName(item) // 關聯名稱\r\n      } as SaveTemplateDetailArgs))\r\n    };\r\n\r\n    console.log('SaveTemplate API 調用:', saveTemplateArgs);\r\n\r\n    // 調用 SaveTemplate API\r\n    this.templateService.apiTemplateSaveTemplatePost$Json({\r\n      body: saveTemplateArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0) {\r\n          // API 調用成功\r\n          console.log('模板創建成功:', response);\r\n\r\n          // 重新載入模板列表\r\n          this.loadTemplates();\r\n\r\n          // 關閉表單\r\n          this.showAddForm = false;\r\n          alert(`模板 \"${name}\" 已成功創建！包含 ${selectedItems.length} 個項目`);\r\n        } else {\r\n          // API 返回錯誤\r\n          console.error('模板創建失敗:', response.Message);\r\n          alert(`模板創建失敗：${response.Message || '未知錯誤'}`);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        // HTTP 請求錯誤\r\n        console.error('SaveTemplate API 調用失敗:', error);\r\n        alert('模板創建失敗，請檢查網路連線或聯繫系統管理員');\r\n      }\r\n    });\r\n  }\r\n\r\n  // 獲取關聯主檔ID的輔助方法\r\n  private getRefId(item: any): number {\r\n    return item.CRequirementID || item.ID || item.id || 0;\r\n  }\r\n\r\n  // 獲取欄位名稱的輔助方法\r\n  private getFieldName(_item?: any): string {\r\n    // 根據模板類型決定欄位名稱\r\n    switch (this.templateType) {\r\n      case 1: // 客變需求\r\n        return 'CRequirement';\r\n      default:\r\n        return 'name';\r\n    }\r\n  }\r\n\r\n  // 獲取欄位值的輔助方法\r\n  private getFieldValue(item: any): string {\r\n    return item.CRequirement || item.name || item.title || '';\r\n  }\r\n\r\n  // 查看模板\r\n  onSelectTemplate(template: Template) {\r\n    this.selectedTemplate = template;\r\n    this.selectTemplate.emit(template);\r\n    this.updateDetailPagination();\r\n  }\r\n\r\n  // 更新詳情分頁\r\n  updateDetailPagination() {\r\n    const details = this.currentTemplateDetails;\r\n    this.detailPagination.totalItems = details.length;\r\n    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);\r\n    this.detailPagination.currentPage = 1; // 重置到第一頁\r\n    this.updatePaginatedDetails();\r\n  }\r\n\r\n  // 更新分頁後的詳情列表\r\n  updatePaginatedDetails() {\r\n    const details = this.currentTemplateDetails;\r\n    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;\r\n    const endIndex = startIndex + this.detailPagination.pageSize;\r\n    this.paginatedDetails = details.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 詳情分頁導航\r\n  goToDetailPage(page: number) {\r\n    if (page >= 1 && page <= this.detailPagination.totalPages) {\r\n      this.detailPagination.currentPage = page;\r\n      this.updatePaginatedDetails();\r\n    }\r\n  }\r\n\r\n  // 獲取詳情分頁頁碼數組\r\n  getDetailPageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const totalPages = this.detailPagination.totalPages;\r\n    const currentPage = this.detailPagination.currentPage;\r\n\r\n    // 顯示當前頁面前後各2頁\r\n    const startPage = Math.max(1, currentPage - 2);\r\n    const endPage = Math.min(totalPages, currentPage + 2);\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 關閉模板查看器\r\n  onClose() {\r\n    this.close.emit();\r\n  }\r\n\r\n  // 刪除模板\r\n  onDeleteTemplate(templateID: number) {\r\n    if (confirm('確定刪除此模板？')) {\r\n      // TODO: 替換為實際的API調用\r\n      this.deleteTemplateById(templateID);\r\n    }\r\n  }\r\n\r\n  // TODO: 替換為實際的API調用\r\n  deleteTemplateById(templateID: number) {\r\n    // 模擬API調用 - 刪除模板\r\n    console.log('刪除模板 API 調用:', {\r\n      templateID: templateID,\r\n      templateType: this.templateType\r\n    });\r\n\r\n    // 刪除模板和相關詳情\r\n    this.templates = this.templates.filter(t => t.TemplateID !== templateID);\r\n    this.templateDetails = this.templateDetails.filter(d => d.TemplateID !== templateID);\r\n    this.updateFilteredTemplates();\r\n\r\n    // 如果當前查看的模板被刪除，關閉詳情\r\n    if (this.selectedTemplate?.TemplateID === templateID) {\r\n      this.selectedTemplate = null;\r\n    }\r\n\r\n    alert('模板已刪除');\r\n  }\r\n\r\n  /*\r\n   * API 設計說明：\r\n   *\r\n   * 1. 載入模板列表 API:\r\n   *    GET /api/Template/GetTemplateList\r\n   *    請求體: { CTemplateType: number, PageIndex: number, PageSize: number }\r\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\r\n   *\r\n   * 2. 創建模板 API:\r\n   *    POST /api/Template/SaveTemplate\r\n   *    請求體: {\r\n   *      CTemplateName: string,\r\n   *      CTemplateType: number,  // 1=客變需求\r\n   *      CStatus: number,\r\n   *      Details: [{\r\n   *        CReleateId: number,        // 關聯主檔ID\r\n   *        CReleateName: string       // 關聯名稱\r\n   *      }]\r\n   *    }\r\n   *\r\n   * 3. 刪除模板 API:\r\n   *    POST /api/Template/DeleteTemplate\r\n   *    請求體: { CTemplateId: number }\r\n   *\r\n   * 資料庫設計重點：\r\n   * - template 表：存放模板基本資訊 (CTemplateId, CTemplateName, CTemplateType, CStatus)\r\n   * - templatedetail 表：存放模板詳情 (CTemplateDetailId, CTemplateId, CReleateId, CReleateName)\r\n   * - CTemplateType 欄位用於區分不同模板類型 (1=客變需求)\r\n   * - CReleateId 存放關聯主檔的ID，CReleateName 存放關聯名稱\r\n   */\r\n\r\n  // 關閉模板詳情\r\n  closeTemplateDetail() {\r\n    this.selectedTemplate = null;\r\n  }\r\n\r\n  // 取得當前選中模板的詳情\r\n  get currentTemplateDetails(): TemplateDetail[] {\r\n    if (!this.selectedTemplate) {\r\n      return [];\r\n    }\r\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);\r\n  }\r\n\r\n  // TrackBy函數用於優化ngFor性能\r\n  trackByTemplateId(index: number, template: Template): number {\r\n    return template.TemplateID || index;\r\n  }\r\n}\r\n\r\n// DB 對應型別\r\nexport interface Template {\r\n  TemplateID?: number;\r\n  TemplateName: string;\r\n  Description?: string;\r\n}\r\nexport interface TemplateDetail {\r\n  TemplateDetailID?: number;\r\n  TemplateID: number;\r\n  RefID: number; // 關聯主檔ID\r\n  CTemplateType: number; // 模板類型，1=客變需求\r\n  FieldName: string;\r\n  FieldValue: string;\r\n}\r\n", "<nb-card style=\"width: 90vw; max-width: 1200px; height: 80vh;\">\r\n  <nb-card-header>\r\n    <div class=\"d-flex justify-content-between align-items-center\">\r\n      <h5 class=\"mb-0\">{{ showOnlyAddForm ? '新增模板' : '模板管理' }}</h5>\r\n      <button class=\"btn btn-success btn-sm\" (click)=\"onAddTemplate()\" *ngIf=\"!showOnlyAddForm\">\r\n        <i class=\"fas fa-plus mr-1\"></i>新增模板\r\n      </button>\r\n    </div>\r\n  </nb-card-header>\r\n  <nb-card-body style=\"overflow: auto;\">\r\n    <!-- 搜尋功能 -->\r\n    <div class=\"search-container mb-3\" *ngIf=\"!showOnlyAddForm\">\r\n      <div class=\"input-group\">\r\n        <input type=\"text\" class=\"form-control\" placeholder=\"搜尋模板名稱或描述...\" [(ngModel)]=\"searchKeyword\"\r\n          (input)=\"onSearch()\" (keyup.enter)=\"onSearch()\">\r\n        <div class=\"input-group-append\">\r\n          <button class=\"btn btn-outline-secondary\" type=\"button\" (click)=\"onSearch()\">\r\n            <i class=\"fas fa-search\"></i>\r\n          </button>\r\n          <button class=\"btn btn-outline-secondary\" type=\"button\" (click)=\"clearSearch()\" *ngIf=\"searchKeyword\">\r\n            <i class=\"fas fa-times\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 新增模板表單 -->\r\n    <div *ngIf=\"showAddForm\" class=\"add-template-form mb-4\">\r\n      <div class=\"form-container\">\r\n        <div class=\"form-header\">\r\n          <div class=\"form-title\">\r\n            <i class=\"fas fa-plus\"></i>\r\n            <span>新增模板</span>\r\n          </div>\r\n        </div>\r\n\r\n        <form (ngSubmit)=\"saveNewTemplate()\" class=\"form-content\">\r\n          <div class=\"input-row\">\r\n            <div class=\"input-group\">\r\n              <label class=\"input-label\">\r\n                模板名稱 <span class=\"required\">*</span>\r\n              </label>\r\n              <input type=\"text\" class=\"input-field\" [(ngModel)]=\"newTemplate.name\" name=\"templateName\"\r\n                placeholder=\"請輸入模板名稱\" required maxlength=\"50\">\r\n            </div>\r\n            <div class=\"input-group\">\r\n              <label class=\"input-label\">模板描述</label>\r\n              <input type=\"text\" class=\"input-field\" [(ngModel)]=\"newTemplate.description\" name=\"templateDescription\"\r\n                placeholder=\"請輸入模板描述（可選）\" maxlength=\"100\">\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"input-group full-width\">\r\n            <label class=\"input-label\">\r\n              選擇要加入模板的項目 <span class=\"required\">*</span>\r\n            </label>\r\n            <div class=\"items-selector\">\r\n              <div *ngIf=\"availableData.length === 0\" class=\"empty-items\">\r\n                <i class=\"fas fa-info-circle\"></i>\r\n                <span>暫無可選項目</span>\r\n              </div>\r\n              <div *ngFor=\"let item of availableData; let i = index\" class=\"item-option\">\r\n                <label class=\"item-label\">\r\n                  <input type=\"checkbox\" class=\"item-checkbox\" [(ngModel)]=\"item.selected\" name=\"item_{{i}}\">\r\n                  <div class=\"item-content\">\r\n                    <div class=\"item-title\">{{ item.CRequirement || item.name }}</div>\r\n                    <div class=\"item-desc\">{{ item.CGroupName || item.description }}</div>\r\n                  </div>\r\n                </label>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"form-actions\">\r\n            <button type=\"button\" class=\"btn-cancel\" (click)=\"cancelAddTemplate()\">\r\n              取消\r\n            </button>\r\n            <button type=\"submit\" class=\"btn-save\">\r\n              儲存模板\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 模板列表（僅在未選擇模板細節時顯示） -->\r\n    <div class=\"template-list\" *ngIf=\"!selectedTemplate && !showOnlyAddForm\">\r\n      <!-- 搜尋結果統計 -->\r\n      <div class=\"search-results-info mb-2\" *ngIf=\"searchKeyword\">\r\n        <small class=\"text-muted\">\r\n          找到 {{ filteredTemplates.length }} 個符合「{{ searchKeyword }}」的模板\r\n        </small>\r\n      </div>\r\n\r\n      <!-- 分頁資訊 -->\r\n      <div class=\"pagination-info mb-2\" *ngIf=\"templatePagination.totalItems > 0\">\r\n        <small class=\"text-muted\">\r\n          顯示第 {{ (templatePagination.currentPage - 1) * templatePagination.pageSize + 1 }} -\r\n          {{ Math.min(templatePagination.currentPage * templatePagination.pageSize, templatePagination.totalItems) }} 項，\r\n          共 {{ templatePagination.totalItems }} 項模板\r\n        </small>\r\n      </div>\r\n\r\n      <div class=\"table-responsive\">\r\n        <table class=\"table table-striped table-hover\">\r\n          <thead class=\"thead-light\">\r\n            <tr>\r\n              <th width=\"30%\">模板名稱</th>\r\n              <th width=\"50%\">描述</th>\r\n              <th width=\"20%\" class=\"text-center\">操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let tpl of paginatedTemplates; trackBy: trackByTemplateId\">\r\n              <td>\r\n                <strong>{{ tpl.TemplateName }}</strong>\r\n              </td>\r\n              <td>\r\n                <span class=\"text-muted\">{{ tpl.Description || '無描述' }}</span>\r\n              </td>\r\n              <td class=\"text-center\">\r\n                <div class=\"btn-group btn-group-sm\" role=\"group\">\r\n                  <button class=\"btn btn-info\" (click)=\"onSelectTemplate(tpl)\" title=\"查看詳情\">\r\n                    <i class=\"fas fa-eye\"></i> 查看\r\n                  </button>\r\n                  <button class=\"btn btn-danger\" (click)=\"tpl.TemplateID && onDeleteTemplate(tpl.TemplateID)\"\r\n                    *ngIf=\"tpl.TemplateID\" title=\"刪除模板\">\r\n                    <i class=\"fas fa-trash\"></i> 刪除\r\n                  </button>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n            <tr *ngIf=\"!paginatedTemplates || paginatedTemplates.length === 0\">\r\n              <td colspan=\"3\" class=\"text-center py-4\">\r\n                <div class=\"empty-state\">\r\n                  <i class=\"fas fa-folder-open fa-2x text-muted mb-2\"></i>\r\n                  <p class=\"text-muted mb-0\">\r\n                    {{ searchKeyword ? '找不到符合條件的模板' : '暫無模板' }}\r\n                  </p>\r\n                  <small class=\"text-muted\" *ngIf=\"searchKeyword\">\r\n                    請嘗試其他關鍵字或 <a href=\"javascript:void(0)\" (click)=\"clearSearch()\">清除搜尋</a>\r\n                  </small>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      <!-- 模板列表分頁控制器 -->\r\n      <div class=\"pagination-container mt-3\" *ngIf=\"templatePagination.totalPages > 1\">\r\n        <nav aria-label=\"模板列表分頁\">\r\n          <ul class=\"pagination pagination-sm justify-content-center mb-0\">\r\n            <!-- 上一頁 -->\r\n            <li class=\"page-item\" [class.disabled]=\"templatePagination.currentPage === 1\">\r\n              <button class=\"page-link\" (click)=\"goToTemplatePage(templatePagination.currentPage - 1)\"\r\n                [disabled]=\"templatePagination.currentPage === 1\">\r\n                <i class=\"fas fa-chevron-left\"></i>\r\n              </button>\r\n            </li>\r\n\r\n            <!-- 頁碼 -->\r\n            <li class=\"page-item\" *ngFor=\"let page of getTemplatePageNumbers()\"\r\n              [class.active]=\"page === templatePagination.currentPage\">\r\n              <button class=\"page-link\" (click)=\"goToTemplatePage(page)\">{{ page }}</button>\r\n            </li>\r\n\r\n            <!-- 下一頁 -->\r\n            <li class=\"page-item\" [class.disabled]=\"templatePagination.currentPage === templatePagination.totalPages\">\r\n              <button class=\"page-link\" (click)=\"goToTemplatePage(templatePagination.currentPage + 1)\"\r\n                [disabled]=\"templatePagination.currentPage === templatePagination.totalPages\">\r\n                <i class=\"fas fa-chevron-right\"></i>\r\n              </button>\r\n            </li>\r\n          </ul>\r\n        </nav>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 查看模板細節 -->\r\n    <div *ngIf=\"selectedTemplate && !showOnlyAddForm\" class=\"template-detail-modal\">\r\n      <div class=\"template-detail-header d-flex justify-content-between align-items-center\">\r\n        <h6 class=\"mb-0\">\r\n          <i class=\"fas fa-file-alt mr-2\"></i>\r\n          模板細節：{{ selectedTemplate!.TemplateName }}\r\n        </h6>\r\n        <button class=\"btn btn-outline-secondary btn-sm\" (click)=\"closeTemplateDetail()\">\r\n          <i class=\"fas fa-times\"></i> 關閉\r\n        </button>\r\n      </div>\r\n\r\n      <div class=\"template-detail-content\">\r\n        <div *ngIf=\"selectedTemplate.Description\" class=\"template-description mb-3\">\r\n          <strong>描述：</strong>\r\n          <span class=\"text-muted\">{{ selectedTemplate.Description }}</span>\r\n        </div>\r\n\r\n        <div class=\"template-items\">\r\n          <div class=\"d-flex justify-content-between align-items-center mb-2\">\r\n            <h6 class=\"mb-0\">\r\n              <i class=\"fas fa-list mr-1\"></i>\r\n              模板內容 ({{ currentTemplateDetails.length }} 項)\r\n            </h6>\r\n            <small class=\"text-muted\" *ngIf=\"detailPagination.totalPages > 1\">\r\n              第 {{ detailPagination.currentPage }} / {{ detailPagination.totalPages }} 頁\r\n            </small>\r\n          </div>\r\n\r\n          <div *ngIf=\"currentTemplateDetails.length > 0; else noDetails\" class=\"detail-list\">\r\n            <div *ngFor=\"let detail of paginatedDetails; let i = index\"\r\n              class=\"detail-item d-flex align-items-center py-2 border-bottom\">\r\n              <div class=\"detail-index\">\r\n                <span class=\"badge badge-light\">{{ (detailPagination.currentPage - 1) * detailPagination.pageSize + i +\r\n                  1 }}</span>\r\n              </div>\r\n              <div class=\"detail-content flex-grow-1 ml-2\">\r\n                <div class=\"detail-field\">\r\n                  <strong>{{ detail.FieldName }}:</strong>\r\n                </div>\r\n                <div class=\"detail-value text-muted\">\r\n                  {{ detail.FieldValue }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 詳情分頁控制器 -->\r\n          <div class=\"detail-pagination mt-3\" *ngIf=\"detailPagination.totalPages > 1\">\r\n            <nav aria-label=\"模板詳情分頁\">\r\n              <ul class=\"pagination pagination-sm justify-content-center mb-0\">\r\n                <!-- 上一頁 -->\r\n                <li class=\"page-item\" [class.disabled]=\"detailPagination.currentPage === 1\">\r\n                  <button class=\"page-link\" (click)=\"goToDetailPage(detailPagination.currentPage - 1)\"\r\n                    [disabled]=\"detailPagination.currentPage === 1\">\r\n                    <i class=\"fas fa-chevron-left\"></i>\r\n                  </button>\r\n                </li>\r\n\r\n                <!-- 頁碼 -->\r\n                <li class=\"page-item\" *ngFor=\"let page of getDetailPageNumbers()\"\r\n                  [class.active]=\"page === detailPagination.currentPage\">\r\n                  <button class=\"page-link\" (click)=\"goToDetailPage(page)\">{{ page }}</button>\r\n                </li>\r\n\r\n                <!-- 下一頁 -->\r\n                <li class=\"page-item\" [class.disabled]=\"detailPagination.currentPage === detailPagination.totalPages\">\r\n                  <button class=\"page-link\" (click)=\"goToDetailPage(detailPagination.currentPage + 1)\"\r\n                    [disabled]=\"detailPagination.currentPage === detailPagination.totalPages\">\r\n                    <i class=\"fas fa-chevron-right\"></i>\r\n                  </button>\r\n                </li>\r\n              </ul>\r\n            </nav>\r\n          </div>\r\n\r\n          <ng-template #noDetails>\r\n            <div class=\"text-center py-3\">\r\n              <i class=\"fas fa-inbox fa-2x text-muted mb-2\"></i>\r\n              <p class=\"text-muted mb-0\">此模板暫無內容</p>\r\n            </div>\r\n          </ng-template>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer>\r\n    <div class=\"text-center\">\r\n      <button class=\"btn btn-secondary\" (click)=\"onClose()\">\r\n        <i class=\"fas fa-times mr-1\"></i>關閉\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>\r\n"], "mappings": "AAAA,SAAoBA,YAAY,QAA0C,eAAe;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,EAAEC,cAAc,QAAQ,gBAAgB;;;;;;;;;ICCvDC,EAAA,CAAAC,cAAA,iBAA0F;IAAnDD,EAAA,CAAAE,UAAA,mBAAAC,kEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,aAAA,EAAe;IAAA,EAAC;IAC9DT,EAAA,CAAAU,SAAA,YAAgC;IAAAV,EAAA,CAAAW,MAAA,gCAClC;IAAAX,EAAA,CAAAY,YAAA,EAAS;;;;;;IAaLZ,EAAA,CAAAC,cAAA,iBAAsG;IAA9CD,EAAA,CAAAE,UAAA,mBAAAW,wEAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAS,WAAA,EAAa;IAAA,EAAC;IAC7Ef,EAAA,CAAAU,SAAA,YAA4B;IAC9BV,EAAA,CAAAY,YAAA,EAAS;;;;;;IARXZ,EAFJ,CAAAC,cAAA,cAA4D,cACjC,gBAE2B;IADiBD,EAAA,CAAAgB,gBAAA,2BAAAC,sEAAAC,MAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAoB,kBAAA,CAAAd,MAAA,CAAAe,aAAA,EAAAH,MAAA,MAAAZ,MAAA,CAAAe,aAAA,GAAAH,MAAA;MAAA,OAAAlB,EAAA,CAAAQ,WAAA,CAAAU,MAAA;IAAA,EAA2B;IACvElB,EAArB,CAAAE,UAAA,mBAAAoB,8DAAA;MAAAtB,EAAA,CAAAI,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiB,QAAA,EAAU;IAAA,EAAC,yBAAAC,oEAAA;MAAAxB,EAAA,CAAAI,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAgBF,MAAA,CAAAiB,QAAA,EAAU;IAAA,EAAC;IADjDvB,EAAA,CAAAY,YAAA,EACkD;IAEhDZ,EADF,CAAAC,cAAA,cAAgC,iBAC+C;IAArBD,EAAA,CAAAE,UAAA,mBAAAuB,+DAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiB,QAAA,EAAU;IAAA,EAAC;IAC1EvB,EAAA,CAAAU,SAAA,YAA6B;IAC/BV,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAA0B,UAAA,IAAAC,+CAAA,qBAAsG;IAK5G3B,EAFI,CAAAY,YAAA,EAAM,EACF,EACF;;;;IAXiEZ,EAAA,CAAA4B,SAAA,GAA2B;IAA3B5B,EAAA,CAAA6B,gBAAA,YAAAvB,MAAA,CAAAe,aAAA,CAA2B;IAMXrB,EAAA,CAAA4B,SAAA,GAAmB;IAAnB5B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAe,aAAA,CAAmB;;;;;IAsChGrB,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAAU,SAAA,YAAkC;IAClCV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAW,MAAA,2CAAM;IACdX,EADc,CAAAY,YAAA,EAAO,EACf;;;;;;IAGFZ,EAFJ,CAAAC,cAAA,cAA2E,gBAC/C,gBACmE;IAA9CD,EAAA,CAAAgB,gBAAA,2BAAAe,6EAAAb,MAAA;MAAA,MAAAc,OAAA,GAAAhC,EAAA,CAAAI,aAAA,CAAA6B,GAAA,EAAAC,SAAA;MAAAlC,EAAA,CAAAoB,kBAAA,CAAAY,OAAA,CAAAG,QAAA,EAAAjB,MAAA,MAAAc,OAAA,CAAAG,QAAA,GAAAjB,MAAA;MAAA,OAAAlB,EAAA,CAAAQ,WAAA,CAAAU,MAAA;IAAA,EAA2B;IAAxElB,EAAA,CAAAY,YAAA,EAA2F;IAEzFZ,EADF,CAAAC,cAAA,cAA0B,cACA;IAAAD,EAAA,CAAAW,MAAA,GAAoC;IAAAX,EAAA,CAAAY,YAAA,EAAM;IAClEZ,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAW,MAAA,GAAyC;IAGtEX,EAHsE,CAAAY,YAAA,EAAM,EAClE,EACA,EACJ;;;;;IANuEZ,EAAA,CAAA4B,SAAA,GAAiB;IAAjB5B,EAAA,CAAAoC,sBAAA,kBAAAC,IAAA,KAAiB;IAA7CrC,EAAA,CAAA6B,gBAAA,YAAAG,OAAA,CAAAG,QAAA,CAA2B;IAE9CnC,EAAA,CAAA4B,SAAA,GAAoC;IAApC5B,EAAA,CAAAsC,iBAAA,CAAAN,OAAA,CAAAO,YAAA,IAAAP,OAAA,CAAAQ,IAAA,CAAoC;IACrCxC,EAAA,CAAA4B,SAAA,GAAyC;IAAzC5B,EAAA,CAAAsC,iBAAA,CAAAN,OAAA,CAAAS,UAAA,IAAAT,OAAA,CAAAU,WAAA,CAAyC;;;;;;IApC1E1C,EAHN,CAAAC,cAAA,cAAwD,cAC1B,cACD,cACC;IACtBD,EAAA,CAAAU,SAAA,YAA2B;IAC3BV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAW,MAAA,+BAAI;IAEdX,EAFc,CAAAY,YAAA,EAAO,EACb,EACF;IAENZ,EAAA,CAAAC,cAAA,eAA0D;IAApDD,EAAA,CAAAE,UAAA,sBAAAyC,gEAAA;MAAA3C,EAAA,CAAAI,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAYF,MAAA,CAAAuC,eAAA,EAAiB;IAAA,EAAC;IAG9B7C,EAFJ,CAAAC,cAAA,cAAuB,cACI,iBACI;IACzBD,EAAA,CAAAW,MAAA,kCAAK;IAAAX,EAAA,CAAAC,cAAA,gBAAuB;IAAAD,EAAA,CAAAW,MAAA,SAAC;IAC/BX,EAD+B,CAAAY,YAAA,EAAO,EAC9B;IACRZ,EAAA,CAAAC,cAAA,iBACgD;IADTD,EAAA,CAAAgB,gBAAA,2BAAA8B,uEAAA5B,MAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAoB,kBAAA,CAAAd,MAAA,CAAAyC,WAAA,CAAAP,IAAA,EAAAtB,MAAA,MAAAZ,MAAA,CAAAyC,WAAA,CAAAP,IAAA,GAAAtB,MAAA;MAAA,OAAAlB,EAAA,CAAAQ,WAAA,CAAAU,MAAA;IAAA,EAA8B;IAEvElB,EAFE,CAAAY,YAAA,EACgD,EAC5C;IAEJZ,EADF,CAAAC,cAAA,eAAyB,iBACI;IAAAD,EAAA,CAAAW,MAAA,gCAAI;IAAAX,EAAA,CAAAY,YAAA,EAAQ;IACvCZ,EAAA,CAAAC,cAAA,iBAC4C;IADLD,EAAA,CAAAgB,gBAAA,2BAAAgC,uEAAA9B,MAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAoB,kBAAA,CAAAd,MAAA,CAAAyC,WAAA,CAAAL,WAAA,EAAAxB,MAAA,MAAAZ,MAAA,CAAAyC,WAAA,CAAAL,WAAA,GAAAxB,MAAA;MAAA,OAAAlB,EAAA,CAAAQ,WAAA,CAAAU,MAAA;IAAA,EAAqC;IAGhFlB,EAHI,CAAAY,YAAA,EAC4C,EACxC,EACF;IAGJZ,EADF,CAAAC,cAAA,eAAoC,iBACP;IACzBD,EAAA,CAAAW,MAAA,sEAAW;IAAAX,EAAA,CAAAC,cAAA,gBAAuB;IAAAD,EAAA,CAAAW,MAAA,SAAC;IACrCX,EADqC,CAAAY,YAAA,EAAO,EACpC;IACRZ,EAAA,CAAAC,cAAA,eAA4B;IAK1BD,EAJA,CAAA0B,UAAA,KAAAuB,6CAAA,kBAA4D,KAAAC,6CAAA,kBAIe;IAU/ElD,EADE,CAAAY,YAAA,EAAM,EACF;IAGJZ,EADF,CAAAC,cAAA,eAA0B,kBAC+C;IAA9BD,EAAA,CAAAE,UAAA,mBAAAiD,gEAAA;MAAAnD,EAAA,CAAAI,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8C,iBAAA,EAAmB;IAAA,EAAC;IACpEpD,EAAA,CAAAW,MAAA,sBACF;IAAAX,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,kBAAuC;IACrCD,EAAA,CAAAW,MAAA,kCACF;IAIRX,EAJQ,CAAAY,YAAA,EAAS,EACL,EACD,EACH,EACF;;;;IAzC2CZ,EAAA,CAAA4B,SAAA,IAA8B;IAA9B5B,EAAA,CAAA6B,gBAAA,YAAAvB,MAAA,CAAAyC,WAAA,CAAAP,IAAA,CAA8B;IAK9BxC,EAAA,CAAA4B,SAAA,GAAqC;IAArC5B,EAAA,CAAA6B,gBAAA,YAAAvB,MAAA,CAAAyC,WAAA,CAAAL,WAAA,CAAqC;IAUtE1C,EAAA,CAAA4B,SAAA,GAAgC;IAAhC5B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA+C,aAAA,CAAAC,MAAA,OAAgC;IAIhBtD,EAAA,CAAA4B,SAAA,EAAkB;IAAlB5B,EAAA,CAAA8B,UAAA,YAAAxB,MAAA,CAAA+C,aAAA,CAAkB;;;;;IA4B9CrD,EADF,CAAAC,cAAA,cAA4D,gBAChC;IACxBD,EAAA,CAAAW,MAAA,GACF;IACFX,EADE,CAAAY,YAAA,EAAQ,EACJ;;;;IAFFZ,EAAA,CAAA4B,SAAA,GACF;IADE5B,EAAA,CAAAuD,kBAAA,mBAAAjD,MAAA,CAAAkD,iBAAA,CAAAF,MAAA,+BAAAhD,MAAA,CAAAe,aAAA,8BACF;;;;;IAKArB,EADF,CAAAC,cAAA,cAA4E,gBAChD;IACxBD,EAAA,CAAAW,MAAA,GAGF;IACFX,EADE,CAAAY,YAAA,EAAQ,EACJ;;;;IAJFZ,EAAA,CAAA4B,SAAA,GAGF;IAHE5B,EAAA,CAAAyD,kBAAA,0BAAAnD,MAAA,CAAAoD,kBAAA,CAAAC,WAAA,QAAArD,MAAA,CAAAoD,kBAAA,CAAAE,QAAA,aAAAtD,MAAA,CAAAuD,IAAA,CAAAC,GAAA,CAAAxD,MAAA,CAAAoD,kBAAA,CAAAC,WAAA,GAAArD,MAAA,CAAAoD,kBAAA,CAAAE,QAAA,EAAAtD,MAAA,CAAAoD,kBAAA,CAAAK,UAAA,4BAAAzD,MAAA,CAAAoD,kBAAA,CAAAK,UAAA,yBAGF;;;;;;IAyBU/D,EAAA,CAAAC,cAAA,iBACsC;IADPD,EAAA,CAAAE,UAAA,mBAAA8D,+EAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAA6D,IAAA;MAAA,MAAAC,OAAA,GAAAlE,EAAA,CAAAO,aAAA,GAAA2B,SAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAA0D,OAAA,CAAAC,UAAA,IAA2B7D,MAAA,CAAA8D,gBAAA,CAAAF,OAAA,CAAAC,UAAA,CAAgC;IAAA,EAAC;IAEzFnE,EAAA,CAAAU,SAAA,YAA4B;IAACV,EAAA,CAAAW,MAAA,qBAC/B;IAAAX,EAAA,CAAAY,YAAA,EAAS;;;;;;IAbXZ,EAFJ,CAAAC,cAAA,SAAuE,SACjE,aACM;IAAAD,EAAA,CAAAW,MAAA,GAAsB;IAChCX,EADgC,CAAAY,YAAA,EAAS,EACpC;IAEHZ,EADF,CAAAC,cAAA,SAAI,eACuB;IAAAD,EAAA,CAAAW,MAAA,GAA8B;IACzDX,EADyD,CAAAY,YAAA,EAAO,EAC3D;IAGDZ,EAFJ,CAAAC,cAAA,aAAwB,cAC2B,iBAC2B;IAA7CD,EAAA,CAAAE,UAAA,mBAAAmE,qEAAA;MAAA,MAAAH,OAAA,GAAAlE,EAAA,CAAAI,aAAA,CAAAkE,GAAA,EAAApC,SAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiE,gBAAA,CAAAL,OAAA,CAAqB;IAAA,EAAC;IAC1DlE,EAAA,CAAAU,SAAA,aAA0B;IAACV,EAAA,CAAAW,MAAA,sBAC7B;IAAAX,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAA0B,UAAA,KAAA8C,sDAAA,qBACsC;IAK5CxE,EAFI,CAAAY,YAAA,EAAM,EACH,EACF;;;;IAhBOZ,EAAA,CAAA4B,SAAA,GAAsB;IAAtB5B,EAAA,CAAAsC,iBAAA,CAAA4B,OAAA,CAAAO,YAAA,CAAsB;IAGLzE,EAAA,CAAA4B,SAAA,GAA8B;IAA9B5B,EAAA,CAAAsC,iBAAA,CAAA4B,OAAA,CAAAQ,WAAA,yBAA8B;IAQlD1E,EAAA,CAAA4B,SAAA,GAAoB;IAApB5B,EAAA,CAAA8B,UAAA,SAAAoC,OAAA,CAAAC,UAAA,CAAoB;;;;;;IAavBnE,EAAA,CAAAC,cAAA,gBAAgD;IAC9CD,EAAA,CAAAW,MAAA,+DAAU;IAAAX,EAAA,CAAAC,cAAA,YAAqD;IAAxBD,EAAA,CAAAE,UAAA,mBAAAyE,wEAAA;MAAA3E,EAAA,CAAAI,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAS,WAAA,EAAa;IAAA,EAAC;IAACf,EAAA,CAAAW,MAAA,+BAAI;IACrEX,EADqE,CAAAY,YAAA,EAAI,EACjE;;;;;IAPVZ,EAFJ,CAAAC,cAAA,SAAmE,aACxB,cACd;IACvBD,EAAA,CAAAU,SAAA,YAAwD;IACxDV,EAAA,CAAAC,cAAA,YAA2B;IACzBD,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAY,YAAA,EAAI;IACJZ,EAAA,CAAA0B,UAAA,IAAAmD,oDAAA,oBAAgD;IAKtD7E,EAFI,CAAAY,YAAA,EAAM,EACH,EACF;;;;IAPGZ,EAAA,CAAA4B,SAAA,GACF;IADE5B,EAAA,CAAA8E,kBAAA,MAAAxE,MAAA,CAAAe,aAAA,oGACF;IAC2BrB,EAAA,CAAA4B,SAAA,EAAmB;IAAnB5B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAe,aAAA,CAAmB;;;;;;IAyBlDrB,EAFF,CAAAC,cAAA,aAC2D,iBACE;IAAjCD,EAAA,CAAAE,UAAA,mBAAA6E,2EAAA;MAAA,MAAAC,QAAA,GAAAhF,EAAA,CAAAI,aAAA,CAAA6E,IAAA,EAAA/C,SAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4E,gBAAA,CAAAF,QAAA,CAAsB;IAAA,EAAC;IAAChF,EAAA,CAAAW,MAAA,GAAU;IACvEX,EADuE,CAAAY,YAAA,EAAS,EAC3E;;;;;IAFHZ,EAAA,CAAAmF,WAAA,WAAAH,QAAA,KAAA1E,MAAA,CAAAoD,kBAAA,CAAAC,WAAA,CAAwD;IACG3D,EAAA,CAAA4B,SAAA,GAAU;IAAV5B,EAAA,CAAAsC,iBAAA,CAAA0C,QAAA,CAAU;;;;;;IATrEhF,EALR,CAAAC,cAAA,cAAiF,cACtD,aAC0C,aAEe,iBAExB;IAD1BD,EAAA,CAAAE,UAAA,mBAAAkF,sEAAA;MAAApF,EAAA,CAAAI,aAAA,CAAAiF,IAAA;MAAA,MAAA/E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4E,gBAAA,CAAA5E,MAAA,CAAAoD,kBAAA,CAAAC,WAAA,GAAkD,CAAC,CAAC;IAAA,EAAC;IAEtF3D,EAAA,CAAAU,SAAA,YAAmC;IAEvCV,EADE,CAAAY,YAAA,EAAS,EACN;IAGLZ,EAAA,CAAA0B,UAAA,IAAA4D,kDAAA,iBAC2D;IAMzDtF,EADF,CAAAC,cAAA,aAA0G,iBAExB;IADtDD,EAAA,CAAAE,UAAA,mBAAAqF,sEAAA;MAAAvF,EAAA,CAAAI,aAAA,CAAAiF,IAAA;MAAA,MAAA/E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4E,gBAAA,CAAA5E,MAAA,CAAAoD,kBAAA,CAAAC,WAAA,GAAkD,CAAC,CAAC;IAAA,EAAC;IAEtF3D,EAAA,CAAAU,SAAA,YAAoC;IAK9CV,EAJQ,CAAAY,YAAA,EAAS,EACN,EACF,EACD,EACF;;;;IAtBsBZ,EAAA,CAAA4B,SAAA,GAAuD;IAAvD5B,EAAA,CAAAmF,WAAA,aAAA7E,MAAA,CAAAoD,kBAAA,CAAAC,WAAA,OAAuD;IAEzE3D,EAAA,CAAA4B,SAAA,EAAiD;IAAjD5B,EAAA,CAAA8B,UAAA,aAAAxB,MAAA,CAAAoD,kBAAA,CAAAC,WAAA,OAAiD;IAMd3D,EAAA,CAAA4B,SAAA,GAA2B;IAA3B5B,EAAA,CAAA8B,UAAA,YAAAxB,MAAA,CAAAkF,sBAAA,GAA2B;IAM5CxF,EAAA,CAAA4B,SAAA,EAAmF;IAAnF5B,EAAA,CAAAmF,WAAA,aAAA7E,MAAA,CAAAoD,kBAAA,CAAAC,WAAA,KAAArD,MAAA,CAAAoD,kBAAA,CAAA+B,UAAA,CAAmF;IAErGzF,EAAA,CAAA4B,SAAA,EAA6E;IAA7E5B,EAAA,CAAA8B,UAAA,aAAAxB,MAAA,CAAAoD,kBAAA,CAAAC,WAAA,KAAArD,MAAA,CAAAoD,kBAAA,CAAA+B,UAAA,CAA6E;;;;;IApFzFzF,EAAA,CAAAC,cAAA,cAAyE;IASvED,EAPA,CAAA0B,UAAA,IAAAgE,4CAAA,kBAA4D,IAAAC,4CAAA,kBAOgB;IAYpE3F,EAJR,CAAAC,cAAA,cAA8B,gBACmB,gBAClB,SACrB,aACc;IAAAD,EAAA,CAAAW,MAAA,+BAAI;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACzBZ,EAAA,CAAAC,cAAA,aAAgB;IAAAD,EAAA,CAAAW,MAAA,oBAAE;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACvBZ,EAAA,CAAAC,cAAA,cAAoC;IAAAD,EAAA,CAAAW,MAAA,oBAAE;IAE1CX,EAF0C,CAAAY,YAAA,EAAK,EACxC,EACC;IACRZ,EAAA,CAAAC,cAAA,aAAO;IAoBLD,EAnBA,CAAA0B,UAAA,KAAAkE,4CAAA,kBAAuE,KAAAC,4CAAA,iBAmBJ;IAezE7F,EAFI,CAAAY,YAAA,EAAQ,EACF,EACJ;IAGNZ,EAAA,CAAA0B,UAAA,KAAAoE,6CAAA,mBAAiF;IA2BnF9F,EAAA,CAAAY,YAAA,EAAM;;;;IAzFmCZ,EAAA,CAAA4B,SAAA,EAAmB;IAAnB5B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAe,aAAA,CAAmB;IAOvBrB,EAAA,CAAA4B,SAAA,EAAuC;IAAvC5B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAoD,kBAAA,CAAAK,UAAA,KAAuC;IAkBhD/D,EAAA,CAAA4B,SAAA,IAAuB;IAAA5B,EAAvB,CAAA8B,UAAA,YAAAxB,MAAA,CAAAyF,kBAAA,CAAuB,iBAAAzF,MAAA,CAAA0F,iBAAA,CAA0B;IAmBhEhG,EAAA,CAAA4B,SAAA,EAA4D;IAA5D5B,EAAA,CAAA8B,UAAA,UAAAxB,MAAA,CAAAyF,kBAAA,IAAAzF,MAAA,CAAAyF,kBAAA,CAAAzC,MAAA,OAA4D;IAkB/BtD,EAAA,CAAA4B,SAAA,EAAuC;IAAvC5B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAoD,kBAAA,CAAA+B,UAAA,KAAuC;;;;;IA2C3EzF,EADF,CAAAC,cAAA,cAA4E,aAClE;IAAAD,EAAA,CAAAW,MAAA,yBAAG;IAAAX,EAAA,CAAAY,YAAA,EAAS;IACpBZ,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAW,MAAA,GAAkC;IAC7DX,EAD6D,CAAAY,YAAA,EAAO,EAC9D;;;;IADqBZ,EAAA,CAAA4B,SAAA,GAAkC;IAAlC5B,EAAA,CAAAsC,iBAAA,CAAAhC,MAAA,CAAA2F,gBAAA,CAAAvB,WAAA,CAAkC;;;;;IASzD1E,EAAA,CAAAC,cAAA,gBAAkE;IAChED,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAY,YAAA,EAAQ;;;;IADNZ,EAAA,CAAA4B,SAAA,EACF;IADE5B,EAAA,CAAAuD,kBAAA,aAAAjD,MAAA,CAAA4F,gBAAA,CAAAvC,WAAA,SAAArD,MAAA,CAAA4F,gBAAA,CAAAT,UAAA,aACF;;;;;IAOIzF,EAHJ,CAAAC,cAAA,cACmE,eACvC,gBACQ;IAAAD,EAAA,CAAAW,MAAA,GAC1B;IACRX,EADQ,CAAAY,YAAA,EAAO,EACT;IAGFZ,EAFJ,CAAAC,cAAA,eAA6C,eACjB,aAChB;IAAAD,EAAA,CAAAW,MAAA,GAAuB;IACjCX,EADiC,CAAAY,YAAA,EAAS,EACpC;IACNZ,EAAA,CAAAC,cAAA,eAAqC;IACnCD,EAAA,CAAAW,MAAA,GACF;IAEJX,EAFI,CAAAY,YAAA,EAAM,EACF,EACF;;;;;;IAX8BZ,EAAA,CAAA4B,SAAA,GAC1B;IAD0B5B,EAAA,CAAAsC,iBAAA,EAAAhC,MAAA,CAAA4F,gBAAA,CAAAvC,WAAA,QAAArD,MAAA,CAAA4F,gBAAA,CAAAtC,QAAA,GAAAuC,KAAA,KAC1B;IAIInG,EAAA,CAAA4B,SAAA,GAAuB;IAAvB5B,EAAA,CAAA8E,kBAAA,KAAAsB,UAAA,CAAAC,SAAA,MAAuB;IAG/BrG,EAAA,CAAA4B,SAAA,GACF;IADE5B,EAAA,CAAA8E,kBAAA,MAAAsB,UAAA,CAAAE,UAAA,MACF;;;;;IAbNtG,EAAA,CAAAC,cAAA,cAAmF;IACjFD,EAAA,CAAA0B,UAAA,IAAA6E,oDAAA,mBACmE;IAcrEvG,EAAA,CAAAY,YAAA,EAAM;;;;IAfoBZ,EAAA,CAAA4B,SAAA,EAAqB;IAArB5B,EAAA,CAAA8B,UAAA,YAAAxB,MAAA,CAAAkG,gBAAA,CAAqB;;;;;;IAgCvCxG,EAFF,CAAAC,cAAA,aACyD,iBACE;IAA/BD,EAAA,CAAAE,UAAA,mBAAAuG,4EAAA;MAAA,MAAAC,QAAA,GAAA1G,EAAA,CAAAI,aAAA,CAAAuG,IAAA,EAAAzE,SAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsG,cAAA,CAAAF,QAAA,CAAoB;IAAA,EAAC;IAAC1G,EAAA,CAAAW,MAAA,GAAU;IACrEX,EADqE,CAAAY,YAAA,EAAS,EACzE;;;;;IAFHZ,EAAA,CAAAmF,WAAA,WAAAuB,QAAA,KAAApG,MAAA,CAAA4F,gBAAA,CAAAvC,WAAA,CAAsD;IACG3D,EAAA,CAAA4B,SAAA,GAAU;IAAV5B,EAAA,CAAAsC,iBAAA,CAAAoE,QAAA,CAAU;;;;;;IATnE1G,EALR,CAAAC,cAAA,eAA4E,eACjD,aAC0C,aAEa,iBAExB;IADxBD,EAAA,CAAAE,UAAA,mBAAA2G,uEAAA;MAAA7G,EAAA,CAAAI,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsG,cAAA,CAAAtG,MAAA,CAAA4F,gBAAA,CAAAvC,WAAA,GAA8C,CAAC,CAAC;IAAA,EAAC;IAElF3D,EAAA,CAAAU,SAAA,YAAmC;IAEvCV,EADE,CAAAY,YAAA,EAAS,EACN;IAGLZ,EAAA,CAAA0B,UAAA,IAAAqF,mDAAA,iBACyD;IAMvD/G,EADF,CAAAC,cAAA,aAAsG,iBAExB;IADlDD,EAAA,CAAAE,UAAA,mBAAA8G,uEAAA;MAAAhH,EAAA,CAAAI,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsG,cAAA,CAAAtG,MAAA,CAAA4F,gBAAA,CAAAvC,WAAA,GAA8C,CAAC,CAAC;IAAA,EAAC;IAElF3D,EAAA,CAAAU,SAAA,YAAoC;IAK9CV,EAJQ,CAAAY,YAAA,EAAS,EACN,EACF,EACD,EACF;;;;IAtBsBZ,EAAA,CAAA4B,SAAA,GAAqD;IAArD5B,EAAA,CAAAmF,WAAA,aAAA7E,MAAA,CAAA4F,gBAAA,CAAAvC,WAAA,OAAqD;IAEvE3D,EAAA,CAAA4B,SAAA,EAA+C;IAA/C5B,EAAA,CAAA8B,UAAA,aAAAxB,MAAA,CAAA4F,gBAAA,CAAAvC,WAAA,OAA+C;IAMZ3D,EAAA,CAAA4B,SAAA,GAAyB;IAAzB5B,EAAA,CAAA8B,UAAA,YAAAxB,MAAA,CAAA2G,oBAAA,GAAyB;IAM1CjH,EAAA,CAAA4B,SAAA,EAA+E;IAA/E5B,EAAA,CAAAmF,WAAA,aAAA7E,MAAA,CAAA4F,gBAAA,CAAAvC,WAAA,KAAArD,MAAA,CAAA4F,gBAAA,CAAAT,UAAA,CAA+E;IAEjGzF,EAAA,CAAA4B,SAAA,EAAyE;IAAzE5B,EAAA,CAAA8B,UAAA,aAAAxB,MAAA,CAAA4F,gBAAA,CAAAvC,WAAA,KAAArD,MAAA,CAAA4F,gBAAA,CAAAT,UAAA,CAAyE;;;;;IASjFzF,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAU,SAAA,aAAkD;IAClDV,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAW,MAAA,iDAAO;IACpCX,EADoC,CAAAY,YAAA,EAAI,EAClC;;;;;;IA7EVZ,EAFJ,CAAAC,cAAA,cAAgF,cACQ,YACnE;IACfD,EAAA,CAAAU,SAAA,YAAoC;IACpCV,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAC,cAAA,iBAAiF;IAAhCD,EAAA,CAAAE,UAAA,mBAAAgH,gEAAA;MAAAlH,EAAA,CAAAI,aAAA,CAAA+G,IAAA;MAAA,MAAA7G,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8G,mBAAA,EAAqB;IAAA,EAAC;IAC9EpH,EAAA,CAAAU,SAAA,YAA4B;IAACV,EAAA,CAAAW,MAAA,qBAC/B;IACFX,EADE,CAAAY,YAAA,EAAS,EACL;IAENZ,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAA0B,UAAA,IAAA2F,6CAAA,kBAA4E;IAOxErH,EAFJ,CAAAC,cAAA,eAA4B,eAC0C,aACjD;IACfD,EAAA,CAAAU,SAAA,aAAgC;IAChCV,EAAA,CAAAW,MAAA,IACF;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAA0B,UAAA,KAAA4F,gDAAA,oBAAkE;IAGpEtH,EAAA,CAAAY,YAAA,EAAM;IAiDNZ,EA/CA,CAAA0B,UAAA,KAAA6F,8CAAA,kBAAmF,KAAAC,8CAAA,mBAmBP,KAAAC,sDAAA,gCAAAzH,EAAA,CAAA0H,sBAAA,CA4BpD;IAQ9B1H,EAFI,CAAAY,YAAA,EAAM,EACF,EACF;;;;;IA/EAZ,EAAA,CAAA4B,SAAA,GACF;IADE5B,EAAA,CAAA8E,kBAAA,oCAAAxE,MAAA,CAAA2F,gBAAA,CAAAxB,YAAA,MACF;IAOMzE,EAAA,CAAA4B,SAAA,GAAkC;IAAlC5B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA2F,gBAAA,CAAAvB,WAAA,CAAkC;IASlC1E,EAAA,CAAA4B,SAAA,GACF;IADE5B,EAAA,CAAA8E,kBAAA,gCAAAxE,MAAA,CAAAqH,sBAAA,CAAArE,MAAA,cACF;IAC2BtD,EAAA,CAAA4B,SAAA,EAAqC;IAArC5B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA4F,gBAAA,CAAAT,UAAA,KAAqC;IAK5DzF,EAAA,CAAA4B,SAAA,EAAyC;IAAA5B,EAAzC,CAAA8B,UAAA,SAAAxB,MAAA,CAAAqH,sBAAA,CAAArE,MAAA,KAAyC,aAAAsE,aAAA,CAAc;IAmBxB5H,EAAA,CAAA4B,SAAA,EAAqC;IAArC5B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA4F,gBAAA,CAAAT,UAAA,KAAqC;;;ADrNpF,OAAM,MAAOoC,uBAAuB;EA6ClCC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IA5C1B,KAAA1E,aAAa,GAAU,EAAE,CAAC,CAAC;IAC3B,KAAA2E,YAAY,GAAW,CAAC,CAAC,CAAC;IAC1B,KAAAC,eAAe,GAAY,KAAK,CAAC,CAAC;IACjC,KAAAC,cAAc,GAAG,IAAIvI,YAAY,EAAY;IAC7C,KAAAwI,KAAK,GAAG,IAAIxI,YAAY,EAAQ,CAAC,CAAC;IAE5C;IACA,KAAAkE,IAAI,GAAGA,IAAI;IAEX;IACA,KAAAuE,SAAS,GAAe,EAAE;IAC1B,KAAAC,eAAe,GAAqB,EAAE;IACtC,KAAApC,gBAAgB,GAAoB,IAAI;IAExC;IACA,KAAA5E,aAAa,GAAG,EAAE;IAClB,KAAAmC,iBAAiB,GAAe,EAAE;IAElC;IACA,KAAAE,kBAAkB,GAAG;MACnBC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,EAAE;MACZG,UAAU,EAAE,CAAC;MACb0B,UAAU,EAAE;KACb;IACD,KAAAM,kBAAkB,GAAe,EAAE;IAEnC;IACA,KAAAG,gBAAgB,GAAG;MACjBvC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,CAAC;MACXG,UAAU,EAAE,CAAC;MACb0B,UAAU,EAAE;KACb;IACD,KAAAe,gBAAgB,GAAqB,EAAE;IAEvC;IACA,KAAA8B,WAAW,GAAG,KAAK;IACnB,KAAAvF,WAAW,GAAG;MACZP,IAAI,EAAE,EAAE;MACRE,WAAW,EAAE,EAAE;MACf6F,aAAa,EAAE;KAChB;EAEuD;EAExDC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,EAAE;IAE9B;IACA,IAAI,IAAI,CAACT,eAAe,EAAE;MACxB,IAAI,CAACxH,aAAa,EAAE;IACtB;EACF;EAEAkI,WAAWA,CAAA;IACT,IAAI,CAACD,uBAAuB,EAAE;EAChC;EAEA;EACAD,aAAaA,CAAA;IACX;IACA,MAAMG,mBAAmB,GAAwB;MAC/CC,aAAa,EAAE,IAAI,CAACb,YAAY;MAAE;MAClCc,SAAS,EAAE,CAAC;MAAE;MACdC,QAAQ,EAAE,GAAG;MAAE;MACfC,aAAa,EAAE,IAAI,CAAC;KACrB;IAEDC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEN,mBAAmB,CAAC;IAE3D;IACA,IAAI,CAACb,eAAe,CAACoB,mCAAmC,CAAC;MACvDC,IAAI,EAAER;KACP,CAAC,CAACS,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD;UACAR,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEK,QAAQ,CAAC;UAElC;UACA,IAAI,CAACnB,SAAS,GAAGmB,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7CxF,UAAU,EAAEwF,IAAI,CAACC,WAAW;YAC5BnF,YAAY,EAAEkF,IAAI,CAACX,aAAa,IAAI,EAAE;YACtCtE,WAAW,EAAE,SAASiF,IAAI,CAACE,SAAS,GAAG,IAAIC,IAAI,CAACH,IAAI,CAACE,SAAS,CAAC,CAACE,kBAAkB,EAAE,GAAG,IAAI;WAC5F,CAAC,CAAC;UAEH;UACA,IAAI,CAACrB,uBAAuB,EAAE;UAE9B;UACA,IAAI,CAACL,eAAe,GAAG,EAAE;QAC3B,CAAC,MAAM;UACL;UACAY,OAAO,CAACe,KAAK,CAAC,WAAW,EAAET,QAAQ,CAACU,OAAO,CAAC;UAC5C,IAAI,CAAC7B,SAAS,GAAG,EAAE;UACnB,IAAI,CAACC,eAAe,GAAG,EAAE;UACzB,IAAI,CAACK,uBAAuB,EAAE;QAChC;MACF,CAAC;MACDsB,KAAK,EAAGA,KAAK,IAAI;QACf;QACAf,OAAO,CAACe,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAAC5B,SAAS,GAAG,EAAE;QACnB,IAAI,CAACC,eAAe,GAAG,EAAE;QACzB,IAAI,CAACK,uBAAuB,EAAE;MAChC;KACD,CAAC;EACJ;EAEA;EACAA,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACrH,aAAa,CAAC6I,IAAI,EAAE,EAAE;MAC9B,IAAI,CAAC1G,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAAC4E,SAAS,CAAC;IAC9C,CAAC,MAAM;MACL,MAAM+B,OAAO,GAAG,IAAI,CAAC9I,aAAa,CAAC+I,WAAW,EAAE;MAChD,IAAI,CAAC5G,iBAAiB,GAAG,IAAI,CAAC4E,SAAS,CAACiC,MAAM,CAACC,QAAQ,IACrDA,QAAQ,CAAC7F,YAAY,CAAC2F,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACpDG,QAAQ,CAAC5F,WAAW,IAAI4F,QAAQ,CAAC5F,WAAW,CAAC0F,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAE,CAC/E;IACH;IACA,IAAI,CAACK,wBAAwB,EAAE;EACjC;EAEA;EACAA,wBAAwBA,CAAA;IACtB,IAAI,CAAC9G,kBAAkB,CAACK,UAAU,GAAG,IAAI,CAACP,iBAAiB,CAACF,MAAM;IAClE,IAAI,CAACI,kBAAkB,CAAC+B,UAAU,GAAG5B,IAAI,CAAC4G,IAAI,CAAC,IAAI,CAAC/G,kBAAkB,CAACK,UAAU,GAAG,IAAI,CAACL,kBAAkB,CAACE,QAAQ,CAAC;IAErH;IACA,IAAI,IAAI,CAACF,kBAAkB,CAACC,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC+B,UAAU,EAAE;MAC5E,IAAI,CAAC/B,kBAAkB,CAACC,WAAW,GAAGE,IAAI,CAAC6G,GAAG,CAAC,CAAC,EAAE,IAAI,CAAChH,kBAAkB,CAAC+B,UAAU,CAAC;IACvF;IAEA,IAAI,CAACkF,wBAAwB,EAAE;EACjC;EAEA;EACAA,wBAAwBA,CAAA;IACtB,MAAMC,UAAU,GAAG,CAAC,IAAI,CAAClH,kBAAkB,CAACC,WAAW,GAAG,CAAC,IAAI,IAAI,CAACD,kBAAkB,CAACE,QAAQ;IAC/F,MAAMiH,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAClH,kBAAkB,CAACE,QAAQ;IAC9D,IAAI,CAACmC,kBAAkB,GAAG,IAAI,CAACvC,iBAAiB,CAACsH,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC9E;EAEA;EACA3F,gBAAgBA,CAAC6F,IAAY;IAC3B,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAACrH,kBAAkB,CAAC+B,UAAU,EAAE;MAC3D,IAAI,CAAC/B,kBAAkB,CAACC,WAAW,GAAGoH,IAAI;MAC1C,IAAI,CAACJ,wBAAwB,EAAE;IACjC;EACF;EAEA;EACAnF,sBAAsBA,CAAA;IACpB,MAAMwF,KAAK,GAAa,EAAE;IAC1B,MAAMvF,UAAU,GAAG,IAAI,CAAC/B,kBAAkB,CAAC+B,UAAU;IACrD,MAAM9B,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAACC,WAAW;IAEvD;IACA,MAAMsH,SAAS,GAAGpH,IAAI,CAAC6G,GAAG,CAAC,CAAC,EAAE/G,WAAW,GAAG,CAAC,CAAC;IAC9C,MAAMuH,OAAO,GAAGrH,IAAI,CAACC,GAAG,CAAC2B,UAAU,EAAE9B,WAAW,GAAG,CAAC,CAAC;IAErD,KAAK,IAAIwH,CAAC,GAAGF,SAAS,EAAEE,CAAC,IAAID,OAAO,EAAEC,CAAC,EAAE,EAAE;MACzCH,KAAK,CAACI,IAAI,CAACD,CAAC,CAAC;IACf;IAEA,OAAOH,KAAK;EACd;EAEA;EACAzJ,QAAQA,CAAA;IACN,IAAI,CAACmH,uBAAuB,EAAE;EAChC;EAEA;EACA3H,WAAWA,CAAA;IACT,IAAI,CAACM,aAAa,GAAG,EAAE;IACvB,IAAI,CAACqH,uBAAuB,EAAE;EAChC;EAIA;EACAjI,aAAaA,CAAA;IACX,IAAI,CAAC6H,WAAW,GAAG,IAAI;IACvB,IAAI,CAACvF,WAAW,GAAG;MACjBP,IAAI,EAAE,EAAE;MACRE,WAAW,EAAE,EAAE;MACf6F,aAAa,EAAE;KAChB;IACD;IACA;IACA,IAAI,CAAC,IAAI,CAACN,eAAe,EAAE;MACzB,IAAI,CAAC5E,aAAa,CAACgI,OAAO,CAAC1B,IAAI,IAAIA,IAAI,CAACxH,QAAQ,GAAG,KAAK,CAAC;IAC3D;EACF;EAEA;EACAiB,iBAAiBA,CAAA;IACf,IAAI,CAACkF,WAAW,GAAG,KAAK;IACxB,IAAI,CAACvF,WAAW,GAAG;MACjBP,IAAI,EAAE,EAAE;MACRE,WAAW,EAAE,EAAE;MACf6F,aAAa,EAAE;KAChB;EACH;EAEA;EACA1F,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACE,WAAW,CAACP,IAAI,CAAC0H,IAAI,EAAE,EAAE;MACjCoB,KAAK,CAAC,SAAS,CAAC;MAChB;IACF;IAEA,MAAM/C,aAAa,GAAG,IAAI,CAAClF,aAAa,CAACgH,MAAM,CAACV,IAAI,IAAIA,IAAI,CAACxH,QAAQ,CAAC;IACtE,IAAIoG,aAAa,CAACjF,MAAM,KAAK,CAAC,EAAE;MAC9BgI,KAAK,CAAC,WAAW,CAAC;MAClB;IACF;IAEA;IACA,IAAI,CAACC,cAAc,CAAC,IAAI,CAACxI,WAAW,CAACP,IAAI,EAAE,IAAI,CAACO,WAAW,CAACL,WAAW,EAAE6F,aAAa,CAAC;EACzF;EAEA;EACAgD,cAAcA,CAAC/I,IAAY,EAAEE,WAAmB,EAAE6F,aAAoB;IACpE;IACA,MAAMiD,gBAAgB,GAAqB;MACzC5B,WAAW,EAAE,IAAI;MAAE;MACnBZ,aAAa,EAAExG,IAAI,CAAC0H,IAAI,EAAE;MAC1BrB,aAAa,EAAE,IAAI,CAACb,YAAY;MAAE;MAClCyD,OAAO,EAAE,CAAC;MAAE;MACZC,OAAO,EAAEnD,aAAa,CAACmB,GAAG,CAACC,IAAI,KAAK;QAClCgC,iBAAiB,EAAE,IAAI;QAAE;QACzBC,UAAU,EAAE,IAAI,CAACC,QAAQ,CAAClC,IAAI,CAAC;QAAE;QACjCmC,YAAY,EAAE,IAAI,CAACC,YAAY,CAACpC,IAAI,CAAC,CAAC;OACZ;KAC7B;IAEDV,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEsC,gBAAgB,CAAC;IAErD;IACA,IAAI,CAACzD,eAAe,CAACiE,gCAAgC,CAAC;MACpD5C,IAAI,EAAEoC;KACP,CAAC,CAACnC,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B;UACAP,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEK,QAAQ,CAAC;UAEhC;UACA,IAAI,CAACd,aAAa,EAAE;UAEpB;UACA,IAAI,CAACH,WAAW,GAAG,KAAK;UACxBgD,KAAK,CAAC,OAAO9I,IAAI,cAAc+F,aAAa,CAACjF,MAAM,MAAM,CAAC;QAC5D,CAAC,MAAM;UACL;UACA2F,OAAO,CAACe,KAAK,CAAC,SAAS,EAAET,QAAQ,CAACU,OAAO,CAAC;UAC1CqB,KAAK,CAAC,UAAU/B,QAAQ,CAACU,OAAO,IAAI,MAAM,EAAE,CAAC;QAC/C;MACF,CAAC;MACDD,KAAK,EAAGA,KAAK,IAAI;QACf;QACAf,OAAO,CAACe,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9CsB,KAAK,CAAC,wBAAwB,CAAC;MACjC;KACD,CAAC;EACJ;EAEA;EACQO,QAAQA,CAAClC,IAAS;IACxB,OAAOA,IAAI,CAACsC,cAAc,IAAItC,IAAI,CAACuC,EAAE,IAAIvC,IAAI,CAACwC,EAAE,IAAI,CAAC;EACvD;EAEA;EACQJ,YAAYA,CAACK,KAAW;IAC9B;IACA,QAAQ,IAAI,CAACpE,YAAY;MACvB,KAAK,CAAC;QAAE;QACN,OAAO,cAAc;MACvB;QACE,OAAO,MAAM;IACjB;EACF;EAEA;EACQqE,aAAaA,CAAC1C,IAAS;IAC7B,OAAOA,IAAI,CAACpH,YAAY,IAAIoH,IAAI,CAACnH,IAAI,IAAImH,IAAI,CAAC2C,KAAK,IAAI,EAAE;EAC3D;EAEA;EACA/H,gBAAgBA,CAAC+F,QAAkB;IACjC,IAAI,CAACrE,gBAAgB,GAAGqE,QAAQ;IAChC,IAAI,CAACpC,cAAc,CAACqE,IAAI,CAACjC,QAAQ,CAAC;IAClC,IAAI,CAACkC,sBAAsB,EAAE;EAC/B;EAEA;EACAA,sBAAsBA,CAAA;IACpB,MAAMC,OAAO,GAAG,IAAI,CAAC9E,sBAAsB;IAC3C,IAAI,CAACzB,gBAAgB,CAACnC,UAAU,GAAG0I,OAAO,CAACnJ,MAAM;IACjD,IAAI,CAAC4C,gBAAgB,CAACT,UAAU,GAAG5B,IAAI,CAAC4G,IAAI,CAAC,IAAI,CAACvE,gBAAgB,CAACnC,UAAU,GAAG,IAAI,CAACmC,gBAAgB,CAACtC,QAAQ,CAAC;IAC/G,IAAI,CAACsC,gBAAgB,CAACvC,WAAW,GAAG,CAAC,CAAC,CAAC;IACvC,IAAI,CAAC+I,sBAAsB,EAAE;EAC/B;EAEA;EACAA,sBAAsBA,CAAA;IACpB,MAAMD,OAAO,GAAG,IAAI,CAAC9E,sBAAsB;IAC3C,MAAMiD,UAAU,GAAG,CAAC,IAAI,CAAC1E,gBAAgB,CAACvC,WAAW,GAAG,CAAC,IAAI,IAAI,CAACuC,gBAAgB,CAACtC,QAAQ;IAC3F,MAAMiH,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC1E,gBAAgB,CAACtC,QAAQ;IAC5D,IAAI,CAAC4C,gBAAgB,GAAGiG,OAAO,CAAC3B,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC7D;EAEA;EACAjE,cAAcA,CAACmE,IAAY;IACzB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAC7E,gBAAgB,CAACT,UAAU,EAAE;MACzD,IAAI,CAACS,gBAAgB,CAACvC,WAAW,GAAGoH,IAAI;MACxC,IAAI,CAAC2B,sBAAsB,EAAE;IAC/B;EACF;EAEA;EACAzF,oBAAoBA,CAAA;IAClB,MAAM+D,KAAK,GAAa,EAAE;IAC1B,MAAMvF,UAAU,GAAG,IAAI,CAACS,gBAAgB,CAACT,UAAU;IACnD,MAAM9B,WAAW,GAAG,IAAI,CAACuC,gBAAgB,CAACvC,WAAW;IAErD;IACA,MAAMsH,SAAS,GAAGpH,IAAI,CAAC6G,GAAG,CAAC,CAAC,EAAE/G,WAAW,GAAG,CAAC,CAAC;IAC9C,MAAMuH,OAAO,GAAGrH,IAAI,CAACC,GAAG,CAAC2B,UAAU,EAAE9B,WAAW,GAAG,CAAC,CAAC;IAErD,KAAK,IAAIwH,CAAC,GAAGF,SAAS,EAAEE,CAAC,IAAID,OAAO,EAAEC,CAAC,EAAE,EAAE;MACzCH,KAAK,CAACI,IAAI,CAACD,CAAC,CAAC;IACf;IAEA,OAAOH,KAAK;EACd;EAEA;EACA2B,OAAOA,CAAA;IACL,IAAI,CAACxE,KAAK,CAACoE,IAAI,EAAE;EACnB;EAEA;EACAnI,gBAAgBA,CAACwI,UAAkB;IACjC,IAAIC,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB;MACA,IAAI,CAACC,kBAAkB,CAACF,UAAU,CAAC;IACrC;EACF;EAEA;EACAE,kBAAkBA,CAACF,UAAkB;IACnC;IACA3D,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;MAC1B0D,UAAU,EAAEA,UAAU;MACtB5E,YAAY,EAAE,IAAI,CAACA;KACpB,CAAC;IAEF;IACA,IAAI,CAACI,SAAS,GAAG,IAAI,CAACA,SAAS,CAACiC,MAAM,CAAC0C,CAAC,IAAIA,CAAC,CAAC5I,UAAU,KAAKyI,UAAU,CAAC;IACxE,IAAI,CAACvE,eAAe,GAAG,IAAI,CAACA,eAAe,CAACgC,MAAM,CAAC2C,CAAC,IAAIA,CAAC,CAAC7I,UAAU,KAAKyI,UAAU,CAAC;IACpF,IAAI,CAAClE,uBAAuB,EAAE;IAE9B;IACA,IAAI,IAAI,CAACzC,gBAAgB,EAAE9B,UAAU,KAAKyI,UAAU,EAAE;MACpD,IAAI,CAAC3G,gBAAgB,GAAG,IAAI;IAC9B;IAEAqF,KAAK,CAAC,OAAO,CAAC;EAChB;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA+BA;EACAlE,mBAAmBA,CAAA;IACjB,IAAI,CAACnB,gBAAgB,GAAG,IAAI;EAC9B;EAEA;EACA,IAAI0B,sBAAsBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAC1B,gBAAgB,EAAE;MAC1B,OAAO,EAAE;IACX;IACA,OAAO,IAAI,CAACoC,eAAe,CAACgC,MAAM,CAAC2C,CAAC,IAAIA,CAAC,CAAC7I,UAAU,KAAK,IAAI,CAAC8B,gBAAiB,CAAC9B,UAAU,CAAC;EAC7F;EAEA;EACA6B,iBAAiBA,CAACiH,KAAa,EAAE3C,QAAkB;IACjD,OAAOA,QAAQ,CAACnG,UAAU,IAAI8I,KAAK;EACrC;;;uCAvaWpF,uBAAuB,EAAA7H,EAAA,CAAAkN,iBAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAvBvF,uBAAuB;MAAAwF,SAAA;MAAAC,MAAA;QAAAjK,aAAA;QAAA2E,YAAA;QAAAC,eAAA;MAAA;MAAAsF,OAAA;QAAArF,cAAA;QAAAC,KAAA;MAAA;MAAAqF,UAAA;MAAAC,QAAA,GAAAzN,EAAA,CAAA0N,oBAAA,EAAA1N,EAAA,CAAA2N,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAxD,QAAA,WAAAyD,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX9BhO,EAHN,CAAAC,cAAA,iBAA+D,qBAC7C,aACiD,YAC5C;UAAAD,EAAA,CAAAW,MAAA,GAAuC;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAC7DZ,EAAA,CAAA0B,UAAA,IAAAwM,yCAAA,oBAA0F;UAI9FlO,EADE,CAAAY,YAAA,EAAM,EACS;UACjBZ,EAAA,CAAAC,cAAA,sBAAsC;UA2KpCD,EAzKA,CAAA0B,UAAA,IAAAyM,sCAAA,iBAA4D,IAAAC,sCAAA,kBAgBJ,IAAAC,sCAAA,kBA2DiB,KAAAC,uCAAA,kBA8FO;UAoFlFtO,EAAA,CAAAY,YAAA,EAAe;UAGXZ,EAFJ,CAAAC,cAAA,sBAAgB,eACW,kBAC+B;UAApBD,EAAA,CAAAE,UAAA,mBAAAqO,0DAAA;YAAA,OAASN,GAAA,CAAAtB,OAAA,EAAS;UAAA,EAAC;UACnD3M,EAAA,CAAAU,SAAA,aAAiC;UAAAV,EAAA,CAAAW,MAAA,qBACnC;UAGNX,EAHM,CAAAY,YAAA,EAAS,EACL,EACS,EACT;;;UA7QaZ,EAAA,CAAA4B,SAAA,GAAuC;UAAvC5B,EAAA,CAAAsC,iBAAA,CAAA2L,GAAA,CAAAhG,eAAA,2DAAuC;UACUjI,EAAA,CAAA4B,SAAA,EAAsB;UAAtB5B,EAAA,CAAA8B,UAAA,UAAAmM,GAAA,CAAAhG,eAAA,CAAsB;UAOtDjI,EAAA,CAAA4B,SAAA,GAAsB;UAAtB5B,EAAA,CAAA8B,UAAA,UAAAmM,GAAA,CAAAhG,eAAA,CAAsB;UAgBpDjI,EAAA,CAAA4B,SAAA,EAAiB;UAAjB5B,EAAA,CAAA8B,UAAA,SAAAmM,GAAA,CAAA3F,WAAA,CAAiB;UA2DKtI,EAAA,CAAA4B,SAAA,EAA2C;UAA3C5B,EAAA,CAAA8B,UAAA,UAAAmM,GAAA,CAAAhI,gBAAA,KAAAgI,GAAA,CAAAhG,eAAA,CAA2C;UA8FjEjI,EAAA,CAAA4B,SAAA,EAA0C;UAA1C5B,EAAA,CAAA8B,UAAA,SAAAmM,GAAA,CAAAhI,gBAAA,KAAAgI,GAAA,CAAAhG,eAAA,CAA0C;;;qBDxKxCrI,YAAY,EAAA4O,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE7O,WAAW,EAAA8O,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,4BAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,oBAAA,EAAAL,EAAA,CAAAM,iBAAA,EAAAN,EAAA,CAAAO,kBAAA,EAAAP,EAAA,CAAAQ,OAAA,EAAAR,EAAA,CAAAS,MAAA,EAAEtP,YAAY,EAAAuP,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,qBAAA,EAAAH,EAAA,CAAAI,qBAAA,EAAE1P,cAAc;MAAA2P,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}