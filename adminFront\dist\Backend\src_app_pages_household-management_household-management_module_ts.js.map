{"version": 3, "file": "src_app_pages_household-management_household-management_module_ts.js", "mappings": ";;;;;;;;;;;;;;;AAMM,MAAOA,uBAAuB;EAChCC,SAASA,CAACC,KAAa;IACnB,QAAQA,KAAK;MACT,KAAK,CAAC;QACF,OAAO,IAAI;MACf,KAAK,CAAC;QACF,OAAO,IAAI;MACf;QACI,OAAO,EAAE;IACjB;EACJ;;;uCAVSF,uBAAuB;IAAA;EAAA;;;;YAAvBA,uBAAuB;MAAAG,IAAA;MAAAC,UAAA;IAAA;EAAA;;;;;;;;;;;;;;;ACN7B,IAAKC,kBAOX;AAPD,WAAYA,kBAAkB;EAC5B;EACAA,kBAAA,CAAAA,kBAAA,8DAAQ;EACR;EACAA,kBAAA,CAAAA,kBAAA,kDAAO;EACP;EACAA,kBAAA,CAAAA,kBAAA,sCAAM;AACR,CAAC,EAPWA,kBAAkB,KAAlBA,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACSG;AACmC;AACS;;;;;;;;;;;;;;;;;;;ICAnEI,4DAAA,iBAAoF;IAAvDA,wDAAA,mBAAAG,yEAAA;MAAAH,2DAAA,CAAAK,GAAA;MAAA,MAAAC,MAAA,GAAAN,2DAAA;MAAA,MAAAQ,sBAAA,GAAAR,yDAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAK,MAAA,CAAAH,sBAAA,CAA2B;IAAA,EAAC;IAChER,oDAAA,gCAAI;IAAAA,0DAAA,EAAS;;;;;;IAyBXA,4DAAA,iBAC8C;IAA5CA,wDAAA,mBAAAc,gFAAA;MAAAd,2DAAA,CAAAe,GAAA;MAAA,MAAAC,OAAA,GAAAhB,2DAAA,GAAAiB,SAAA;MAAA,MAAAX,MAAA,GAAAN,2DAAA;MAAA,MAAAQ,sBAAA,GAAAR,yDAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAY,MAAA,CAAAV,sBAAA,EAAAQ,OAAA,CAAiC;IAAA,EAAC;IAAChB,oDAAA,mBAAE;IAAAA,0DAAA,EAAS;;;;;IAP3DA,4DADF,aAA+E,SACzE;IAAAA,oDAAA,GAAuC;;IAAAA,0DAAA,EAAK;IAChDA,4DAAA,SAAI;IAAAA,oDAAA,GAAiC;IAAAA,0DAAA,EAAK;IAC1CA,4DAAA,SAAI;IAAAA,oDAAA,GAAsB;IAAAA,0DAAA,EAAK;IAC/BA,4DAAA,SAAI;IAAAA,oDAAA,GAA8B;IAAAA,0DAAA,EAAK;IACvCA,4DAAA,UAAI;IAAAA,oDAAA,IAAuE;IAAAA,0DAAA,EAAK;IAChFA,4DAAA,cAA6B;IAC3BA,wDAAA,KAAAoB,uDAAA,qBAC8C;IAElDpB,0DADE,EAAK,EACF;;;;;IATCA,uDAAA,GAAuC;IAAvCA,+DAAA,CAAAA,yDAAA,OAAAgB,OAAA,CAAAQ,OAAA,EAAuC;IACvCxB,uDAAA,GAAiC;IAAjCA,+DAAA,CAAAM,MAAA,CAAAmB,UAAA,CAAAT,OAAA,CAAAU,WAAA,EAAiC;IACjC1B,uDAAA,GAAsB;IAAtBA,+DAAA,CAAAgB,OAAA,CAAAW,YAAA,CAAsB;IACtB3B,uDAAA,GAA8B;IAA9BA,+DAAA,CAAAM,MAAA,CAAAmB,UAAA,CAAAT,OAAA,CAAAY,SAAA,EAA8B;IAC9B5B,uDAAA,GAAuE;IAAvEA,+DAAA,CAAAgB,OAAA,CAAAa,UAAA,kCAAAb,OAAA,CAAAa,UAAA,mCAAuE;IAErB7B,uDAAA,GAAc;IAAdA,wDAAA,SAAAM,MAAA,CAAAyB,QAAA,CAAc;;;;;;IAiDxE/B,4DAAA,iBAC8B;IAA5BA,wDAAA,mBAAAgC,yFAAA;MAAAhC,2DAAA,CAAAiC,GAAA;MAAAjC,2DAAA;MAAA,MAAAkC,YAAA,GAAAlC,yDAAA;MAAA,OAAAA,yDAAA,CAASkC,YAAA,CAAAC,KAAA,EAAiB;IAAA,EAAC;IAACnC,oDAAA,+BAAI;IAAAA,0DAAA,EAAS;;;;;IAUvCA,uDAAA,cAA2G;;;;IAAlBA,wDAAA,QAAAqC,QAAA,CAAAC,IAAA,EAAAtC,2DAAA,CAAiB;;;;;IAC1GA,4DAAA,eAC2E;IAAAA,oDAAA,UAAG;IAAAA,0DAAA,EAAO;;;;;;IAHvFA,4DAAA,cAAkG;IAEhGA,wDADA,IAAAwC,yEAAA,kBAA2G,IAAAC,0EAAA,mBAEhC;IAC3EzC,4DAAA,YAA8E;IAAAA,oDAAA,GAAoB;IAAAA,0DAAA,EAAI;IACtGA,4DAAA,eAAkG;IAAxBA,wDAAA,mBAAA0C,0FAAA;MAAA,MAAAC,KAAA,GAAA3C,2DAAA,CAAA4C,IAAA,EAAAC,KAAA;MAAA,MAAAvC,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAwC,UAAA,CAAAH,KAAA,CAAa;IAAA,EAAC;IAC/F3C,uDAAA,YAAwC;IAE5CA,0DADE,EAAO,EACH;;;;;IAPEA,uDAAA,EAA6B;IAA7BA,wDAAA,SAAAM,MAAA,CAAAyC,OAAA,CAAAV,QAAA,CAAAW,SAAA,EAA6B;IAC5BhD,uDAAA,EAA8B;IAA9BA,wDAAA,UAAAM,MAAA,CAAAyC,OAAA,CAAAV,QAAA,CAAAW,SAAA,EAA8B;IAEyChD,uDAAA,GAAoB;IAApBA,+DAAA,CAAAqC,QAAA,CAAAY,SAAA,CAAoB;;;;;IALtGjD,4DAAA,cAAiD;IAC/CA,wDAAA,IAAAkD,mEAAA,kBAAkG;IASpGlD,0DAAA,EAAM;;;;IATkBA,uDAAA,EAAiB;IAAjBA,wDAAA,YAAAM,MAAA,CAAA6C,YAAA,CAAiB;;;;;;IAarCnD,4DAAA,cACmC;IAAjCA,wDAAA,mBAAAoD,+FAAA;MAAApD,2DAAA,CAAAqD,IAAA;MAAA,MAAAC,QAAA,GAAAtD,2DAAA,GAAAiB,SAAA;MAAA,MAAAX,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAiD,UAAA,CAAAD,QAAA,CAAAE,KAAA,CAAsB;IAAA,EAAC;IADlCxD,0DAAA,EACmC;;;;IADuDA,wDAAA,QAAAsD,QAAA,CAAAE,KAAA,EAAAxD,2DAAA,CAAkB;;;;;;IAE5GA,4DAAA,eAEmC;IAAjCA,wDAAA,mBAAAyD,iGAAA;MAAAzD,2DAAA,CAAA0D,IAAA;MAAA,MAAAJ,QAAA,GAAAtD,2DAAA,GAAAiB,SAAA;MAAA,MAAAX,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAiD,UAAA,CAAAD,QAAA,CAAAE,KAAA,CAAsB;IAAA,EAAC;IAACxD,oDAAA,UAAG;IAAAA,0DAAA,EAAO;;;;;IAL/CA,4DAAA,cAA4G;IAG1GA,wDAFA,IAAA2D,yEAAA,kBACmC,IAAAC,0EAAA,mBAGA;IACnC5D,4DAAA,YAA8E;IAAAA,oDAAA,GAAoB;IACpGA,0DADoG,EAAI,EAClG;;;;;IANEA,uDAAA,EAA8B;IAA9BA,wDAAA,UAAAM,MAAA,CAAAuD,WAAA,CAAAP,QAAA,CAAAE,KAAA,EAA8B;IAE7BxD,uDAAA,EAA6B;IAA7BA,wDAAA,SAAAM,MAAA,CAAAuD,WAAA,CAAAP,QAAA,CAAAE,KAAA,EAA6B;IAG0CxD,uDAAA,GAAoB;IAApBA,+DAAA,CAAAsD,QAAA,CAAAL,SAAA,CAAoB;;;;;IAPtGjD,4DAAA,cAAgD;IAC9CA,wDAAA,IAAA8D,mEAAA,kBAA4G;IAQ9G9D,0DAAA,EAAM;;;;IARkBA,uDAAA,EAA2B;IAA3BA,wDAAA,YAAAM,MAAA,CAAAyD,aAAA,CAAAC,QAAA,CAA2B;;;;;;IAmBnDhE,4DAAA,iBAAuF;IAAnCA,wDAAA,mBAAAiE,yFAAA;MAAAjE,2DAAA,CAAAkE,IAAA;MAAA,MAAAC,OAAA,GAAAnE,2DAAA,GAAAoE,SAAA;MAAA,MAAA9D,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAA+D,mBAAA,CAAAF,OAAA,CAAwB;IAAA,EAAC;IAACnE,oDAAA,+BAAI;IAAAA,0DAAA,EAAS;;;;;;IAjExGA,4DADF,kBAAmD,qBACjC;IACdA,oDAAA,GACF;IAAAA,0DAAA,EAAiB;IAIbA,4DAHJ,uBAA2B,cAED,mBAC6E;IACjGA,oDAAA,iCACF;IAAAA,0DAAA,EAAQ;IACRA,4DAAA,qBAE4C;IADEA,8DAAA,2BAAAuE,2FAAAC,MAAA;MAAAxE,2DAAA,CAAAyE,GAAA;MAAA,MAAAnE,MAAA,GAAAN,2DAAA;MAAAA,gEAAA,CAAAM,MAAA,CAAAqE,iBAAA,CAAAjD,WAAA,EAAA8C,MAAA,MAAAlE,MAAA,CAAAqE,iBAAA,CAAAjD,WAAA,GAAA8C,MAAA;MAAA,OAAAxE,yDAAA,CAAAwE,MAAA;IAAA,EAA2C;IAE3FxE,0DAD8C,EAAa,EACrD;IAEJA,4DADF,cAAwB,iBACiE;IACrFA,oDAAA,kCACF;IAAAA,0DAAA,EAAQ;IACRA,4DAAA,iBACwB;IADqCA,8DAAA,2BAAA4E,uFAAAJ,MAAA;MAAAxE,2DAAA,CAAAyE,GAAA;MAAA,MAAAnE,MAAA,GAAAN,2DAAA;MAAAA,gEAAA,CAAAM,MAAA,CAAAqE,iBAAA,CAAAhD,YAAA,EAAA6C,MAAA,MAAAlE,MAAA,CAAAqE,iBAAA,CAAAhD,YAAA,GAAA6C,MAAA;MAAA,OAAAxE,yDAAA,CAAAwE,MAAA;IAAA,EAA4C;IAE3GxE,0DAFE,EACwB,EACpB;IAEJA,4DADF,eAAwB,iBACgD;IACpEA,oDAAA,kCACF;IAAAA,0DAAA,EAAQ;IACRA,wDAAA,KAAA6E,gEAAA,oBAC8B;IAC9B7E,4DAAA,oBAC2D;IADCA,wDAAA,oBAAA8E,gFAAAN,MAAA;MAAAxE,2DAAA,CAAAyE,GAAA;MAAA,MAAAnE,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAAUM,MAAA,CAAAyE,WAAA,CAAAP,MAAA,CAAmB;IAAA,EAAC;IAE5FxE,0DAFE,EAC2D,EACvD;IAENA,4DAAA,eAAkD;IAChDA,uDAAA,iBACQ;IAaRA,wDAZA,KAAAgF,6DAAA,kBAAiD,KAAAC,6DAAA,kBAYD;IAUlDjF,0DAAA,EAAM;IAEJA,4DADF,eAAkD,iBAEvB;IAAAA,oDAAA,gCAAI;IAAAA,0DAAA,EAAQ;IACrCA,4DAAA,oBACoF;IAA/CA,8DAAA,2BAAAkF,0FAAAV,MAAA;MAAAxE,2DAAA,CAAAyE,GAAA;MAAA,MAAAnE,MAAA,GAAAN,2DAAA;MAAAA,gEAAA,CAAAM,MAAA,CAAAqE,iBAAA,CAAAQ,cAAA,EAAAX,MAAA,MAAAlE,MAAA,CAAAqE,iBAAA,CAAAQ,cAAA,GAAAX,MAAA;MAAA,OAAAxE,yDAAA,CAAAwE,MAAA;IAAA,EAA8C;IACrFxE,0DADsF,EAAW,EAC3F;IAGJA,4DADF,eAA2C,kBAC4B;IAAvBA,wDAAA,mBAAAoF,gFAAA;MAAA,MAAAjB,OAAA,GAAAnE,2DAAA,CAAAyE,GAAA,EAAAL,SAAA;MAAA,MAAA9D,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAA+E,OAAA,CAAAlB,OAAA,CAAY;IAAA,EAAC;IAACnE,oDAAA,oBAAE;IAAAA,0DAAA,EAAS;IAChFA,wDAAA,KAAAsF,gEAAA,qBAAuF;IAG7FtF,0DAFI,EAAM,EACO,EACP;;;;IAnENA,uDAAA,GACF;IADEA,gEAAA,wEAAAM,MAAA,CAAAkF,KAAA,CAAAC,UAAA,cAAAnF,MAAA,CAAAkF,KAAA,CAAAE,MAAA,OACF;IAOgB1F,uDAAA,GAA0B;IAA6CA,wDAAvE,2BAA0B,wBAA4C,kBAAkB;IACtDA,8DAAA,YAAAM,MAAA,CAAAqE,iBAAA,CAAAjD,WAAA,CAA2C;IACvF1B,wDADwF,aAAAM,MAAA,CAAAsF,MAAA,CAAmB,uBACrF;IAMqC5F,uDAAA,GAA4C;IAA5CA,8DAAA,YAAAM,MAAA,CAAAqE,iBAAA,CAAAhD,YAAA,CAA4C;IACvG3B,wDAAA,aAAAM,MAAA,CAAAsF,MAAA,CAAmB;IAMZ5F,uDAAA,GAAoD;IAApDA,wDAAA,WAAAM,MAAA,CAAAsF,MAAA,IAAAtF,MAAA,CAAAyD,aAAA,CAAAlC,UAAA,WAAoD;IAE8B7B,uDAAA,EAAmB;IAAnBA,wDAAA,aAAAM,MAAA,CAAAsF,MAAA,CAAmB;IAO5E5F,uDAAA,GAAa;IAAbA,wDAAA,UAAAM,MAAA,CAAAsF,MAAA,CAAa;IAYb5F,uDAAA,EAAY;IAAZA,wDAAA,SAAAM,MAAA,CAAAsF,MAAA,CAAY;IAe5C5F,uDAAA,GAAmB;IAAnBA,wDAAA,aAAAM,MAAA,CAAAsF,MAAA,CAAmB;IAAgB5F,8DAAA,YAAAM,MAAA,CAAAqE,iBAAA,CAAAQ,cAAA,CAA8C;IAK9CnF,uDAAA,GAAa;IAAbA,wDAAA,UAAAM,MAAA,CAAAsF,MAAA,CAAa;;;ADtGpD,MAAOC,8BAA+B,SAAQ/F,yEAAa;EAC/DgG,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,KAAuB,EACvBC,qBAA2C,EAC3CC,aAA2B,EAE3BC,KAAqB,EACrBC,OAAuB,EACvBC,QAAkB,EAClBC,aAA2B;IACjC,KAAK,CAACR,MAAM,CAAC;IAVP,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,aAAa,GAAbA,aAAa;IAEb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,aAAa,GAAbA,aAAa;IAIvB,KAAApD,YAAY,GAAU,EAAE;IACxB,KAAAyC,MAAM,GAAG,KAAK;IACd,KAAAY,aAAa,GAAiB,CAC5B;MACE/G,KAAK,EAAE,CAAC;MACRgH,GAAG,EAAE,OAAO;MACZC,KAAK,EAAE;KACR,EACD;MACEjH,KAAK,EAAE,CAAC;MACRgH,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;KACR,CACF;IAEQ,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IA4NzB,KAAAC,YAAY,GAAU,EAAE;EAjPN;EA8BTC,QAAQA,CAAA;IAEf,IAAI,CAACZ,KAAK,CAACa,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QACjC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACG,WAAW,GAAGD,EAAE;QACrB,MAAME,QAAQ,GAAGL,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QAClC,MAAMI,GAAG,GAAGD,QAAQ,GAAG,CAACA,QAAQ,GAAG,CAAC;QACpC,IAAI,CAACE,OAAO,GAAGD,GAAG;QAClB,IAAI,CAACE,oBAAoB,EAAE;QAC3B,IAAI,CAACC,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EACJ;EAGAC,eAAeA,CAACvF,IAAU;IACxB,IAAIA,IAAI,IAAIA,IAAI,CAAC0B,QAAQ,CAACR,KAAK,EAAEsE,MAAM,CAACC,IAAI,CAACzF,IAAI,CAAC0B,QAAQ,CAACR,KAAK,EAAE,QAAQ,CAAC;EAC7E;EAKAmE,oBAAoBA,CAAA;IAClB,IAAI,CAACzB,qBAAqB,CAAC8B,6CAA6C,CAAC;MAAEC,IAAI,EAAE;QAC/EC,QAAQ,EAAE,IAAI,CAACR,OAAO;QACtBS,SAAS,EAAE,IAAI,CAACtB,SAAS;QACzBuB,QAAQ,EAAE,IAAI,CAACxB;;IAChB,CAAE,CAAC,CAACM,SAAS,CAACmB,GAAG,IAAG;MACnB,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAACC,iBAAiB,GAAGJ,GAAG,CAACE,OAAQ,IAAI,EAAE;QAC3C,IAAI,CAACzB,YAAY,GAAGuB,GAAG,CAACC,UAAU;MACpC;IACF,CAAC,CAAC;EACJ;EAEAV,YAAYA,CAAA;IACV,IAAI,CAACzB,aAAa,CAACuC,6BAA6B,CAAC;MAAET,IAAI,EAAE;QACvDU,QAAQ,EAAE,IAAI,CAACjB;;IAChB,CAAE,CAAC,CAACR,SAAS,CAACmB,GAAG,IAAG;MACnB,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAAChD,KAAK,GAAG6C,GAAG,CAACE,OAAO;QACxB,IAAI,CAACK,UAAU,GAAG,GAAG,IAAI,CAACpD,KAAK,CAACC,UAAU,IAAI,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG;MACpE;IACF,CAAC,CAAC;EACJ;EAIAmD,oBAAoBA,CAACC,GAAQ,EAAEC,gBAAqB;IAClD,IAAI,CAAC7C,qBAAqB,CAAC8C,6CAA6C,CAAC;MAAEf,IAAI,EAAEc;IAAgB,CAAE,CAAC,CAAC7B,SAAS,CAACmB,GAAG,IAAG;MACnH,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAACzE,aAAa,GAAGsE,GAAG,CAACE,OAAO;QAChC,IAAI,CAAC5D,iBAAiB,GAAG;UACvBQ,cAAc,EAAE,IAAI,CAACpB,aAAa,CAACoB,cAAc;UACjD8D,YAAY,EAAE,IAAI,CAAC1B,WAAW;UAC9B5F,YAAY,EAAE,IAAI,CAACoC,aAAa,CAACpC,YAAY;UAC7CgH,QAAQ,EAAE,IAAI,CAACjB,OAAO;UACtBwB,kBAAkB,EAAE;SACrB;QACD,IAAG,IAAI,CAACnF,aAAa,CAACrC,WAAW,EAAE;UACjC,IAAI,CAACiD,iBAAiB,CAACjD,WAAW,GAAG,IAAIyH,IAAI,CAAC,IAAI,CAACpF,aAAa,CAACrC,WAAW,CAAC;QAC/E;QACA,IAAI,CAACsE,aAAa,CAAC+B,IAAI,CAACe,GAAG,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ;EAIAzE,mBAAmBA,CAACyE,GAAQ;IAC1B,IAAI,CAACM,UAAU,EAAE;IACjB,IAAI,IAAI,CAACnD,KAAK,CAACoD,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACjD,OAAO,CAACkD,aAAa,CAAC,IAAI,CAACtD,KAAK,CAACoD,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAACnD,qBAAqB,CAACsD,0CAA0C,CAAC;MAAEvB,IAAI,EAAE,IAAI,CAACwB,WAAW;IAAE,CAAE,CAAC,CAACvC,SAAS,CAACmB,GAAG,IAAG;MAClH,IAAIA,GAAG,CAACG,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACnC,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAAC/B,oBAAoB,EAAE;QAC3BmB,GAAG,CAACa,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAChD,SAAS,GAAGgD,OAAO;IACxB,IAAI,CAAClC,oBAAoB,EAAE;EAC7B;EAGAhH,MAAMA,CAACmI,GAAQ;IACb,IAAI,CAAC3F,YAAY,GAAG,EAAE;IACtB,IAAI,CAACyC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACjB,iBAAiB,GAAG;MACvBQ,cAAc,EAAE,EAAE;MAClB8D,YAAY,EAAE,IAAI,CAAC1B,WAAW;MAC9B7F,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBgH,QAAQ,EAAE,IAAI,CAACjB,OAAO;MACtBwB,kBAAkB,EAAE;KACrB;IACD,IAAI,CAAClD,aAAa,CAAC+B,IAAI,CAACe,GAAG,CAAC;EAC9B;EAEA5H,MAAMA,CAAC4H,GAAQ,EAAEgB,aAAmB;IAClC,IAAI,CAAC3G,YAAY,GAAG,EAAE;IACtB,IAAI,CAACyC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACiD,oBAAoB,CAACC,GAAG,EAAEgB,aAAa,CAACf,gBAAgB,CAAC;EAChE;EAEAK,UAAUA,CAAA;IACR,IAAI,CAACnD,KAAK,CAAC8D,KAAK,EAAE;IAClB,IAAI,CAAC9D,KAAK,CAAC+D,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACrF,iBAAiB,CAACjD,WAAW,CAAC;IACjE,IAAI,CAACuE,KAAK,CAAC+D,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACrF,iBAAiB,CAAChD,YAAY,CAAC;IAClE,IAAI,CAACsE,KAAK,CAAC+D,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACrF,iBAAiB,CAACQ,cAAc,CAAC;EACtE;EAEA1D,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAO7B,mCAAM,CAAC6B,WAAW,CAAC,CAACuI,MAAM,CAAC,YAAY,CAAC;IACjD;IACA,OAAO,EAAE;EACX;EAEAC,gBAAgBA,CAACC,KAAY;IAC3B,KAAK,MAAMC,IAAI,IAAID,KAAK,EAAE;MACxB,OAAOC,IAAI,CAAC9H,IAAI;IAClB;IACA,OAAO6H,KAAK;EACd;EAEAV,WAAWA,CAAA;IACT,MAAMY,MAAM,GAAG;MACb,GAAG,IAAI,CAAC1F,iBAAiB;MACzBuE,kBAAkB,EAAE,IAAI,CAAC/F;KAC1B;IACD,IAAI,CAAC+G,gBAAgB,CAACG,MAAM,CAACnB,kBAAkB,CAAC;IAEhD,IAAI,IAAI,CAACvE,iBAAiB,CAACjD,WAAW,EAAE;MACtC2I,MAAM,CAAC3I,WAAW,GAAG,IAAI,CAACD,UAAU,CAAC,IAAI,CAACkD,iBAAiB,CAACjD,WAAW,CAAC;IAC1E;IACA,OAAO2I,MAAM;EACf;EAEAhF,OAAOA,CAACyD,GAAQ;IACdA,GAAG,CAACa,KAAK,EAAE;EACb;EAEAW,kBAAkBA,CAACC,YAAiB;IAClC,MAAMC,WAAW,GAAGD,YAAY,CAACE,OAAO,CAAC,GAAG,CAAC;IAC7C,IAAID,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,OAAOD,YAAY,CAACG,SAAS,CAACF,WAAW,GAAG,CAAC,CAAC;IAChD;IACA,OAAOD,YAAY;EACrB;EAEAxF,WAAWA,CAAC4F,KAAU;IACpB,MAAMC,KAAK,GAAaD,KAAK,CAACE,MAAM,CAACD,KAAK;IAC1C,IAAIA,KAAK,IAAIA,KAAK,CAACtB,MAAM,GAAG,CAAC,EAAE;MAC7B,MAAMwB,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,CAAC;MACnE,MAAMC,SAAS,GAAG,mBAAmB;MACrC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAACtB,MAAM,EAAE0B,CAAC,EAAE,EAAE;QACrC,MAAMC,IAAI,GAAGL,KAAK,CAACI,CAAC,CAAC;QACrB,IAAI,CAACD,SAAS,CAACG,IAAI,CAACD,IAAI,CAACE,IAAI,CAAC,EAAE;UAC9B,IAAI,CAAC9E,OAAO,CAAC+E,YAAY,CAAC,kBAAkB,CAAC;QAC/C;QACA,IAAIN,YAAY,CAACO,QAAQ,CAACJ,IAAI,CAACE,IAAI,CAAC,EAAE;UACpC,MAAMG,MAAM,GAAG,IAAIC,UAAU,EAAE;UAE/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;YACzB,MAAMC,QAAQ,GAAGT,IAAI,CAACE,IAAI,CAACQ,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC;YACvD,IAAI,CAACxI,YAAY,CAACyI,IAAI,CAAC;cACrBtJ,IAAI,EAAEmJ,CAAC,CAACZ,MAAM,CAACR,MAAM;cACrBwB,UAAU,EAAE,IAAI,CAACvB,kBAAkB,CAACmB,CAAC,CAACZ,MAAM,CAACR,MAAM,CAAC;cACpDpH,SAAS,EAAEgI,IAAI,CAACa,IAAI;cACpB9I,SAAS,EAAE0I;aACZ,CAAC;YAEF,IAAI,IAAI,CAACvI,YAAY,CAACmG,MAAM,KAAKsB,KAAK,CAACtB,MAAM,EAAE;cAC7CyC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC7I,YAAY,CAAC;cACnD,IAAI,IAAI,CAAC8I,SAAS,EAAE;gBAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACzM,KAAK,GAAG,IAAI;cAC3C;YACF;UACF,CAAC;UACD6L,MAAM,CAACa,aAAa,CAAClB,IAAI,CAAC;QAC5B;MACF;IACF;EACF;EAGApH,WAAWA,CAACuI,GAAQ;IAClB,IAAGA,GAAG,EAAE;MACN,OAAOA,GAAG,CAACC,WAAW,EAAE,CAACC,QAAQ,CAAC,MAAM,CAAC;IAC3C;IACA,OAAO,KAAK;EACd;EAEAvJ,OAAOA,CAAC2I,QAAgB;IACtB,OAAOA,QAAQ,KAAK,CAAC;EACvB;EAGAa,KAAKA,CAACC,SAAiB;IACrB,OAAOA,SAAS,CAACH,WAAW,EAAE,KAAK,KAAK;EAC1C;EAIAvJ,UAAUA,CAACD,KAAa;IACtB,IAAI,CAACM,YAAY,CAACsJ,MAAM,CAAC5J,KAAK,EAAE,CAAC,CAAC;EACpC;EAEA6J,WAAWA,CAACC,SAAiB;IAC3B,IAAI,CAAC5F,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC6F,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACvF,EAAE,IAAIqF,SAAS,CAAC;EACtE;EAEAG,WAAWA,CAAChE,GAAQ,GACpB;EAEAiE,UAAUA,CAACpC,KAAU,EAAE9H,KAAa;IAClC,IAAImK,IAAI,GAAG,IAAI,CAACjG,YAAY,CAAClE,KAAK,CAAC,CAACW,KAAK,CAACyJ,KAAK,CAAC,CAAC,EAAE,IAAI,CAAClG,YAAY,CAAClE,KAAK,CAAC,CAACW,KAAK,CAAC0J,IAAI,EAAE,IAAI,CAACnG,YAAY,CAAClE,KAAK,CAAC,CAACW,KAAK,CAAC2H,IAAI,CAAC;IAC5H,IAAIgC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACJ,IAAI,CAAC,EAAE,GAAGrC,KAAK,CAACE,MAAM,CAACpL,KAAK,GAAG,GAAG,GAAG,IAAI,CAACsH,YAAY,CAAClE,KAAK,CAAC,CAAC2J,SAAS,EAAE,EAAE;MAAErB,IAAI,EAAE,IAAI,CAACpE,YAAY,CAAClE,KAAK,CAAC,CAACW,KAAK,CAAC2H;IAAI,CAAE,CAAC;IACjJ,IAAI,CAACpE,YAAY,CAAClE,KAAK,CAAC,CAACW,KAAK,GAAG2J,OAAO;EAC1C;EAEAE,MAAMA,CAAA;IACJ,IAAI,CAAC9G,aAAa,CAACqF,IAAI,CAAC;MACtB0B,MAAM;MACNC,OAAO,EAAE,IAAI,CAAChG;KACf,CAAC;IACF,IAAI,CAACjB,QAAQ,CAACkH,IAAI,EAAE;EACtB;EACAjK,UAAUA,CAACkK,GAAQ;IACjB,IAAGA,GAAG,EAAE3F,MAAM,CAACC,IAAI,CAAC0F,GAAG,EAAE,QAAQ,CAAC;EACpC;;;uCAzRW5H,8BAA8B,EAAA7F,+DAAA,CAAA2N,0EAAA,GAAA3N,+DAAA,CAAA6N,4DAAA,GAAA7N,+DAAA,CAAA+N,oFAAA,GAAA/N,+DAAA,CAAAiO,2EAAA,GAAAjO,+DAAA,CAAAiO,mEAAA,GAAAjO,+DAAA,CAAAoO,4DAAA,GAAApO,+DAAA,CAAAsO,mFAAA,GAAAtO,+DAAA,CAAAwO,sDAAA,GAAAxO,+DAAA,CAAA0O,+EAAA;IAAA;EAAA;;;YAA9B7I,8BAA8B;MAAA+I,SAAA;MAAAC,SAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCxBzC/O,4DADF,iBAA0B,qBACR;UACdA,oDAAA,GACF;UAAAA,0DAAA,EAAiB;UAEfA,4DADF,mBAAc,YACyB;UAAAA,oDAAA,iPAAuC;UAAAA,0DAAA,EAAK;UAK7EA,4DAHJ,aAA8B,aAEL,aAC0B;UAC7CA,wDAAA,IAAAiP,gDAAA,oBAAoF;UAI1FjP,0DAFI,EAAM,EACF,EACF;UAMEA,4DAJR,cAAmC,iBAC+D,aACvF,cACoE,cACzC;UAAAA,oDAAA,oBAAE;UAAAA,0DAAA,EAAK;UACrCA,4DAAA,cAA8B;UAAAA,oDAAA,gCAAI;UAAAA,0DAAA,EAAK;UACvCA,4DAAA,cAA8B;UAAAA,oDAAA,gCAAI;UAAAA,0DAAA,EAAK;UACvCA,4DAAA,cAA8B;UAAAA,oDAAA,gCAAI;UAAAA,0DAAA,EAAK;UACvCA,4DAAA,cAA8B;UAAAA,oDAAA,gCAAI;UAAAA,0DAAA,EAAK;UACvCA,4DAAA,cAA8B;UAAAA,oDAAA,oBAAE;UAEpCA,0DAFoC,EAAK,EAClC,EACC;UACRA,4DAAA,aAAO;UACLA,wDAAA,KAAAkP,6CAAA,kBAA+E;UAcvFlP,0DAHM,EAAQ,EACF,EACJ,EACO;UAEbA,4DADF,0BAAsD,0BAES;UAD7CA,8DAAA,wBAAAmP,8EAAA3K,MAAA;YAAAxE,2DAAA,CAAAoP,GAAA;YAAApP,gEAAA,CAAAgP,GAAA,CAAAnI,SAAA,EAAArC,MAAA,MAAAwK,GAAA,CAAAnI,SAAA,GAAArC,MAAA;YAAA,OAAAxE,yDAAA,CAAAwE,MAAA;UAAA,EAAoB;UAClCxE,wDAAA,wBAAAmP,8EAAA3K,MAAA;YAAAxE,2DAAA,CAAAoP,GAAA;YAAA,OAAApP,yDAAA,CAAcgP,GAAA,CAAApF,WAAA,CAAApF,MAAA,CAAmB;UAAA,EAAC;UAEtCxE,0DADE,EAAiB,EACF;UAGbA,4DAFJ,sBAAgB,eAC6B,kBACmB;UAAnBA,wDAAA,mBAAAqP,iEAAA;YAAArP,2DAAA,CAAAoP,GAAA;YAAA,OAAApP,yDAAA,CAASgP,GAAA,CAAA3B,MAAA,EAAQ;UAAA,EAAC;UACzDrN,oDAAA,wCACF;UAGNA,0DAHM,EAAS,EACL,EACS,EACT;UAGVA,wDAAA,KAAAsP,sDAAA,kCAAAtP,oEAAA,CAAiE;;;UA1D7DA,uDAAA,GACF;UADEA,gEAAA,wEAAAgP,GAAA,CAAApG,UAAA,MACF;UAQ4E5I,uDAAA,GAAc;UAAdA,wDAAA,SAAAgP,GAAA,CAAAS,QAAA,CAAc;UAmB7DzP,uDAAA,IAAuB;UAAvBA,wDAAA,YAAAgP,GAAA,CAAAvG,iBAAA,CAAuB;UAgBlCzI,uDAAA,GAAoB;UAApBA,8DAAA,SAAAgP,GAAA,CAAAnI,SAAA,CAAoB;UAAuB7G,wDAAtB,aAAAgP,GAAA,CAAApI,QAAA,CAAqB,mBAAAoI,GAAA,CAAAlI,YAAA,CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7CkC;AAEnF;AACmB;AACuB;AAQnB;AAEnC;AACiD;AAExB;AACR;AACE;AACR;AACU;AACJ;AAE2B;;;;;;;;;;;;;;;;;;ICuB/D9G,6DADF,aAAoE,aAChD;IAAAA,qDAAA,GAAwB;IAAAA,2DAAA,EAAK;IAC/CA,6DAAA,aAAkB;IAAAA,qDAAA,GAAoB;IAAAA,2DAAA,EAAK;IAEzCA,6DADF,aAAkB,iBAEgB;IAAhCA,yDAAA,mBAAA0Q,wEAAA;MAAA,MAAAC,OAAA,GAAA3Q,4DAAA,CAAA4Q,GAAA,EAAA3P,SAAA;MAAA,MAAA4P,MAAA,GAAA7Q,4DAAA;MAAA,OAAAA,0DAAA,CAAS6Q,MAAA,CAAAhJ,eAAA,CAAA8I,OAAA,CAAqB;IAAA,EAAC;IAAC3Q,qDAAA,mBAAE;IAEtCA,2DAFsC,EAAS,EACxC,EACF;;;;IANeA,wDAAA,GAAwB;IAAxBA,gEAAA,CAAA2Q,OAAA,CAAAG,aAAA,CAAwB;IACxB9Q,wDAAA,GAAoB;IAApBA,gEAAA,CAAA2Q,OAAA,CAAAI,SAAA,CAAoB;IAEe/Q,wDAAA,GAAkD;IAAlDA,yDAAA,cAAA2Q,OAAA,CAAAK,UAAA,KAAAL,OAAA,CAAAM,WAAA,CAAkD;;;;;;IAwCzGjR,6DADF,cAA0D,eAC5B;IAAAA,qDAAA,GAAc;IAAAA,2DAAA,EAAO;IACjDA,6DAAA,iBAAoF;IAA9DA,yDAAA,mBAAAkR,wFAAA;MAAAlR,4DAAA,CAAAyE,GAAA;MAAA,MAAAoM,MAAA,GAAA7Q,4DAAA;MAAA,OAAAA,0DAAA,CAAS6Q,MAAA,CAAAM,SAAA,EAAW;IAAA,EAAC;IACzCnR,wDAAA,YAAiC;IAErCA,2DADE,EAAS,EACL;;;;IAJwBA,wDAAA,GAAc;IAAdA,gEAAA,CAAA6Q,MAAA,CAAAO,QAAA,CAAc;;;;;;IAjBlDpR,6DADF,kBAAgD,qBAC9B;IACdA,qDAAA,GACF;IAAAA,2DAAA,EAAiB;IAIXA,6DAHN,uBAA2B,cAC2B,cACf,gBAC8E;IAAAA,qDAAA,oBAC/G;IACFA,2DADE,EAAQ,EACJ;IAEJA,6DADF,cAAiD,gBAEW;IAAlCA,yDAAA,oBAAAqR,iFAAA7M,MAAA;MAAAxE,4DAAA,CAAAsR,GAAA;MAAA,MAAAT,MAAA,GAAA7Q,4DAAA;MAAA,OAAAA,0DAAA,CAAU6Q,MAAA,CAAAU,cAAA,CAAA/M,MAAA,CAAsB;IAAA,EAAC;IADzDxE,2DAAA,EAC0D;IAC1DA,6DAAA,iBAC8F;IAC5FA,wDAAA,aAA+C;IAACA,qDAAA,sBAClD;IAAAA,2DAAA,EAAQ;IACRA,yDAAA,KAAAwR,+DAAA,kBAA0D;IAO9DxR,2DADE,EAAM,EACF;IAGJA,6DADF,eAAwB,iBACoF;IACxGA,qDAAA,kCACF;IAAAA,2DAAA,EAAQ;IACRA,6DAAA,iBAA2F;IAA9BA,+DAAA,2BAAAyR,yFAAAjN,MAAA;MAAAxE,4DAAA,CAAAsR,GAAA;MAAA,MAAAT,MAAA,GAAA7Q,4DAAA;MAAAA,iEAAA,CAAA6Q,MAAA,CAAAC,aAAA,EAAAtM,MAAA,MAAAqM,MAAA,CAAAC,aAAA,GAAAtM,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAA2B;IAC1FxE,2DADE,EAA2F,EACvF;IAGJA,6DADF,eAAkD,iBAC4B;IAAAA,qDAAA,iCAC1E;IAAAA,6DAAA,aAAsB;IAAAA,qDAAA,wDAAQ;IAChCA,2DADgC,EAAI,EAC5B;IAERA,6DAAA,oBAC+B;IAA7BA,+DAAA,2BAAA0R,4FAAAlN,MAAA;MAAAxE,4DAAA,CAAAsR,GAAA;MAAA,MAAAT,MAAA,GAAA7Q,4DAAA;MAAAA,iEAAA,CAAA6Q,MAAA,CAAA1L,cAAA,EAAAX,MAAA,MAAAqM,MAAA,CAAA1L,cAAA,GAAAX,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAA4B;IAC9BxE,qDAAA;IACFA,2DADE,EAAW,EACP;IAGJA,6DADF,eAAkD,iBAC2B;IAAAA,qDAAA,iCACzE;IAAAA,6DAAA,aAAsB;IAAAA,qDAAA,wDAAQ;IAChCA,2DADgC,EAAI,EAC5B;IACRA,6DAAA,oBACsB;IAApBA,+DAAA,2BAAA2R,4FAAAnN,MAAA;MAAAxE,4DAAA,CAAAsR,GAAA;MAAA,MAAAT,MAAA,GAAA7Q,4DAAA;MAAAA,iEAAA,CAAA6Q,MAAA,CAAAe,KAAA,EAAApN,MAAA,MAAAqM,MAAA,CAAAe,KAAA,GAAApN,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAAmB;IACrBxE,qDAAA;IAEJA,2DAFI,EAAW,EACP,EACO;IAEbA,6DADF,0BAAsD,kBACgB;IAAtBA,yDAAA,mBAAA6R,kFAAA;MAAA,MAAAC,MAAA,GAAA9R,4DAAA,CAAAsR,GAAA,EAAAlN,SAAA;MAAA,OAAApE,0DAAA,CAAS8R,MAAA,CAAAnI,KAAA,EAAW;IAAA,EAAC;IAAC3J,qDAAA,oBAAE;IAAAA,2DAAA,EAAS;IAC/EA,6DAAA,kBAAoE;IAAhCA,yDAAA,mBAAA+R,kFAAA;MAAA,MAAAD,MAAA,GAAA9R,4DAAA,CAAAsR,GAAA,EAAAlN,SAAA;MAAA,MAAAyM,MAAA,GAAA7Q,4DAAA;MAAA,OAAAA,0DAAA,CAAS6Q,MAAA,CAAAmB,gBAAA,CAAAF,MAAA,CAAqB;IAAA,EAAC;IAAC9R,qDAAA,4CAAM;IAE9EA,2DAF8E,EAAS,EACpE,EACT;;;;IAtDNA,wDAAA,GACF;IADEA,iEAAA,wEAAA6Q,MAAA,CAAAoB,SAAA,CAAAxM,UAAA,OAAAoL,MAAA,CAAAoB,SAAA,CAAAvM,MAAA,OACF;IAcgD1F,wDAAA,IAAc;IAAdA,yDAAA,SAAA6Q,MAAA,CAAAO,QAAA,CAAc;IAaGpR,wDAAA,GAA2B;IAA3BA,+DAAA,YAAA6Q,MAAA,CAAAC,aAAA,CAA2B;IAStF9Q,wDAAA,GAA4B;IAA5BA,+DAAA,YAAA6Q,MAAA,CAAA1L,cAAA,CAA4B;IAS5BnF,wDAAA,GAAmB;IAAnBA,+DAAA,YAAA6Q,MAAA,CAAAe,KAAA,CAAmB;;;ADxEvB,MAAOM,gCAAiC,SAAQpS,yEAAa;EAsBjEgG,YACUC,MAAmB,EACnBoM,UAAsB,EACtBnM,aAA8B,EAC9BK,OAAuB,EACvBJ,KAAuB,EACvBmM,oBAA0C,EAC1CC,OAAsB,EACtBC,MAAc,EACdlM,KAAqB,EACrBmM,UAAsB,EACtBhM,aAA2B,EAC3BD,QAAkB,EAClBH,aAA2B;IAEnC,KAAK,CAACJ,MAAM,CAAC;IAdL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAoM,UAAU,GAAVA,UAAU;IACV,KAAAnM,aAAa,GAAbA,aAAa;IACb,KAAAK,OAAO,GAAPA,OAAO;IACP,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAmM,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAlM,KAAK,GAALA,KAAK;IACL,KAAAmM,UAAU,GAAVA,UAAU;IACV,KAAAhM,aAAa,GAAbA,aAAa;IACb,KAAAD,QAAQ,GAARA,QAAQ;IACR,KAAAH,aAAa,GAAbA,aAAa;IA9BvB,KAAAqM,eAAe,GAAoB;MACjCC,OAAO,EAAE,CACPrC,kEAAiB,EACjBC,8DAAa,EACbC,+DAAc,EACdC,2DAAU,EACVD,+DAAc,EACdE,gEAAe,CAChB;MACDkC,MAAM,EAAE,OAAO;MACfC,aAAa,EAAE;QACbC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,OAAO;QACfC,KAAK,EAAE;;KAEV;IAwBD,KAAA7H,IAAI,GAAgB,IAAI;IAIxB;IACA,KAAA8H,sBAAsB,GAA2B,EAAE;IACnD,KAAAC,qBAAqB,GAAmD,EAAE;IAE1E;IACA,KAAAC,YAAY,GAAuB,EAAE;IAfnC,IAAI,CAACC,OAAO,GAAG,IAAI/J,IAAI,EAAE;EAC3B;EAiBSnC,QAAQA,CAAA;IACf,IAAI,CAACZ,KAAK,CAACa,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QACjC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACG,WAAW,GAAGD,EAAE;QACrB,MAAME,QAAQ,GAAGL,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QAClC,MAAMI,GAAG,GAAGD,QAAQ,GAAG,CAACA,QAAQ,GAAG,CAAC;QACpC,IAAI,CAAC2L,cAAc,GAAG1L,GAAG;QACzB,IAAI,CAAC2L,OAAO,EAAE;QACd,IAAI,CAACxL,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EACJ;EAEAjH,MAAMA,CAACmI,GAAQ;IACb,IAAI,CAAC3D,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAC2L,aAAa,GAAG,IAAI;IACzB,IAAI,CAACc,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC3G,IAAI,GAAG,IAAI;IAChB,IAAI,CAACjF,aAAa,CAAC+B,IAAI,CAACe,GAAG,CAAC;EAC9B;EAEAjB,eAAeA,CAACvF,IAAsB;IACpC,IAAIA,IAAI,EAAE;MACR,IAAIA,IAAI,CAACyO,SAAS,IAAIzO,IAAI,CAAC+Q,KAAK,EAAE;QAChCvL,MAAM,CAACC,IAAI,CAACzF,IAAI,CAAC0O,UAAW,EAAE,QAAQ,CAAC;MACzC,CAAC,MACI;QACHlJ,MAAM,CAACC,IAAI,CAACzF,IAAI,CAAC2O,WAAY,EAAE,QAAQ,CAAC;MAC1C;IACF;EACF;EAEA5D,MAAMA,CAAA;IACJ,IAAI,CAAC9G,aAAa,CAACqF,IAAI,CAAC;MACtB0B,MAAM;MACNC,OAAO,EAAE,IAAI,CAAChG;KACf,CAAC;IACF,IAAI,CAACjB,QAAQ,CAACkH,IAAI,EAAE;EACtB;EAEA+D,cAAcA,CAAC5G,KAAU;IACvB,MAAMM,IAAI,GAASN,KAAK,CAACE,MAAM,CAACD,KAAK,CAAC,CAAC,CAAC;IACxC,MAAMG,SAAS,GAAG,MAAM;IACxB,IAAI,CAACA,SAAS,CAACG,IAAI,CAACD,IAAI,CAACE,IAAI,CAAC,EAAE;MAC9B,IAAI,CAAC9E,OAAO,CAAC+E,YAAY,CAAC,oBAAoB,CAAC;MAC/C;IACF;IACA,IAAIH,IAAI,EAAE;MACR,MAAMH,YAAY,GAAG,CAAC,iBAAiB,CAAC;MACxC,IAAIA,YAAY,CAACO,QAAQ,CAACJ,IAAI,CAACE,IAAI,CAAC,EAAE;QACpC,IAAI,CAACiG,QAAQ,GAAGnG,IAAI,CAACa,IAAI;QACzB,IAAI,CAACb,IAAI,GAAGA,IAAI;MAClB;IACF;EACF;EAEAkG,SAASA,CAAA;IACP,IAAI,IAAI,CAAClG,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,GAAG,IAAI;MAChB,IAAI,CAACmG,QAAQ,GAAG,IAAI;IACtB;EACF;EAEAxJ,YAAYA,CAAA;IACV,IAAI,CAACzB,aAAa,CAACuC,6BAA6B,CAAC;MAC/CT,IAAI,EAAE;QAAEU,QAAQ,EAAE,IAAI,CAACwK;MAAc;KACtC,CAAC,CAACjM,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACyJ,SAAS,GAAG5J,GAAG,CAACE,OAAO;MAC9B;IACF,CAAC,CAAC;EACJ;EAEA6K,OAAOA,CAAA;IACL,IAAI,CAACL,sBAAsB,CAAC3K,QAAQ,GAAG,IAAI,CAACxB,QAAQ;IACpD,IAAI,CAACmM,sBAAsB,CAAC5K,SAAS,GAAG,IAAI,CAACtB,SAAS;IACtD,IAAI,IAAI,CAACsM,cAAc,IAAI,CAAC,EAAE;MAC5B,IAAI,CAACJ,sBAAsB,CAACpK,QAAQ,GAAG,IAAI,CAACwK,cAAc;MAC1D,IAAI,CAACf,oBAAoB,CAACkB,+CAA+C,CAAC;QAAErL,IAAI,EAAE,IAAI,CAAC8K;MAAsB,CAAE,CAAC,CAC7GQ,IAAI,EAAE,CACNrM,SAAS,CAACmB,GAAG,IAAG;QACf,IAAIA,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;UACvB,IAAIH,GAAG,CAACE,OAAO,EAAE;YACf,IAAI,CAAC0K,YAAY,GAAG5K,GAAG,CAACE,OAAO;YAC/B,IAAI,CAACzB,YAAY,GAAGuB,GAAG,CAACC,UAAW;YACnC,IAAI,IAAI,CAAC2K,YAAY,EAAE;cACrB,KAAK,IAAIjI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACiI,YAAY,CAAC3J,MAAM,EAAE0B,CAAC,EAAE,EAAE;gBACjD,IAAI,IAAI,CAACiI,YAAY,CAACjI,CAAC,CAAC,CAAC+F,SAAS,EAChC,IAAI,CAACkC,YAAY,CAACjI,CAAC,CAAC,CAAC+F,SAAS,GAAGlR,mCAAM,CAAC,IAAI,CAACoT,YAAY,CAACjI,CAAC,CAAC,CAAC+F,SAAS,CAAC,CAAC9G,MAAM,CAAC,oBAAoB,CAAC;cACxG;YACF;UACF;QACF;MACF,CAAC,CAAC;IACN;EACF;EAEA+H,gBAAgBA,CAAClJ,GAAQ;IACvB,IAAI,CAACM,UAAU,EAAE;IACjB,IAAI,IAAI,CAACnD,KAAK,CAACoD,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACjD,OAAO,CAACkD,aAAa,CAAC,IAAI,CAACtD,KAAK,CAACoD,aAAa,CAAC;MACpD;IACF;IACA,IAAI,CAAC+I,oBAAoB,CAACoB,uCAAuC,CAAC;MAChEvL,IAAI,EAAE;QACJU,QAAQ,EAAE,IAAI,CAACwK,cAAc;QAC7BlK,YAAY,EAAE,IAAI,CAAC1B,WAAW;QAC9BuJ,aAAa,EAAE,IAAI,CAACA,aAAc;QAClC3L,cAAc,EAAE,IAAI,CAACA,cAAe;QACpCyM,KAAK,EAAE,IAAI,CAACA,KAAM;QAClBpO,KAAK,EAAE,IAAI,CAACyH;;KAEf,CAAC,CAAC/D,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC4K,OAAO,EAAE;QACd,IAAI,CAAC/M,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;QAClCZ,GAAG,CAACa,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACtD,OAAO,CAAC+E,YAAY,CAAC/C,GAAG,CAACoL,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;EACJ;EAEAC,aAAaA,CAACpR,IAAiC,EAAEqR,QAAA,GAAmB,0BAA0B;IAC5F,IAAIrR,IAAI,YAAYsR,WAAW,EAAE;MAC/B,OAAO,IAAIC,IAAI,CAAC,CAACvR,IAAI,CAAC,EAAE;QAAE6I,IAAI,EAAEwI;MAAQ,CAAE,CAAC;IAC7C,CAAC,MAAM,IAAI,OAAOrR,IAAI,KAAK,QAAQ,EAAE;MACnC,OAAO,IAAIuR,IAAI,CAAC,CAACvR,IAAI,CAAC,EAAE;QAAE6I,IAAI,EAAEwI;MAAQ,CAAE,CAAC;IAC7C,CAAC,MAAM;MACL,OAAOG,SAAS;IAClB;EACF;EAEA1K,UAAUA,CAAA;IACR,IAAI,CAACnD,KAAK,CAAC8D,KAAK,EAAE;IAClB,IAAI,CAAC9D,KAAK,CAAC+D,QAAQ,CAAC,WAAW,EAAE,IAAI,CAACiB,IAAI,CAAC;IAC3C,IAAI,CAAChF,KAAK,CAAC+D,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC8G,aAAa,CAAC;IACjD,IAAI,CAAC7K,KAAK,CAAC+D,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC7E,cAAc,CAAC;IAClD,IAAI,CAACc,KAAK,CAAC+D,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC4H,KAAK,CAAC;EAC7C;;;uCArMWM,gCAAgC,EAAAlS,gEAAA,CAAA2N,0EAAA,GAAA3N,gEAAA,CAAA6N,wEAAA,GAAA7N,gEAAA,CAAA+N,4DAAA,GAAA/N,gEAAA,CAAAiO,mFAAA,GAAAjO,gEAAA,CAAAoO,oFAAA,GAAApO,gEAAA,CAAAsO,2EAAA,GAAAtO,gEAAA,CAAAwO,8EAAA,GAAAxO,gEAAA,CAAA0O,oDAAA,GAAA1O,gEAAA,CAAA0O,4DAAA,GAAA1O,gEAAA,CAAAA,sDAAA,GAAAA,gEAAA,CAAAoU,+EAAA,GAAApU,gEAAA,CAAAqU,sDAAA,GAAArU,gEAAA,CAAAsO,mEAAA;IAAA;EAAA;;;YAAhC4D,gCAAgC;MAAAtD,SAAA;MAAAC,SAAA,WAAAyF,uCAAAvF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UC9CzC/O,6DAHJ,iBAA0B,qBACR,aAEgB;UAAAA,qDAAA,sEAAa;UAC7CA,2DAD6C,EAAM,EAClC;UAKTA,6DAJR,sBAA+B,aACT,aACD,aACyC,cACX;UACzCA,qDAAA,iCACF;UAAAA,2DAAA,EAAO;UACPA,6DAAA,qBAEsB;UADpBA,+DAAA,2BAAAuU,+EAAA/P,MAAA;YAAAxE,4DAAA,CAAAoP,GAAA;YAAApP,iEAAA,CAAAgP,GAAA,CAAA+D,sBAAA,CAAAyB,UAAA,EAAAhQ,MAAA,MAAAwK,GAAA,CAAA+D,sBAAA,CAAAyB,UAAA,GAAAhQ,MAAA;YAAA,OAAAxE,0DAAA,CAAAwE,MAAA;UAAA,EAA+C;UAC3BxE,2DAAA,EAAa;UACnCA,6DAAA,eAAgD;UAAAA,qDAAA,SAAC;UAAAA,2DAAA,EAAO;UACxDA,6DAAA,qBAEsB;UADpBA,+DAAA,2BAAAyU,+EAAAjQ,MAAA;YAAAxE,4DAAA,CAAAoP,GAAA;YAAApP,iEAAA,CAAAgP,GAAA,CAAA+D,sBAAA,CAAA2B,QAAA,EAAAlQ,MAAA,MAAAwK,GAAA,CAAA+D,sBAAA,CAAA2B,QAAA,GAAAlQ,MAAA;YAAA,OAAAxE,0DAAA,CAAAwE,MAAA;UAAA,EAA6C;UAEjDxE,2DADwB,EAAa,EAC/B;UAEJA,6DADF,eAAmD,kBACK;UAApBA,yDAAA,mBAAA2U,mEAAA;YAAA3U,4DAAA,CAAAoP,GAAA;YAAA,OAAApP,0DAAA,CAASgP,GAAA,CAAAoE,OAAA,EAAS;UAAA,EAAC;UAACpT,wDAAA,aAAkC;UAAAA,qDAAA,oBAAE;UAE9FA,2DAF8F,EAAS,EAC/F,EACF;UAIAA,6DAHN,cAAiB,eACQ,eAC0B,kBACuB;UAAvCA,yDAAA,mBAAA4U,mEAAA;YAAA5U,4DAAA,CAAAoP,GAAA;YAAA,MAAAyF,uBAAA,GAAA7U,0DAAA;YAAA,OAAAA,0DAAA,CAASgP,GAAA,CAAArO,MAAA,CAAAkU,uBAAA,CAA4B;UAAA,EAAC;UACjE7U,qDAAA,kCACF;UAKVA,2DALU,EAAS,EACL,EACF,EACF,EACF,EACO;UAOHA,6DANZ,uBAA+B,cACT,eACY,iBACmE,aACtF,cAC4D,cACjC;UAAAA,qDAAA,gCAAI;UAAAA,2DAAA,EAAK;UACvCA,6DAAA,cAA8B;UAAAA,qDAAA,4CAAM;UAAAA,2DAAA,EAAK;UACzCA,6DAAA,cAA8B;UAAAA,qDAAA,oBAAE;UAEpCA,2DAFoC,EAAK,EAClC,EACC;UACRA,6DAAA,aAAO;UACLA,yDAAA,KAAA8U,+CAAA,iBAAoE;UAU1E9U,2DAFI,EAAQ,EACF,EACJ;UACNA,6DAAA,0BAC2B;UADqBA,+DAAA,wBAAA+U,gFAAAvQ,MAAA;YAAAxE,4DAAA,CAAAoP,GAAA;YAAApP,iEAAA,CAAAgP,GAAA,CAAAnI,SAAA,EAAArC,MAAA,MAAAwK,GAAA,CAAAnI,SAAA,GAAArC,MAAA;YAAA,OAAAxE,0DAAA,CAAAwE,MAAA;UAAA,EAAoB;UAClExE,yDAAA,wBAAA+U,gFAAA;YAAA/U,4DAAA,CAAAoP,GAAA;YAAA,OAAApP,0DAAA,CAAcgP,GAAA,CAAAoE,OAAA,EAAS;UAAA,EAAC;UAG9BpT,2DAFI,EAAiB,EACb,EACO;UAGXA,6DAFJ,sBAAgB,eAC6B,kBACoB;UAAnBA,yDAAA,mBAAAgV,mEAAA;YAAAhV,4DAAA,CAAAoP,GAAA;YAAA,OAAApP,0DAAA,CAASgP,GAAA,CAAA3B,MAAA,EAAQ;UAAA,EAAC;UAC1DrN,qDAAA,wCACF;UAGNA,2DAHM,EAAS,EACL,EACS,EACT;UAEVA,yDAAA,KAAAiV,wDAAA,iCAAAjV,qEAAA,CAAkE;;;UA5D5CA,wDAAA,IAAmB;UAAnBA,yDAAA,oBAAmB;UAC7BA,+DAAA,YAAAgP,GAAA,CAAA+D,sBAAA,CAAAyB,UAAA,CAA+C;UAC/CxU,yDAAA,YAAAgP,GAAA,CAAAkE,OAAA,CAAmB;UAETlT,wDAAA,GAAmB;UAAnBA,yDAAA,oBAAmB;UAC7BA,+DAAA,YAAAgP,GAAA,CAAA+D,sBAAA,CAAA2B,QAAA,CAA6C;UAC7C1U,yDAAA,YAAAgP,GAAA,CAAAkE,OAAA,CAAmB;UA6BElT,wDAAA,IAAiB;UAAjBA,yDAAA,YAAAgP,GAAA,CAAAiE,YAAA,CAAiB;UAW5BjT,wDAAA,EAA+B;UAA/BA,yDAAA,mBAAAgP,GAAA,CAAAlI,YAAA,CAA+B;UAAC9G,+DAAA,SAAAgP,GAAA,CAAAnI,SAAA,CAAoB;UAAC7G,yDAAA,aAAAgP,GAAA,CAAApI,QAAA,CAAqB;;;qBDzB5F8I,yDAAY,EAAA3B,4DAAA,EAAAA,gEAAA,EAAAA,kEAAA,EAAAA,kEAAA,EAEZ6B,0DAAa,EAAA7B,6DAAA,EACbgC,wDAAW,EAAAwF,iEAAA,EAAAA,4DAAA,EAAAA,oDAAA,EACXzF,2DAAc,EACdD,2DAAc,EACdI,kDAAI,EACJD,mDAAK,EACLE,4FAAmB,EACnBP,6DAAgB,EAChBQ,sEAAkB,EAClBM,6DAAc,EAAAkF,uDAAA;MAAAE,MAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;AE3CqC;AAEyB;AAC6B;AACA;AAClB;AACF;AACE;AACM;AACgB;;;AAEjH,MAAMQ,MAAM,GAAW,CACnB;EACIC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAER,yFAA4BA;CAC1C,EACD;EACIO,IAAI,EAAE,mCAAmC;EACzCC,SAAS,EAAE1Q,sHAA8BA;CAC5C,EACD;EACIyQ,IAAI,EAAE,mCAAmC;EACzCC,SAAS,EAAEP,sHAA8BA;CAC5C,EACD;EACIM,IAAI,EAAE,uBAAuB;EAC7BC,SAAS,EAAEN,oGAAwBA;CACtC,EACD;EACIK,IAAI,EAAE,sBAAsB;EAC5BC,SAAS,EAAEL,kGAAwBA;CACtC,EACD;EACII,IAAI,EAAE,uBAAuB;EAC7BC,SAAS,EAAEJ,oGAAwBA;CACtC,EACD;EACIG,IAAI,EAAE,yBAAyB;EAC/BC,SAAS,EAAEH,0GAA0BA;CACxC,EACD;EACIE,IAAI,EAAE,oCAAoC;EAC1CC,SAAS,EAAErE,0HAAgCA;CAC9C,CACJ;AAMK,MAAOsE,sBAAsB;;;uCAAtBA,sBAAsB;IAAA;EAAA;;;YAAtBA;IAAsB;EAAA;;;gBAHrBV,yDAAY,CAACW,QAAQ,CAACJ,MAAM,CAAC,EAC7BP,yDAAY;IAAA;EAAA;;;sHAEbU,sBAAsB;IAAAE,OAAA,GAAA/I,yDAAA;IAAAgJ,OAAA,GAFrBb,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChDiC;AACZ;AAC6B;AACP;AAOJ;AAG3B;AACkB;AACvB;AACoD;AAEX;AAER;AACA;AACE;AACU;AACM;AACrB;AACoB;;;;;;;;;;;;;;;;;;;;;ICfvE9V,6DAAA,oBAAoE;IAClEA,qDAAA,GACF;IAAAA,2DAAA,EAAY;;;;IAFyCA,yDAAA,UAAA0X,OAAA,CAAc;IACjE1X,wDAAA,EACF;IADEA,iEAAA,MAAA0X,OAAA,CAAAC,cAAA,MACF;;;;;IAQA3X,6DAAA,oBAAgE;IAC9DA,qDAAA,GACF;IAAAA,2DAAA,EAAY;;;;IAFqCA,yDAAA,UAAA4X,OAAA,CAAc;IAC7D5X,wDAAA,EACF;IADEA,iEAAA,MAAA4X,OAAA,CAAAlR,KAAA,MACF;;;;;IAuCA1G,6DAAA,oBAAgE;IAC9DA,qDAAA,GACF;IAAAA,2DAAA,EAAY;;;;IAFqCA,yDAAA,UAAA6X,OAAA,CAAc;IAC7D7X,wDAAA,EACF;IADEA,iEAAA,MAAA6X,OAAA,CAAAnR,KAAA,MACF;;;;;IAWA1G,6DAAA,oBAAgE;IAC9DA,qDAAA,GACF;IAAAA,2DAAA,EAAY;;;;IAFqCA,yDAAA,UAAA8X,OAAA,CAAc;IAC7D9X,wDAAA,EACF;IADEA,iEAAA,MAAA8X,OAAA,CAAApR,KAAA,MACF;;;;;IAWA1G,6DAAA,oBAA+D;IAC7DA,qDAAA,GACF;IAAAA,2DAAA,EAAY;;;;IAFoCA,yDAAA,UAAA+X,OAAA,CAAc;IAC5D/X,wDAAA,EACF;IADEA,iEAAA,MAAA+X,OAAA,CAAArR,KAAA,MACF;;;;;IAUA1G,6DAAA,oBAAgE;IAC9DA,qDAAA,GACF;IAAAA,2DAAA,EAAY;;;;IAFqCA,yDAAA,UAAAgY,OAAA,CAAc;IAC7DhY,wDAAA,EACF;IADEA,iEAAA,MAAAgY,OAAA,CAAAtR,KAAA,MACF;;;;;IAWA1G,6DAAA,oBAAiE;IAC/DA,qDAAA,GACF;IAAAA,2DAAA,EAAY;;;;IAFsCA,yDAAA,UAAAiY,OAAA,CAAc;IAC9DjY,wDAAA,EACF;IADEA,iEAAA,MAAAiY,OAAA,CAAAvR,KAAA,MACF;;;;;IAWA1G,6DAAA,oBAAsE;IACpEA,qDAAA,GACF;IAAAA,2DAAA,EAAY;;;;IAF2CA,yDAAA,UAAAkY,OAAA,CAAc;IACnElY,wDAAA,EACF;IADEA,iEAAA,MAAAkY,OAAA,CAAAxR,KAAA,MACF;;;;;;IAoBF1G,6DAAA,iBAAmG;IAAzCA,yDAAA,mBAAAmY,wEAAA;MAAAnY,4DAAA,CAAA4C,IAAA;MAAA,MAAAwV,OAAA,GAAApY,4DAAA;MAAA,MAAAqY,uBAAA,GAAArY,0DAAA;MAAA,OAAAA,0DAAA,CAASoY,OAAA,CAAAE,SAAA,CAAAD,uBAAA,CAA8B;IAAA,EAAC;IAChGrY,qDAAA,yDACF;IAAAA,2DAAA,EAAS;;;;;;IAuDLA,6DAAA,iBACyD;IAAvDA,yDAAA,mBAAAuY,+EAAA;MAAAvY,4DAAA,CAAAwY,IAAA;MAAA,MAAAC,QAAA,GAAAzY,4DAAA,GAAAiB,SAAA;MAAA,MAAAmX,OAAA,GAAApY,4DAAA;MAAA,MAAA0Y,yBAAA,GAAA1Y,0DAAA;MAAA,OAAAA,0DAAA,CAASoY,OAAA,CAAAO,eAAA,CAAAD,yBAAA,EAAAD,QAAA,CAA4C;IAAA,EAAC;IACtDzY,qDAAA,qBACF;IAAAA,2DAAA,EAAS;;;;;;IApBXA,6DAFF,SAAmD,SAE7C;IAAAA,qDAAA,GAAoB;IAAAA,2DAAA,EAAK;IAC7BA,6DAAA,SAAI;IAAAA,qDAAA,GAAgB;IAAAA,2DAAA,EAAK;IACzBA,6DAAA,SAAI;IACFA,qDAAA,GAEF;IAAAA,2DAAA,EAAK;IACLA,6DAAA,SAAI;IAAAA,qDAAA,GAA4E;IAAAA,2DAAA,EAAK;IACrFA,6DAAA,SAAI;IAAAA,qDAAA,IAAuB;IAAAA,2DAAA,EAAK;IAChCA,6DAAA,UAAI;IACFA,qDAAA,IAGF;IAAAA,2DAAA,EAAK;IACLA,6DAAA,UAAI;IAAAA,qDAAA,IAA0E;IAAAA,2DAAA,EAAK;IACnFA,6DAAA,UAAI;IAAAA,qDAAA,IAAmD;IAAAA,2DAAA,EAAK;IAC5DA,6DAAA,UAAI;IAAAA,qDAAA,IAAiC;IAAAA,2DAAA,EAAK;IAC1CA,6DAAA,cAAkC;IAChCA,yDAAA,KAAA4Y,sDAAA,qBACyD;IAGzD5Y,6DAAA,kBACmH;IAAjHA,yDAAA,mBAAA6Y,sEAAA;MAAA,MAAAJ,QAAA,GAAAzY,4DAAA,CAAAqD,IAAA,EAAApC,SAAA;MAAA,MAAAmX,OAAA,GAAApY,4DAAA;MAAA,OAAAA,0DAAA,CAASoY,OAAA,CAAAU,4BAAA,CAA6B,yBAAyB,EAAAV,OAAA,CAAAW,WAAA,CAAAC,kBAAA,CAAAC,GAAA,EAAAR,QAAA,CAAAS,GAAA,CAAgD;IAAA,EAAC;IAChHlZ,qDAAA,kCACF;IAAAA,2DAAA,EAAS;IACTA,6DAAA,kBACmH;IAAjHA,yDAAA,mBAAAmZ,sEAAA;MAAA,MAAAV,QAAA,GAAAzY,4DAAA,CAAAqD,IAAA,EAAApC,SAAA;MAAA,MAAAmX,OAAA,GAAApY,4DAAA;MAAA,OAAAA,0DAAA,CAASoY,OAAA,CAAAU,4BAAA,CAA6B,yBAAyB,EAAAV,OAAA,CAAAW,WAAA,CAAAC,kBAAA,CAAAC,GAAA,EAAAR,QAAA,CAAAS,GAAA,CAAgD;IAAA,EAAC;IAChHlZ,qDAAA,8CACF;IAAAA,2DAAA,EAAS;IACTA,6DAAA,kBACoH;IAAlHA,yDAAA,mBAAAoZ,sEAAA;MAAA,MAAAX,QAAA,GAAAzY,4DAAA,CAAAqD,IAAA,EAAApC,SAAA;MAAA,MAAAmX,OAAA,GAAApY,4DAAA;MAAA,OAAAA,0DAAA,CAASoY,OAAA,CAAAU,4BAAA,CAA6B,0BAA0B,EAAAV,OAAA,CAAAW,WAAA,CAAAC,kBAAA,CAAAC,GAAA,EAAAR,QAAA,CAAAS,GAAA,CAAgD;IAAA,EAAC;IACjHlZ,qDAAA,8CACF;IAAAA,2DAAA,EAAS;IACTA,6DAAA,kBAAsF;IAA/BA,yDAAA,mBAAAqZ,sEAAA;MAAA,MAAAZ,QAAA,GAAAzY,4DAAA,CAAAqD,IAAA,EAAApC,SAAA;MAAA,MAAAmX,OAAA,GAAApY,4DAAA;MAAA,OAAAA,0DAAA,CAASoY,OAAA,CAAAkB,cAAA,CAAAb,QAAA,CAAoB;IAAA,EAAC;IACnFzY,qDAAA,kCACF;IAAAA,2DAAA,EAAS;IACTA,6DAAA,kBAAsG;IAA/CA,yDAAA,mBAAAuZ,sEAAA;MAAA,MAAAd,QAAA,GAAAzY,4DAAA,CAAAqD,IAAA,EAAApC,SAAA;MAAA,MAAAmX,OAAA,GAAApY,4DAAA;MAAA,MAAAwZ,mBAAA,GAAAxZ,0DAAA;MAAA,OAAAA,0DAAA,CAASoY,OAAA,CAAAqB,aAAA,CAAAD,mBAAA,EAAAf,QAAA,CAAoC;IAAA,EAAC;IACnGzY,qDAAA,4BACF;IAEJA,2DAFI,EAAS,EACN,EACF;;;;;IAxCCA,wDAAA,GAAoB;IAApBA,gEAAA,CAAAyY,QAAA,CAAAiB,UAAA,CAAoB;IACpB1Z,wDAAA,GAAgB;IAAhBA,gEAAA,CAAAyY,QAAA,CAAA/S,MAAA,CAAgB;IAElB1F,wDAAA,GAEF;IAFEA,iEAAA,MAAAyY,QAAA,CAAAkB,UAAA,yCAAAlB,QAAA,CAAAkB,UAAA,wCAEF;IACI3Z,wDAAA,GAA4E;IAA5EA,gEAAA,CAAAyY,QAAA,CAAAmB,SAAA,6BAAAnB,QAAA,CAAAmB,SAAA,iCAA4E;IAC5E5Z,wDAAA,GAAuB;IAAvBA,gEAAA,CAAAyY,QAAA,CAAAoB,aAAA,CAAuB;IAEzB7Z,wDAAA,GAGF;IAHEA,iEAAA,MAAAyY,QAAA,CAAAsB,UAAA,yCAAAtB,QAAA,CAAAsB,UAAA,yCAAAtB,QAAA,CAAAsB,UAAA,8CAGF;IACI/Z,wDAAA,GAA0E;IAA1EA,gEAAA,CAAAyY,QAAA,CAAAuB,WAAA,UAAAvB,QAAA,CAAAuB,WAAA,uDAA0E;IAC1Eha,wDAAA,GAAmD;IAAnDA,gEAAA,CAAAoY,OAAA,CAAA6B,sBAAA,CAAAxB,QAAA,CAAAyB,gBAAA,EAAmD;IACnDla,wDAAA,GAAiC;IAAjCA,gEAAA,CAAAyY,QAAA,CAAA0B,SAAA,mCAAiC;IAE1Bna,wDAAA,GAAc;IAAdA,yDAAA,SAAAoY,OAAA,CAAArW,QAAA,CAAc;;;;;IA6C3B/B,6DAAA,oBAAwE;IACtEA,qDAAA,GACF;IAAAA,2DAAA,EAAY;;;;IAF2CA,yDAAA,UAAAoa,UAAA,CAAgB;IACrEpa,wDAAA,EACF;IADEA,iEAAA,MAAAoa,UAAA,CAAAzC,cAAA,MACF;;;;;IAoDA3X,6DAAA,oBAA4E;IAC1EA,qDAAA,GACF;IAAAA,2DAAA,EAAY;;;;IAF+CA,yDAAA,UAAAqa,UAAA,CAAgB;IACzEra,wDAAA,EACF;IADEA,iEAAA,MAAAqa,UAAA,CAAA3T,KAAA,MACF;;;;;IASA1G,6DAAA,oBAA4E;IAC1EA,qDAAA,GACF;IAAAA,2DAAA,EAAY;;;;IAF+CA,yDAAA,UAAAsa,UAAA,CAAgB;IACzEta,wDAAA,EACF;IADEA,iEAAA,MAAAsa,UAAA,CAAA5T,KAAA,MACF;;;;;;IANF1G,6DADF,cAAkD,gBACqC;IACnFA,qDAAA,iCACF;IAAAA,2DAAA,EAAQ;IACRA,6DAAA,oBAA6F;IAA/DA,+DAAA,2BAAAua,+GAAA/V,MAAA;MAAAxE,4DAAA,CAAAwa,IAAA;MAAA,MAAApC,OAAA,GAAApY,4DAAA;MAAAA,iEAAA,CAAAoY,OAAA,CAAAqC,cAAA,CAAAC,kBAAA,EAAAlW,MAAA,MAAA4T,OAAA,CAAAqC,cAAA,CAAAC,kBAAA,GAAAlW,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAA+C;IAC3ExE,yDAAA,IAAA2a,uFAAA,wBAA4E;IAIhF3a,2DADE,EAAY,EACR;;;;IAL0BA,wDAAA,GAA+C;IAA/CA,+DAAA,YAAAoY,OAAA,CAAAqC,cAAA,CAAAC,kBAAA,CAA+C;IAC7C1a,wDAAA,EAA2B;IAA3BA,yDAAA,YAAAoY,OAAA,CAAAwC,OAAA,CAAAC,gBAAA,CAA2B;;;;;IAUzD7a,6DAAA,oBAA2E;IACzEA,qDAAA,GACF;IAAAA,2DAAA,EAAY;;;;IAF8CA,yDAAA,UAAA8a,UAAA,CAAgB;IACxE9a,wDAAA,EACF;IADEA,iEAAA,MAAA8a,UAAA,CAAApU,KAAA,MACF;;;;;;IANF1G,6DADF,cAAiD,gBACqC;IAClFA,qDAAA,qBACF;IAAAA,2DAAA,EAAQ;IACRA,6DAAA,oBAA0F;IAA9DA,+DAAA,2BAAA+a,+GAAAvW,MAAA;MAAAxE,4DAAA,CAAAgb,IAAA;MAAA,MAAA5C,OAAA,GAAApY,4DAAA;MAAAA,iEAAA,CAAAoY,OAAA,CAAAqC,cAAA,CAAAQ,iBAAA,EAAAzW,MAAA,MAAA4T,OAAA,CAAAqC,cAAA,CAAAQ,iBAAA,GAAAzW,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAA8C;IACxExE,yDAAA,IAAAkb,uFAAA,wBAA2E;IAI/Elb,2DADE,EAAY,EACR;;;;IALwBA,wDAAA,GAA8C;IAA9CA,+DAAA,YAAAoY,OAAA,CAAAqC,cAAA,CAAAQ,iBAAA,CAA8C;IAC1Cjb,wDAAA,EAA0B;IAA1BA,yDAAA,YAAAoY,OAAA,CAAAwC,OAAA,CAAAO,eAAA,CAA0B;;;;;;IA/E1Dnb,6DAFJ,uBAA+C,cACrB,gBACiE;IACrFA,qDAAA,iCACF;IAAAA,2DAAA,EAAQ;IACRA,6DAAA,oBAA6G;IAA/EA,+DAAA,2BAAAob,wGAAA5W,MAAA;MAAAxE,4DAAA,CAAAqb,IAAA;MAAA,MAAAjD,OAAA,GAAApY,4DAAA;MAAAA,iEAAA,CAAAoY,OAAA,CAAAqC,cAAA,CAAAzB,kBAAA,EAAAxU,MAAA,MAAA4T,OAAA,CAAAqC,cAAA,CAAAzB,kBAAA,GAAAxU,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAA+C;IAC3ExE,yDAAA,IAAAsb,gFAAA,wBAAwE;IAI5Etb,2DADE,EAAY,EACR;IAGJA,6DADF,cAAwB,gBAC+D;IACnFA,qDAAA,iCACF;IAAAA,2DAAA,EAAQ;IACRA,6DAAA,gBAAoG;IAAvCA,+DAAA,2BAAAub,oGAAA/W,MAAA;MAAAxE,4DAAA,CAAAqb,IAAA;MAAA,MAAAjD,OAAA,GAAApY,4DAAA;MAAAA,iEAAA,CAAAoY,OAAA,CAAAoD,WAAA,CAAA/V,UAAA,EAAAjB,MAAA,MAAA4T,OAAA,CAAAoD,WAAA,CAAA/V,UAAA,GAAAjB,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAAoC;IACnGxE,2DADE,EAAoG,EAChG;IAGJA,6DADF,eAAwB,iBAC2D;IAC/EA,qDAAA,sBACF;IAAAA,2DAAA,EAAQ;IACRA,6DAAA,iBACoB;IADyCA,+DAAA,2BAAAyb,qGAAAjX,MAAA;MAAAxE,4DAAA,CAAAqb,IAAA;MAAA,MAAAjD,OAAA,GAAApY,4DAAA;MAAAA,iEAAA,CAAAoY,OAAA,CAAAoD,WAAA,CAAA9V,MAAA,EAAAlB,MAAA,MAAA4T,OAAA,CAAAoD,WAAA,CAAA9V,MAAA,GAAAlB,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAAgC;IAE/FxE,2DAFE,EACoB,EAChB;IAGJA,6DADF,eAAwB,iBACmD;IACvEA,qDAAA,kCACF;IAAAA,2DAAA,EAAQ;IACRA,6DAAA,iBAAuG;IAA1CA,+DAAA,2BAAA0b,qGAAAlX,MAAA;MAAAxE,4DAAA,CAAAqb,IAAA;MAAA,MAAAjD,OAAA,GAAApY,4DAAA;MAAAA,iEAAA,CAAAoY,OAAA,CAAAoD,WAAA,CAAAG,aAAA,EAAAnX,MAAA,MAAA4T,OAAA,CAAAoD,WAAA,CAAAG,aAAA,GAAAnX,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAAuC;IACtGxE,2DADE,EAAuG,EACnG;IAGJA,6DADF,eAAwB,iBACiD;IACrEA,qDAAA,wCACF;IAAAA,2DAAA,EAAQ;IACRA,6DAAA,iBACmB;IAD2CA,+DAAA,2BAAA4b,qGAAApX,MAAA;MAAAxE,4DAAA,CAAAqb,IAAA;MAAA,MAAAjD,OAAA,GAAApY,4DAAA;MAAAA,iEAAA,CAAAoY,OAAA,CAAAoD,WAAA,CAAAK,WAAA,EAAArX,MAAA,MAAA4T,OAAA,CAAAoD,WAAA,CAAAK,WAAA,GAAArX,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAAqC;IAErGxE,2DAFE,EACmB,EACf;IAGJA,6DADF,eAAwB,iBAC2C;IAC/DA,qDAAA,kCACF;IAAAA,2DAAA,EAAQ;IACRA,6DAAA,iBAA+F;IAAlCA,+DAAA,2BAAA8b,qGAAAtX,MAAA;MAAAxE,4DAAA,CAAAqb,IAAA;MAAA,MAAAjD,OAAA,GAAApY,4DAAA;MAAAA,iEAAA,CAAAoY,OAAA,CAAAoD,WAAA,CAAAO,KAAA,EAAAvX,MAAA,MAAA4T,OAAA,CAAAoD,WAAA,CAAAO,KAAA,GAAAvX,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAA+B;IAC9FxE,2DADE,EAA+F,EAC3F;IAEJA,6DADF,eAAwB,iBAC4C;IAChEA,qDAAA,kCACF;IAAAA,2DAAA,EAAQ;IACRA,6DAAA,iBAAgG;IAAnCA,+DAAA,2BAAAgc,qGAAAxX,MAAA;MAAAxE,4DAAA,CAAAqb,IAAA;MAAA,MAAAjD,OAAA,GAAApY,4DAAA;MAAAA,iEAAA,CAAAoY,OAAA,CAAAoD,WAAA,CAAAS,MAAA,EAAAzX,MAAA,MAAA4T,OAAA,CAAAoD,WAAA,CAAAS,MAAA,GAAAzX,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAAgC;IAC/FxE,2DADE,EAAgG,EAC5F;IAGJA,6DADF,eAAwB,iBAC+D;IACnFA,qDAAA,kCACF;IAAAA,2DAAA,EAAQ;IACRA,6DAAA,qBAA6F;IAA/DA,+DAAA,2BAAAkc,yGAAA1X,MAAA;MAAAxE,4DAAA,CAAAqb,IAAA;MAAA,MAAAjD,OAAA,GAAApY,4DAAA;MAAAA,iEAAA,CAAAoY,OAAA,CAAAqC,cAAA,CAAA0B,kBAAA,EAAA3X,MAAA,MAAA4T,OAAA,CAAAqC,cAAA,CAAA0B,kBAAA,GAAA3X,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAA+C;IAC3ExE,yDAAA,KAAAoc,iFAAA,wBAA4E;IAIhFpc,2DADE,EAAY,EACR;IAYNA,yDAVA,KAAAqc,2EAAA,kBAAkD,KAAAC,2EAAA,kBAUD;IAY/Ctc,6DADF,eAAwB,iBAC+C;IACnEA,qDAAA,kCACF;IAAAA,2DAAA,EAAQ;IACRA,6DAAA,uBAAgE;IAApCA,+DAAA,2BAAAuc,2GAAA/X,MAAA;MAAAxE,4DAAA,CAAAqb,IAAA;MAAA,MAAAjD,OAAA,GAAApY,4DAAA;MAAAA,iEAAA,CAAAoY,OAAA,CAAAoD,WAAA,CAAA5B,SAAA,EAAApV,MAAA,MAAA4T,OAAA,CAAAoD,WAAA,CAAA5B,SAAA,GAAApV,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAAmC;IAACxE,qDAAA,eAChE;IACFA,2DADE,EAAc,EACV;IAGJA,6DADF,eAAwB,iBAC+C;IACnEA,qDAAA,kCACF;IAAAA,2DAAA,EAAQ;IACRA,6DAAA,uBAAgE;IAApCA,+DAAA,2BAAAwc,2GAAAhY,MAAA;MAAAxE,4DAAA,CAAAqb,IAAA;MAAA,MAAAjD,OAAA,GAAApY,4DAAA;MAAAA,iEAAA,CAAAoY,OAAA,CAAAoD,WAAA,CAAArB,SAAA,EAAA3V,MAAA,MAAA4T,OAAA,CAAAoD,WAAA,CAAArB,SAAA,GAAA3V,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAAmC;IAACxE,qDAAA,eAChE;IACFA,2DADE,EAAc,EACV;IAGJA,6DADF,eAAsC,iBACgD;IAClFA,qDAAA,kCACF;IAAAA,2DAAA,EAAQ;IAENA,6DADF,eAAoC,yBACL;IAC3BA,wDAAA,mBAAoD;IACpDA,6DAAA,iBACiE;IAA1CA,+DAAA,2BAAAyc,qGAAAjY,MAAA;MAAAxE,4DAAA,CAAAqb,IAAA;MAAA,MAAAjD,OAAA,GAAApY,4DAAA;MAAAA,iEAAA,CAAAoY,OAAA,CAAAoD,WAAA,CAAAkB,eAAA,EAAAlY,MAAA,MAAA4T,OAAA,CAAAoD,WAAA,CAAAkB,eAAA,GAAAlY,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAAyC;IADhExE,2DAAA,EACiE;IACjEA,wDAAA,4BAA8D;IAChEA,2DAAA,EAAgB;IAChBA,6DAAA,yBAA6B;IAC3BA,wDAAA,mBAAoD;IACpDA,6DAAA,iBAC+D;IAAxCA,+DAAA,2BAAA2c,qGAAAnY,MAAA;MAAAxE,4DAAA,CAAAqb,IAAA;MAAA,MAAAjD,OAAA,GAAApY,4DAAA;MAAAA,iEAAA,CAAAoY,OAAA,CAAAoD,WAAA,CAAAoB,aAAA,EAAApY,MAAA,MAAA4T,OAAA,CAAAoD,WAAA,CAAAoB,aAAA,GAAApY,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAAuC;IAD9DxE,2DAAA,EAC+D;IAC/DA,wDAAA,4BAA4D;IAIpEA,2DAHM,EAAgB,EACZ,EACF,EACO;;;;;;IArHmBA,wDAAA,GAA+C;IAA/CA,+DAAA,YAAAoY,OAAA,CAAAqC,cAAA,CAAAzB,kBAAA,CAA+C;IAC7ChZ,wDAAA,EAAuB;IAAvBA,yDAAA,YAAAoY,OAAA,CAAAyE,oBAAA,CAAuB;IAUM7c,wDAAA,GAAoC;IAApCA,+DAAA,YAAAoY,OAAA,CAAAoD,WAAA,CAAA/V,UAAA,CAAoC;IAOpCzF,wDAAA,GAAgC;IAAhCA,+DAAA,YAAAoY,OAAA,CAAAoD,WAAA,CAAA9V,MAAA,CAAgC;IAQhC1F,wDAAA,GAAuC;IAAvCA,+DAAA,YAAAoY,OAAA,CAAAoD,WAAA,CAAAG,aAAA,CAAuC;IAOtC3b,wDAAA,GAAqC;IAArCA,+DAAA,YAAAoY,OAAA,CAAAoD,WAAA,CAAAK,WAAA,CAAqC;IAQtC7b,wDAAA,GAA+B;IAA/BA,+DAAA,YAAAoY,OAAA,CAAAoD,WAAA,CAAAO,KAAA,CAA+B;IAM/B/b,wDAAA,GAAgC;IAAhCA,+DAAA,YAAAoY,OAAA,CAAAoD,WAAA,CAAAS,MAAA,CAAgC;IAO/Djc,wDAAA,GAA+C;IAA/CA,+DAAA,YAAAoY,OAAA,CAAAqC,cAAA,CAAA0B,kBAAA,CAA+C;IAC7Cnc,wDAAA,EAA2B;IAA3BA,yDAAA,YAAAoY,OAAA,CAAAwC,OAAA,CAAAkC,gBAAA,CAA2B;IAMpC9c,wDAAA,EAAuB;IAAvBA,yDAAA,SAAAoY,OAAA,CAAA2E,iBAAA,CAAuB;IAUvB/c,wDAAA,EAAsB;IAAtBA,yDAAA,SAAAoY,OAAA,CAAA4E,gBAAA,CAAsB;IAejBhd,wDAAA,GAAmC;IAAnCA,+DAAA,YAAAoY,OAAA,CAAAoD,WAAA,CAAA5B,SAAA,CAAmC;IAQnC5Z,wDAAA,GAAmC;IAAnCA,+DAAA,YAAAoY,OAAA,CAAAoD,WAAA,CAAArB,SAAA,CAAmC;IAWQna,wDAAA,GAA0B;IAA1BA,yDAAA,iBAAAid,aAAA,CAA0B;IACtEjd,+DAAA,YAAAoY,OAAA,CAAAoD,WAAA,CAAAkB,eAAA,CAAyC;IAKC1c,wDAAA,GAAwB;IAAxBA,yDAAA,iBAAAkd,WAAA,CAAwB;IAClEld,+DAAA,YAAAoY,OAAA,CAAAoD,WAAA,CAAAoB,aAAA,CAAuC;;;;;;IAQpE5c,6DAAA,iBAAqG;IAA9BA,yDAAA,mBAAAmd,uFAAA;MAAAnd,4DAAA,CAAAod,IAAA;MAAA,MAAAC,OAAA,GAAArd,4DAAA,GAAAoE,SAAA;MAAA,MAAAgU,OAAA,GAAApY,4DAAA;MAAA,OAAAA,0DAAA,CAASoY,OAAA,CAAAkF,cAAA,CAAAD,OAAA,CAAmB;IAAA,EAAC;IAACrd,qDAAA,mBAAE;IAAAA,2DAAA,EAAS;;;;;;IAhIpHA,6DAAA,kBAA+C;IAG7CA,yDAAA,IAAAud,oEAAA,6BAA+C;IA4H7Cvd,6DADF,yBAAsD,iBACsB;IAAvBA,yDAAA,mBAAAwd,8EAAA;MAAA,MAAAH,OAAA,GAAArd,4DAAA,CAAAyd,IAAA,EAAArZ,SAAA;MAAA,MAAAgU,OAAA,GAAApY,4DAAA;MAAA,OAAAA,0DAAA,CAASoY,OAAA,CAAA/S,OAAA,CAAAgY,OAAA,CAAY;IAAA,EAAC;IAACrd,qDAAA,mBAAE;IAAAA,2DAAA,EAAS;IACrFA,yDAAA,IAAA0d,8DAAA,qBAAqG;IAEzG1d,2DADE,EAAiB,EACT;;;;IA/HoBA,wDAAA,EAAiB;IAAjBA,yDAAA,SAAAoY,OAAA,CAAAoD,WAAA,CAAiB;IA6HYxb,wDAAA,GAAc;IAAdA,yDAAA,SAAAoY,OAAA,CAAA3I,QAAA,CAAc;;;;;;IA8BrEzP,6DAAA,kBAAiF;IAAhCA,yDAAA,mBAAA2d,wFAAA;MAAA3d,4DAAA,CAAA4d,IAAA;MAAA,MAAAC,OAAA,GAAA7d,4DAAA,GAAAoE,SAAA;MAAA,MAAAgU,OAAA,GAAApY,4DAAA;MAAA,OAAAA,0DAAA,CAASoY,OAAA,CAAA0F,gBAAA,CAAAD,OAAA,CAAqB;IAAA,EAAC;IAAC7d,qDAAA,mBAAE;IAAAA,2DAAA,EAAS;;;;;;IAvB9FA,6DADF,kBAA+C,qBAC7B;IACdA,qDAAA,wFACF;IAAAA,2DAAA,EAAiB;IAGbA,6DAFJ,uBAA2B,cACD,gBACkE;IACtFA,qDAAA,qBACF;IAAAA,2DAAA,EAAQ;IACRA,6DAAA,gBAAuG;IAA5CA,+DAAA,2BAAA+d,qFAAAvZ,MAAA;MAAAxE,4DAAA,CAAAge,IAAA;MAAA,MAAA5F,OAAA,GAAApY,4DAAA;MAAAA,iEAAA,CAAAoY,OAAA,CAAA6F,aAAA,CAAAC,aAAA,EAAA1Z,MAAA,MAAA4T,OAAA,CAAA6F,aAAA,CAAAC,aAAA,GAAA1Z,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAAyC;IACtGxE,2DADE,EAAuG,EACnG;IAEJA,6DADF,cAAwB,gBACoE;IAAAA,qDAAA,6CAC1F;IAAAA,2DAAA,EAAQ;IACRA,6DAAA,iBAA+G;IAA9CA,+DAAA,2BAAAme,sFAAA3Z,MAAA;MAAAxE,4DAAA,CAAAge,IAAA;MAAA,MAAA5F,OAAA,GAAApY,4DAAA;MAAAA,iEAAA,CAAAoY,OAAA,CAAA6F,aAAA,CAAAG,eAAA,EAAA5Z,MAAA,MAAA4T,OAAA,CAAA6F,aAAA,CAAAG,eAAA,GAAA5Z,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAA2C;IAC9GxE,2DADE,EAA+G,EAC3G;IAEJA,6DADF,eAAwB,kBAC2D;IAAAA,qDAAA,uCACjF;IAAAA,2DAAA,EAAQ;IACRA,6DAAA,kBAAqG;IAArCA,+DAAA,2BAAAqe,sFAAA7Z,MAAA;MAAAxE,4DAAA,CAAAge,IAAA;MAAA,MAAA5F,OAAA,GAAApY,4DAAA;MAAAA,iEAAA,CAAAoY,OAAA,CAAA6F,aAAA,CAAAvY,MAAA,EAAAlB,MAAA,MAAA4T,OAAA,CAAA6F,aAAA,CAAAvY,MAAA,GAAAlB,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAAkC;IAEtGxE,2DAFI,EAAqG,EACjG,EACO;IAEbA,6DADF,0BAAsD,mBACQ;IAAvBA,yDAAA,mBAAAse,+EAAA;MAAA,MAAAT,OAAA,GAAA7d,4DAAA,CAAAge,IAAA,EAAA5Z,SAAA;MAAA,MAAAgU,OAAA,GAAApY,4DAAA;MAAA,OAAAA,0DAAA,CAASoY,OAAA,CAAA/S,OAAA,CAAAwY,OAAA,CAAY;IAAA,EAAC;IAAC7d,qDAAA,IAAS;IAAAA,2DAAA,EAAS;IAC9EA,yDAAA,KAAAue,+DAAA,sBAAiF;IAErFve,2DADE,EAAiB,EACT;;;;IAjBuDA,wDAAA,GAAyC;IAAzCA,+DAAA,YAAAoY,OAAA,CAAA6F,aAAA,CAAAC,aAAA,CAAyC;IAKnCle,wDAAA,GAA2C;IAA3CA,+DAAA,YAAAoY,OAAA,CAAA6F,aAAA,CAAAG,eAAA,CAA2C;IAK5Cpe,wDAAA,GAAkC;IAAlCA,+DAAA,YAAAoY,OAAA,CAAA6F,aAAA,CAAAvY,MAAA,CAAkC;IAIxC1F,wDAAA,GAAS;IAATA,gEAAA,gBAAS;IACpCA,wDAAA,EAAc;IAAdA,yDAAA,SAAAoY,OAAA,CAAA3I,QAAA,CAAc;;;;;;IAc7CzP,6DADF,eAA6E,iBACV;IAA7BA,yDAAA,mBAAAwe,oFAAA;MAAAxe,4DAAA,CAAAye,IAAA;MAAA,MAAArG,OAAA,GAAApY,4DAAA;MAAA,OAAAA,0DAAA,CAASoY,OAAA,CAAAsG,gBAAA,EAAkB;IAAA,EAAC;IAC9D1e,qDAAA,qDACF;IAAAA,2DAAA,EAAS;IAEPA,6DADF,UAAK,kBACwE;IAA7BA,yDAAA,mBAAA2e,oFAAA;MAAA3e,4DAAA,CAAAye,IAAA;MAAA,MAAArG,OAAA,GAAApY,4DAAA;MAAA,OAAAA,0DAAA,CAASoY,OAAA,CAAAwG,gBAAA,EAAkB;IAAA,EAAC;IACxE5e,qDAAA,6CACF;IAAAA,2DAAA,EAAS;IACTA,6DAAA,iBAAsE;IAA7BA,yDAAA,mBAAA6e,oFAAA;MAAA7e,4DAAA,CAAAye,IAAA;MAAA,MAAArG,OAAA,GAAApY,4DAAA;MAAA,OAAAA,0DAAA,CAASoY,OAAA,CAAA0G,gBAAA,EAAkB;IAAA,EAAC;IACnE9e,qDAAA,6CACF;IAEJA,2DAFI,EAAS,EACL,EACF;;;;;IAGNA,6DAAA,eAAmE;IACjEA,wDAAA,aAAgC;IAChCA,6DAAA,aAAQ;IAAAA,qDAAA,2CAAM;IAAAA,2DAAA,EAAS;IAACA,qDAAA,iNAC1B;IAAAA,2DAAA,EAAM;;;;;;IAgDIA,6DAAA,kBAAmG;IAAjCA,yDAAA,mBAAA+e,8FAAA;MAAA/e,4DAAA,CAAAgf,IAAA;MAAA,MAAAC,KAAA,GAAAjf,4DAAA,GAAA6C,KAAA;MAAA,MAAAuV,OAAA,GAAApY,4DAAA;MAAA,OAAAA,0DAAA,CAASoY,OAAA,CAAA8G,mBAAA,CAAAD,KAAA,CAAsB;IAAA,EAAC;IAChGjf,qDAAA,qBACF;IAAAA,2DAAA,EAAS;;;;;IACTA,6DAAA,gBAAsD;IACpDA,wDAAA,aAA2B;IAC7BA,2DAAA,EAAO;;;;;;IAnCPA,6DAFJ,SAAuD,SACjD,iBAI0G;IAHjFA,+DAAA,2BAAAmf,2FAAA3a,MAAA;MAAA,MAAA4a,QAAA,GAAApf,4DAAA,CAAAqf,IAAA,EAAApe,SAAA;MAAAjB,iEAAA,CAAAof,QAAA,CAAAE,SAAA,EAAA9a,MAAA,MAAA4a,QAAA,CAAAE,SAAA,GAAA9a,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAA4B;IAIzDxE,2DAJE,EAG4G,EACzG;IAEHA,6DADF,SAAI,iBAEsG;IAD3EA,+DAAA,2BAAAuf,2FAAA/a,MAAA;MAAA,MAAA4a,QAAA,GAAApf,4DAAA,CAAAqf,IAAA,EAAApe,SAAA;MAAAjB,iEAAA,CAAAof,QAAA,CAAAI,UAAA,EAAAhb,MAAA,MAAA4a,QAAA,CAAAI,UAAA,GAAAhb,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAA6B;IAACxE,yDAAA,2BAAAuf,2FAAA;MAAAvf,4DAAA,CAAAqf,IAAA;MAAA,MAAAjH,OAAA,GAAApY,4DAAA;MAAA,OAAAA,0DAAA,CAAiBoY,OAAA,CAAAqH,cAAA,EAAgB;IAAA,EAAC;IAE/Fzf,2DAFE,EACwG,EACrG;IAEHA,6DADF,SAAI,iBAI0G;IAHjFA,+DAAA,2BAAA0f,2FAAAlb,MAAA;MAAA,MAAA4a,QAAA,GAAApf,4DAAA,CAAAqf,IAAA,EAAApe,SAAA;MAAAjB,iEAAA,CAAAof,QAAA,CAAAO,KAAA,EAAAnb,MAAA,MAAA4a,QAAA,CAAAO,KAAA,GAAAnb,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAAwB;IAIrDxE,2DAJE,EAG4G,EACzG;IAEHA,6DADF,SAAI,iBAE+E;IADpDA,+DAAA,2BAAA4f,2FAAApb,MAAA;MAAA,MAAA4a,QAAA,GAAApf,4DAAA,CAAAqf,IAAA,EAAApe,SAAA;MAAAjB,iEAAA,CAAAof,QAAA,CAAAS,MAAA,EAAArb,MAAA,MAAA4a,QAAA,CAAAS,MAAA,GAAArb,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAAyB;IAACxE,yDAAA,2BAAA4f,2FAAA;MAAA5f,4DAAA,CAAAqf,IAAA;MAAA,MAAAjH,OAAA,GAAApY,4DAAA;MAAA,OAAAA,0DAAA,CAAiBoY,OAAA,CAAAqH,cAAA,EAAgB;IAAA,EAAC;IAE3Fzf,2DAFE,EACiF,EAC9E;IACLA,6DAAA,cAAuB;IACrBA,qDAAA,IACF;IAAAA,2DAAA,EAAK;IAEHA,6DADF,UAAI,iBAGyF;IACzFA,qDAAA,IACF;IACFA,2DADE,EAAO,EACJ;IACLA,6DAAA,UAAI;IAIFA,yDAHA,KAAA8f,qEAAA,sBAAmG,KAAAC,mEAAA,oBAG7C;IAI1D/f,2DADE,EAAK,EACF;;;;;IAlCCA,wDAAA,GAAyG;IAAzGA,0DAAA,cAAAoY,OAAA,CAAA6H,mBAAA,IAAAb,QAAA,CAAAxf,kBAAA,UAAAwf,QAAA,CAAAxf,kBAAA,OAAyG;IAHhFI,+DAAA,YAAAof,QAAA,CAAAE,SAAA,CAA4B;IACrDtf,yDAAA,cAAAoY,OAAA,CAAA6H,mBAAA,IAAAb,QAAA,CAAAxf,kBAAA,UAAAwf,QAAA,CAAAxf,kBAAA,OAAmG;IAKxEI,wDAAA,GAA6B;IAA7BA,+DAAA,YAAAof,QAAA,CAAAI,UAAA,CAA6B;IACrBxf,yDAAA,cAAAoY,OAAA,CAAA6H,mBAAA,IAAAb,QAAA,CAAAxf,kBAAA,OAAkE;IAMrGI,wDAAA,GAAyG;IAAzGA,0DAAA,cAAAoY,OAAA,CAAA6H,mBAAA,IAAAb,QAAA,CAAAxf,kBAAA,UAAAwf,QAAA,CAAAxf,kBAAA,OAAyG;IAHhFI,+DAAA,YAAAof,QAAA,CAAAO,KAAA,CAAwB;IACjD3f,yDAAA,cAAAoY,OAAA,CAAA6H,mBAAA,IAAAb,QAAA,CAAAxf,kBAAA,UAAAwf,QAAA,CAAAxf,kBAAA,OAAmG;IAKxEI,wDAAA,GAAyB;IAAzBA,+DAAA,YAAAof,QAAA,CAAAS,MAAA,CAAyB;IACxC7f,yDAAA,cAAAoY,OAAA,CAAA6H,mBAAA,IAAAb,QAAA,CAAAxf,kBAAA,OAAkE;IAGhFI,wDAAA,GACF;IADEA,iEAAA,MAAAoY,OAAA,CAAA8H,cAAA,CAAAd,QAAA,CAAAI,UAAA,GAAAJ,QAAA,CAAAS,MAAA,OACF;IAEsB7f,wDAAA,GAAqD;IAEvEA,0DAFkB,kBAAAof,QAAA,CAAAxf,kBAAA,OAAqD,eAAAwf,QAAA,CAAAxf,kBAAA,OACrB,oBAAAwf,QAAA,CAAAxf,kBAAA,UAAAwf,QAAA,CAAAxf,kBAAA,OACsC;IACxFI,wDAAA,EACF;IADEA,iEAAA,MAAAoY,OAAA,CAAA+H,oBAAA,CAAAf,QAAA,CAAAxf,kBAAA,OACF;IAGSI,wDAAA,GAAyB;IAAzBA,yDAAA,SAAAoY,OAAA,CAAA6H,mBAAA,CAAyB;IAG3BjgB,wDAAA,EAA0B;IAA1BA,yDAAA,UAAAoY,OAAA,CAAA6H,mBAAA,CAA0B;;;;;IAMnCjgB,6DADF,SAAwC,cACc;IAClDA,qDAAA,iLACF;IACFA,2DADE,EAAK,EACF;;;;;;IAuDTA,6DAAA,kBACiB;IADgEA,yDAAA,mBAAAogB,wFAAA;MAAApgB,4DAAA,CAAAqgB,IAAA;MAAA,MAAAjI,OAAA,GAAApY,4DAAA;MAAA,OAAAA,0DAAA,CAASoY,OAAA,CAAAkI,kBAAA,EAAoB;IAAA,EAAC;IAE7GtgB,wDAAA,aAAgC;IAACA,qDAAA,6CACnC;IAAAA,2DAAA,EAAS;;;;;;IASTA,6DAAA,kBAC2C;IADqBA,yDAAA,mBAAAugB,wFAAA;MAAAvgB,4DAAA,CAAAwgB,IAAA;MAAA,MAAAC,OAAA,GAAAzgB,4DAAA,GAAAoE,SAAA;MAAA,MAAAgU,OAAA,GAAApY,4DAAA;MAAA,OAAAA,0DAAA,CAASoY,OAAA,CAAAsI,aAAA,CAAAD,OAAA,CAAkB;IAAA,EAAC;IAE1FzgB,qDAAA,uCACF;IAAAA,2DAAA,EAAS;;;;IAFPA,yDAAA,aAAAoY,OAAA,CAAAuI,cAAA,CAAArX,MAAA,OAAwC;;;;;;IAG1CtJ,6DAAA,kBAC2C;IADqBA,yDAAA,mBAAA4gB,wFAAA;MAAA5gB,4DAAA,CAAA6gB,IAAA;MAAA,MAAAJ,OAAA,GAAAzgB,4DAAA,GAAAoE,SAAA;MAAA,MAAAgU,OAAA,GAAApY,4DAAA;MAAA,OAAAA,0DAAA,CAASoY,OAAA,CAAA0I,aAAA,CAAAL,OAAA,CAAkB;IAAA,EAAC;IAE1FzgB,qDAAA,uCACF;IAAAA,2DAAA,EAAS;;;;IAFPA,yDAAA,aAAAoY,OAAA,CAAAuI,cAAA,CAAArX,MAAA,OAAwC;;;;;;IA3J9CtJ,6DADF,mBAAgD,qBAC9B;IACdA,qDAAA,GACF;IAAAA,2DAAA,EAAiB;IACjBA,6DAAA,mBAAc;IAiBZA,yDAfA,IAAA+gB,2DAAA,mBAA6E,IAAAC,2DAAA,mBAeV;IAS3DhhB,6DAJR,eAA8B,iBACQ,YAC3B,SACD,eACc;IAAAA,qDAAA,gCAAI;IAAAA,2DAAA,EAAK;IACzBA,6DAAA,eAAgB;IAAAA,qDAAA,6BAAM;IAAAA,2DAAA,EAAK;IAC3BA,6DAAA,eAAe;IAAAA,qDAAA,oBAAE;IAAAA,2DAAA,EAAK;IACtBA,6DAAA,eAAe;IAAAA,qDAAA,oBAAE;IAAAA,2DAAA,EAAK;IACtBA,6DAAA,eAAgB;IAAAA,qDAAA,6BAAM;IAAAA,2DAAA,EAAK;IAC3BA,6DAAA,eAAgB;IAAAA,qDAAA,oBAAE;IAAAA,2DAAA,EAAK;IACvBA,6DAAA,eAAgB;IAAAA,qDAAA,oBAAE;IAEtBA,2DAFsB,EAAK,EACpB,EACC;IACRA,6DAAA,aAAO;IAyCLA,yDAxCA,KAAAihB,2DAAA,mBAAuD,KAAAC,2DAAA,kBAwCf;IAO9ClhB,2DAFI,EAAQ,EACF,EACJ;IAQEA,6DALR,gBAAkB,gBACqB,gBACR,gBAE2C,iBACjC;IAAAA,qDAAA,oBAAE;IAAAA,2DAAA,EAAO;IAC1CA,6DAAA,iBAAwC;IAAAA,qDAAA,IAAiC;IAC3EA,2DAD2E,EAAO,EAC5E;IAKFA,6DAFJ,gBAAqG,gBAC5D,gBACF;IACjCA,wDAAA,cAAwC;IAC1CA,2DAAA,EAAM;IAEJA,6DADF,WAAK,iBAC+B;IAAAA,qDAAA,0BAAG;IAAAA,2DAAA,EAAO;IAC5CA,6DAAA,iBAA2D;IAAAA,qDAAA,UAAE;IAAAA,2DAAA,EAAO;IACpEA,6DAAA,gBAAmC;IACjCA,wDAAA,cAAuC;IACvCA,qDAAA,4DACF;IAEJA,2DAFI,EAAM,EACF,EACF;IAEJA,6DADF,gBAAsB,gBAC8B;IAAAA,qDAAA,IAAyC;IAAAA,2DAAA,EAAM;IACjGA,6DAAA,gBAA8B;IAAAA,qDAAA,gCAAI;IAEtCA,2DAFsC,EAAM,EACpC,EACF;IAGNA,wDAAA,eAAiB;IAIfA,6DADF,gBAA+D,iBACrB;IAAAA,qDAAA,0BAAG;IAAAA,2DAAA,EAAO;IAClDA,6DAAA,iBAA2C;IAAAA,qDAAA,IAAsC;IAK3FA,2DAL2F,EAAO,EACpF,EACF,EACF,EACF,EACO;IAGXA,6DAFJ,2BAAuD,WAChD,mBAEsD;IADRA,yDAAA,mBAAAmhB,+EAAA;MAAAnhB,4DAAA,CAAAohB,IAAA;MAAA,MAAAhJ,OAAA,GAAApY,4DAAA;MAAA,OAAAA,0DAAA,CAASoY,OAAA,CAAAiJ,cAAA,EAAgB;IAAA,EAAC;IAEzErhB,wDAAA,cAAiC;IAACA,qDAAA,wCACpC;IAAAA,2DAAA,EAAS;IAETA,yDAAA,KAAAshB,+DAAA,sBACiB;IAOnBthB,2DAAA,EAAM;IAEJA,6DADF,WAAK,mBACkE;IAAvBA,yDAAA,mBAAAuhB,+EAAA;MAAA,MAAAd,OAAA,GAAAzgB,4DAAA,CAAAohB,IAAA,EAAAhd,SAAA;MAAA,MAAAgU,OAAA,GAAApY,4DAAA;MAAA,OAAAA,0DAAA,CAASoY,OAAA,CAAA/S,OAAA,CAAAob,OAAA,CAAY;IAAA,EAAC;IAACzgB,qDAAA,oBAAE;IAAAA,2DAAA,EAAS;IAMhFA,yDAJA,KAAAwhB,+DAAA,sBAC2C,KAAAC,+DAAA,sBAIA;IAKjDzhB,2DAFI,EAAM,EACS,EACT;;;;IA/JNA,wDAAA,GACF;IADEA,iEAAA,2BAAAoY,OAAA,CAAAsJ,YAAA,kBAAAtJ,OAAA,CAAAsJ,YAAA,CAAAhI,UAAA,QAAAtB,OAAA,CAAAsJ,YAAA,kBAAAtJ,OAAA,CAAAsJ,YAAA,CAAAhc,MAAA,aACF;IAGQ1F,wDAAA,GAAyB;IAAzBA,yDAAA,SAAAoY,OAAA,CAAA6H,mBAAA,CAAyB;IAezBjgB,wDAAA,EAA0B;IAA1BA,yDAAA,UAAAoY,OAAA,CAAA6H,mBAAA,CAA0B;IAmBLjgB,wDAAA,IAAmB;IAAnBA,yDAAA,YAAAoY,OAAA,CAAAuI,cAAA,CAAmB;IAwCnC3gB,wDAAA,EAAiC;IAAjCA,yDAAA,SAAAoY,OAAA,CAAAuI,cAAA,CAAArX,MAAA,OAAiC;IAgBItJ,wDAAA,GAAiC;IAAjCA,gEAAA,CAAAoY,OAAA,CAAA8H,cAAA,CAAA9H,OAAA,CAAAuJ,WAAA,EAAiC;IAmBrB3hB,wDAAA,IAAyC;IAAzCA,gEAAA,CAAAoY,OAAA,CAAA8H,cAAA,CAAA9H,OAAA,CAAAwJ,mBAAA,EAAyC;IAWlD5hB,wDAAA,GAAsC;IAAtCA,gEAAA,CAAAoY,OAAA,CAAA8H,cAAA,CAAA9H,OAAA,CAAAyJ,gBAAA,EAAsC;IASrF7hB,wDAAA,GAAwC;IAAxCA,yDAAA,aAAAoY,OAAA,CAAAuI,cAAA,CAAArX,MAAA,OAAwC;IAIjCtJ,wDAAA,GAA0B;IAA1BA,yDAAA,UAAAoY,OAAA,CAAA6H,mBAAA,CAA0B;IAY1BjgB,wDAAA,GAAyB;IAAzBA,yDAAA,SAAAoY,OAAA,CAAA6H,mBAAA,CAAyB;IAIzBjgB,wDAAA,EAAyB;IAAzBA,yDAAA,SAAAoY,OAAA,CAAA6H,mBAAA,CAAyB;;;ADnfpC,MAAOlK,4BAA6B,SAAQjW,yEAAa;EAE7DgG,YACUC,MAAmB,EACnBoM,UAAsB,EACtBnM,aAA8B,EAC9BK,OAAuB,EACvBJ,KAAuB,EACvBE,aAA2B,EAC3B2b,qBAA2C,EAC3CC,iBAAmC,EACnC1P,OAAsB,EACtBC,MAAc,EACd/L,aAA2B,EAC3Byb,gBAAgC,EAChCC,gBAAkC;IAE1C,KAAK,CAAClc,MAAM,CAAC;IAdL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAoM,UAAU,GAAVA,UAAU;IACV,KAAAnM,aAAa,GAAbA,aAAa;IACb,KAAAK,OAAO,GAAPA,OAAO;IACP,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAE,aAAa,GAAbA,aAAa;IACb,KAAA2b,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAA1P,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAA/L,aAAa,GAAbA,aAAa;IACb,KAAAyb,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAd1B,KAAAC,eAAe,GAAW,CAAC,CAAC;IA0BnB,KAAAvb,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAAN,aAAa,GAAiB,CAC5B;MACE/G,KAAK,EAAE,CAAC;MACRgH,GAAG,EAAE,OAAO;MACZC,KAAK,EAAE;KACR,EACD;MACEjH,KAAK,EAAE,CAAC;MACRgH,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;KACR,CACF;IAED,KAAAyb,gBAAgB,GAAG,CACjB;MACE1iB,KAAK,EAAE,IAAI;MACXgH,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE;KACR,EACD;MACEjH,KAAK,EAAE,IAAI;MACXgH,GAAG,EAAE,QAAQ;MACbC,KAAK,EAAE;KACR,EACD;MACEjH,KAAK,EAAE,KAAK;MACZgH,GAAG,EAAE,YAAY;MACjBC,KAAK,EAAE;KACR,CACF;IAKD,KAAA0b,gBAAgB,GAAU,CAAC;MAAE1b,KAAK,EAAE,IAAI;MAAEjH,KAAK,EAAE;IAAE,CAAE,CAAC;IACtD,KAAA4iB,gBAAgB,GAAU,CAAC;MAAE3b,KAAK,EAAE,IAAI;MAAEjH,KAAK,EAAE;IAAE,CAAE,CAAC;IACtD,KAAA0b,eAAe,GAAU,CAAC;MAAEzU,KAAK,EAAE,IAAI;MAAEjH,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACrD,KAAAqd,gBAAgB,GAAU,CAAC;MAAEpW,KAAK,EAAE,IAAI;MAAEjH,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACtD,KAAAob,gBAAgB,GAAU,CAAC;MAAEnU,KAAK,EAAE,IAAI;MAAEjH,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACtD,KAAA6iB,iBAAiB,GAAU,CAAC;MAAE5b,KAAK,EAAE,IAAI;MAAEjH,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACvD,KAAA8iB,sBAAsB,GAAU,CAAC;MAAE7b,KAAK,EAAE,IAAI;MAAEjH,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IAE5D,KAAAmb,OAAO,GAAG;MACRO,eAAe,EAAE,IAAI,CAAChJ,UAAU,CAACqQ,cAAc,CAACrL,oFAAiB,CAAC;MAClE0D,gBAAgB,EAAE,IAAI,CAAC1I,UAAU,CAACqQ,cAAc,CAACnL,4EAAa,CAAC;MAC/DyF,gBAAgB,EAAE,IAAI,CAAC3K,UAAU,CAACqQ,cAAc,CAACpL,4EAAa,CAAC;MAC/DmL,sBAAsB,EAAE,IAAI,CAACpQ,UAAU,CAACqQ,cAAc,CAACjL,yFAAmB;KAC3E;IAGD,KAAAkL,UAAU,GAAG;MACX9Z,QAAQ,EAAE,CAAC;MACXoT,KAAK,EAAE,EAAE;MACTnC,SAAS,EAAE,KAAK;MAChBG,UAAU,EAAE,CAAC;MACbI,SAAS,EAAE,KAAK;MAChBwB,aAAa,EAAE,EAAE;MACjB+G,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbhJ,UAAU,EAAE,CAAC;MACbD,UAAU,EAAE,EAAE;MACduC,MAAM,EAAE;KACT;IACD;IACA,KAAA0E,cAAc,GAAoB,EAAE;IACpC,KAAAgB,WAAW,GAAW,CAAC;IACvB;IACA,KAAAiB,iBAAiB,GAAW,KAAK,CAAC,CAAE;IACpC,KAAAC,uBAAuB,GAAW,CAAC,CAAC,CAAG;IACvC,KAAAjB,mBAAmB,GAAW,CAAC,CAAC,CAAO;IACvC,KAAAC,gBAAgB,GAAW,CAAC,CAAC,CAAU;IACvC,KAAAiB,mBAAmB,GAAY,IAAI,CAAC,CAAG;IACvC,KAAApB,YAAY,GAAQ,IAAI;IACxB,KAAAqB,kBAAkB,GAAW,CAAC;IAC9B,KAAA9C,mBAAmB,GAAY,IAAI,CAAC,CAAC;IAuHrC,KAAA+C,YAAY,GAAgB,IAAI;IAuKhC,KAAAC,uBAAuB,GAAU,CAC/B;MACExjB,KAAK,EAAE,EAAE;MAAEiH,KAAK,EAAE;KACnB,CACF;IA1XC,IAAI,CAACH,aAAa,CAAC2c,OAAO,EAAE,CAAC3P,IAAI,CAC/B0D,0CAAG,CAAE5O,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACiF,MAAM,kDAA4B,CAAC,CAACjF,GAAG,CAACkF,OAAO,EAAE;QACvD,IAAI,CAAC2U,eAAe,GAAG7Z,GAAG,CAACkF,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACrG,SAAS,EAAE;EACf;EAmFSF,QAAQA,CAAA;IACf,IAAI,CAACmU,eAAe,GAAG,CACrB,GAAG,IAAI,CAACA,eAAe,EACvB,GAAG,IAAI,CAAChJ,UAAU,CAACqQ,cAAc,CAACrL,oFAAiB,CAAC,CACrD;IACD,IAAI,CAAC2F,gBAAgB,GAAG,CACtB,GAAG,IAAI,CAACA,gBAAgB,EACxB,GAAG,IAAI,CAAC3K,UAAU,CAACqQ,cAAc,CAACpL,4EAAa,CAAC,CACjD;IACD,IAAI,CAACyD,gBAAgB,GAAG,CACtB,GAAG,IAAI,CAACA,gBAAgB,EACxB,GAAG,IAAI,CAAC1I,UAAU,CAACqQ,cAAc,CAACnL,4EAAa,CAAC,CACjD;IACD,IAAI,CAACiL,iBAAiB,GAAG,CACvB,GAAG,IAAI,CAACA,iBAAiB,EACzB,GAAG,IAAI,CAACnQ,UAAU,CAACqQ,cAAc,CAAClL,8EAAc,CAAC,CAClD;IACD,IAAI,CAACiL,sBAAsB,GAAG,CAC5B,GAAG,IAAI,CAACA,sBAAsB,EAC9B,GAAG,IAAI,CAACpQ,UAAU,CAACqQ,cAAc,CAACjL,yFAAmB,CAAC,CACvD;IAED,IAAIC,+FAAmB,CAAC2L,iBAAiB,CAAC1L,0EAAW,CAAC2L,YAAY,CAAC,IAAI,IAAI,IACtE5L,+FAAmB,CAAC2L,iBAAiB,CAAC1L,0EAAW,CAAC2L,YAAY,CAAC,IAAItP,SAAS,IAC5E0D,+FAAmB,CAAC2L,iBAAiB,CAAC1L,0EAAW,CAAC2L,YAAY,CAAC,IAAI,EAAE,EAAE;MAC1E,IAAIC,eAAe,GAAGC,IAAI,CAACC,KAAK,CAAC/L,+FAAmB,CAAC2L,iBAAiB,CAAC1L,0EAAW,CAAC2L,YAAY,CAAC,CAAC;MACjG,IAAI,CAACrK,WAAW,GAAG;QACjBC,kBAAkB,EAAE,IAAI;QACxB;QACA;QACA;QACAwK,kBAAkB,EAAEH,eAAe,CAACG,kBAAkB,IAAI,IAAI,IAAIH,eAAe,CAACG,kBAAkB,IAAI1P,SAAS,GAC7G,IAAI,CAACuO,gBAAgB,CAACoB,IAAI,CAAC5W,CAAC,IAAIA,CAAC,CAACpN,KAAK,IAAI4jB,eAAe,CAACG,kBAAkB,CAAC/jB,KAAK,CAAC,GACpF,IAAI,CAAC4iB,gBAAgB,CAAC,CAAC,CAAC;QAC5BlG,kBAAkB,EAAEkH,eAAe,CAAClH,kBAAkB,IAAI,IAAI,IAAIkH,eAAe,CAAClH,kBAAkB,IAAIrI,SAAS,GAC7G,IAAI,CAACgJ,gBAAgB,CAAC2G,IAAI,CAAC5W,CAAC,IAAIA,CAAC,CAACpN,KAAK,IAAI4jB,eAAe,CAAClH,kBAAkB,CAAC1c,KAAK,CAAC,GACpF,IAAI,CAACqd,gBAAgB,CAAC,CAAC,CAAC;QAC5BpC,kBAAkB,EAAE2I,eAAe,CAAC3I,kBAAkB,IAAI,IAAI,IAAI2I,eAAe,CAAC3I,kBAAkB,IAAI5G,SAAS,GAC7G,IAAI,CAAC+G,gBAAgB,CAAC4I,IAAI,CAAC5W,CAAC,IAAIA,CAAC,CAACpN,KAAK,IAAI4jB,eAAe,CAAC3I,kBAAkB,CAACjb,KAAK,CAAC,GACpF,IAAI,CAACob,gBAAgB,CAAC,CAAC,CAAC;QAC5BI,iBAAiB,EAAEoI,eAAe,CAACpI,iBAAiB,IAAI,IAAI,IAAIoI,eAAe,CAACpI,iBAAiB,IAAInH,SAAS,GAC1G,IAAI,CAACqH,eAAe,CAACsI,IAAI,CAAC5W,CAAC,IAAIA,CAAC,CAACpN,KAAK,IAAI4jB,eAAe,CAACpI,iBAAiB,CAACxb,KAAK,CAAC,GAClF,IAAI,CAAC0b,eAAe,CAAC,CAAC,CAAC;QAC3BuI,mBAAmB,EAAEL,eAAe,CAACK,mBAAmB,IAAI,IAAI,IAAIL,eAAe,CAACK,mBAAmB,IAAI5P,SAAS,GAChH,IAAI,CAACwO,iBAAiB,CAACmB,IAAI,CAAC5W,CAAC,IAAIA,CAAC,CAACpN,KAAK,IAAI4jB,eAAe,CAACK,mBAAmB,CAACjkB,KAAK,CAAC,GACtF,IAAI,CAAC6iB,iBAAiB,CAAC,CAAC,CAAC;QAC7BqB,wBAAwB,EAAEN,eAAe,CAACM,wBAAwB,IAAI,IAAI,IAAIN,eAAe,CAACM,wBAAwB,IAAI7P,SAAS,GAC/H,IAAI,CAACyO,sBAAsB,CAACkB,IAAI,CAAC5W,CAAC,IAAIA,CAAC,CAACpN,KAAK,IAAI4jB,eAAe,CAACM,wBAAwB,CAAClkB,KAAK,CAAC,GAChG,IAAI,CAAC8iB,sBAAsB,CAAC,CAAC,CAAC;QAClCqB,gBAAgB,EAAEP,eAAe,CAACO,gBAAgB,IAAI,IAAI,IAAIP,eAAe,CAACO,gBAAgB,IAAI9P,SAAS,GACvG,IAAI,CAACqO,gBAAgB,CAACsB,IAAI,CAAC5W,CAAC,IAAIA,CAAC,CAACpN,KAAK,IAAI4jB,eAAe,CAACO,gBAAgB,CAACnkB,KAAK,CAAC,GAClF,IAAI,CAAC0iB,gBAAgB,CAAC,CAAC,CAAC;QAC5B0B,KAAK,EAAER,eAAe,CAACQ,KAAK,IAAI,IAAI,IAAIR,eAAe,CAACQ,KAAK,IAAI/P,SAAS,GACtEuP,eAAe,CAACQ,KAAK,GACrB,EAAE;QACNC,GAAG,EAAET,eAAe,CAACS,GAAG,IAAI,IAAI,IAAIT,eAAe,CAACS,GAAG,IAAIhQ,SAAS,GAChEuP,eAAe,CAACS,GAAG,GACnB;OACL;IACH,CAAC,MACI;MACH,IAAI,CAAC/K,WAAW,GAAG;QACjBC,kBAAkB,EAAE,IAAI;QACxB;QACAwK,kBAAkB,EAAE,IAAI,CAACnB,gBAAgB,CAAC,CAAC,CAAC;QAC5ClG,kBAAkB,EAAE,IAAI,CAACW,gBAAgB,CAAC,CAAC,CAAC;QAC5CpC,kBAAkB,EAAE,IAAI,CAACG,gBAAgB,CAAC,CAAC,CAAC;QAC5CI,iBAAiB,EAAE,IAAI,CAACE,eAAe,CAAC,CAAC,CAAC;QAC1CuI,mBAAmB,EAAE,IAAI,CAACpB,iBAAiB,CAAC,CAAC,CAAC;QAC9CqB,wBAAwB,EAAE,IAAI,CAACpB,sBAAsB,CAAC,CAAC,CAAC;QACxDqB,gBAAgB,EAAE,IAAI,CAACzB,gBAAgB,CAAC,CAAC,CAAC;QAC1C0B,KAAK,EAAE,EAAE;QACTC,GAAG,EAAE;OACN;IACH;IACA,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAC,QAAQA,CAAA;IACN,IAAIC,WAAW,GAAG;MAChBjL,kBAAkB,EAAE,IAAI,CAACD,WAAW,CAACC,kBAAkB;MACvD;MACA6K,KAAK,EAAE,IAAI,CAAC9K,WAAW,CAAC8K,KAAK;MAC7BC,GAAG,EAAE,IAAI,CAAC/K,WAAW,CAAC+K,GAAG;MACzBN,kBAAkB,EAAE,IAAI,CAACzK,WAAW,CAACyK,kBAAkB;MACvDrH,kBAAkB,EAAE,IAAI,CAACpD,WAAW,CAACoD,kBAAkB;MACvDyH,gBAAgB,EAAE,IAAI,CAAC7K,WAAW,CAAC6K,gBAAgB;MACnDlJ,kBAAkB,EAAE,IAAI,CAAC3B,WAAW,CAAC2B,kBAAkB;MACvDO,iBAAiB,EAAE,IAAI,CAAClC,WAAW,CAACkC,iBAAiB;MACrDyI,mBAAmB,EAAE,IAAI,CAAC3K,WAAW,CAAC2K,mBAAmB;MACzDC,wBAAwB,EAAE,IAAI,CAAC5K,WAAW,CAAC4K;KAC5C;IACDnM,+FAAmB,CAAC0M,iBAAiB,CAACzM,0EAAW,CAAC2L,YAAY,EAAEE,IAAI,CAACa,SAAS,CAACF,WAAW,CAAC,CAAC;IAC5F,IAAI,CAACG,YAAY,EAAE,CAACld,SAAS,EAAE;EACjC;EAEA0C,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAChD,SAAS,GAAGgD,OAAO;IACxB,IAAI,CAACua,YAAY,EAAE,CAACld,SAAS,EAAE;EACjC;EAEAmd,WAAWA,CAAA;IACT,IAAI,IAAI,CAACtL,WAAW,CAACC,kBAAkB,CAACC,GAAG,EAAE;MAC3C,IAAI,CAAC9S,aAAa,CAACme,4BAA4B,CAAC;QAC9Crb,YAAY,EAAE,IAAI,CAAC8P,WAAW,CAACC,kBAAkB,CAACC;OACnD,CAAC,CAAC/R,SAAS,CAACmB,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;UACtC,IAAI,CAACwZ,gBAAgB,CAACuC,iBAAiB,CACrClc,GAAG,CAACE,OAAO,EAAE,QAAQ,CACtB;QACH,CAAC,MAAM;UACL,IAAI,CAAClC,OAAO,CAAC+E,YAAY,CAAC/C,GAAG,CAACoL,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;EACF;EAMA+Q,gBAAgBA,CAAA;IACd,IAAI,CAACvY,SAAS,CAACC,aAAa,CAAC/J,KAAK,EAAE;EACtC;EAEAoP,cAAcA,CAAC5G,KAAY;IACzB,MAAM8Z,KAAK,GAAG9Z,KAAK,CAACE,MAA0B;IAC9C,IAAI4Z,KAAK,CAAC7Z,KAAK,IAAI6Z,KAAK,CAAC7Z,KAAK,CAACtB,MAAM,GAAG,CAAC,EAAE;MACzC,IAAI,CAAC0Z,YAAY,GAAGyB,KAAK,CAAC7Z,KAAK,CAAC,CAAC,CAAC;MAClC,IAAI,CAAC8Z,WAAW,EAAE;IACpB;EACF;EAEAA,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC1B,YAAY,EAAE;MACrB,MAAM2B,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC7B,YAAY,CAAC;MAC3C,IAAI,CAAC7c,aAAa,CAAC2e,4BAA4B,CAAC;QAC9C7c,IAAI,EAAE;UACJgB,YAAY,EAAE,IAAI,CAAC8P,WAAW,CAACC,kBAAkB,CAACC,GAAG;UACrDzV,KAAK,EAAE,IAAI,CAACwf;;OAEf,CAAC,CAAC9b,SAAS,CAACmB,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACG,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACnC,OAAO,CAACqD,aAAa,CAACrB,GAAG,CAACoL,OAAQ,CAAC;UACxC,IAAI,CAAC2Q,YAAY,EAAE,CAACld,SAAS,EAAE;QACjC,CAAC,MAAM;UACL,IAAI,CAACb,OAAO,CAAC+E,YAAY,CAAC/C,GAAG,CAACoL,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;EACF;EAGAsR,gBAAgBA,CAAA;IACd,IAAI,CAAC5e,aAAa,CAAC6e,iCAAiC,CAAC;MACnD/c,IAAI,EAAE;QAAEgB,YAAY,EAAE,IAAI,CAAC8P,WAAW,CAACC,kBAAkB,CAACC;MAAG;KAC9D,CAAC,CAAC/R,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC6Z,gBAAgB,GAAG,CAAC;UACvB5iB,KAAK,EAAE,EAAE;UAAEiH,KAAK,EAAE;SACnB,EAAE,GAAG2B,GAAG,CAACE,OAAO,CAAC0c,GAAG,CAACxZ,CAAC,IAAG;UACxB,OAAO;YAAEhM,KAAK,EAAEgM,CAAC;YAAE/E,KAAK,EAAE+E;UAAC,CAAE;QAC/B,CAAC,CAAC,CAAC;QACH,IAAI+L,+FAAmB,CAAC2L,iBAAiB,CAAC1L,0EAAW,CAAC2L,YAAY,CAAC,IAAI,IAAI,IACtE5L,+FAAmB,CAAC2L,iBAAiB,CAAC1L,0EAAW,CAAC2L,YAAY,CAAC,IAAItP,SAAS,IAC5E0D,+FAAmB,CAAC2L,iBAAiB,CAAC1L,0EAAW,CAAC2L,YAAY,CAAC,IAAI,EAAE,EAAE;UAC1E,IAAIC,eAAe,GAAGC,IAAI,CAACC,KAAK,CAAC/L,+FAAmB,CAAC2L,iBAAiB,CAAC1L,0EAAW,CAAC2L,YAAY,CAAC,CAAC;UACjG,IAAIC,eAAe,CAACG,kBAAkB,IAAI,IAAI,IAAIH,eAAe,CAACG,kBAAkB,IAAI1P,SAAS,EAAE;YACjG,IAAIjR,KAAK,GAAG,IAAI,CAACwf,gBAAgB,CAAC6C,SAAS,CAAErY,CAAM,IAAKA,CAAC,CAACpN,KAAK,IAAI4jB,eAAe,CAACG,kBAAkB,CAAC/jB,KAAK,CAAC;YAC5G,IAAI,CAACsZ,WAAW,CAACyK,kBAAkB,GAAG,IAAI,CAACnB,gBAAgB,CAACxf,KAAK,CAAC;UACpE,CAAC,MAAM;YACL,IAAI,CAACkW,WAAW,CAACyK,kBAAkB,GAAG,IAAI,CAACnB,gBAAgB,CAAC,CAAC,CAAC;UAChE;QACF;MACF;IACF,CAAC,CAAC;EACJ;EAKA8C,WAAWA,CAAA;IACT,IAAI,CAACC,WAAW,GAAG;MACjBnc,YAAY,EAAE,IAAI,CAAC8P,WAAW,CAACC,kBAAkB,CAACC,GAAG;MACrD9Q,SAAS,EAAE,IAAI,CAACtB,SAAS;MACzBuB,QAAQ,EAAE,IAAI,CAACxB;KAChB;IACD,IAAI,IAAI,CAACmS,WAAW,CAAC8K,KAAK,IAAI,IAAI,CAAC9K,WAAW,CAAC+K,GAAG,EAAE;MAClD,IAAI,CAACsB,WAAW,CAAC,QAAQ,CAAC,GAAG;QAAEvB,KAAK,EAAE,IAAI,CAAC9K,WAAW,CAAC8K,KAAK;QAAEC,GAAG,EAAE,IAAI,CAAC/K,WAAW,CAAC+K;MAAG,CAAE;IAC3F;IACA,IAAI,IAAI,CAAC/K,WAAW,CAACyK,kBAAkB,EAAE;MACvC,IAAI,CAAC4B,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAACrM,WAAW,CAACyK,kBAAkB,CAAC/jB,KAAK;IAC5E;IACA,IAAI,IAAI,CAACsZ,WAAW,CAACoD,kBAAkB,CAAC1c,KAAK,EAAE;MAC7C,IAAI,CAAC2lB,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAACrM,WAAW,CAACoD,kBAAkB,CAAC1c,KAAK;IAC5E;IACA,IAAI,OAAO,IAAI,CAACsZ,WAAW,CAAC6K,gBAAgB,CAACnkB,KAAK,KAAK,SAAS,EAAE;MAChE,IAAI,CAAC2lB,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAACrM,WAAW,CAAC6K,gBAAgB,CAACnkB,KAAK;IACzE;IACA,IAAI,IAAI,CAACsZ,WAAW,CAAC2B,kBAAkB,CAACjb,KAAK,EAAE;MAC7C,IAAI,CAAC2lB,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAACrM,WAAW,CAAC2B,kBAAkB,CAACjb,KAAK;IAC5E;IACA,IAAI,IAAI,CAACsZ,WAAW,CAACkC,iBAAiB,CAACxb,KAAK,EAAE;MAC5C,IAAI,CAAC2lB,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAACrM,WAAW,CAACkC,iBAAiB,CAACxb,KAAK;IAC1E;IACA,IAAI,IAAI,CAACsZ,WAAW,CAAC2K,mBAAmB,CAACjkB,KAAK,EAAE;MAC9C,IAAI,CAAC2lB,WAAW,CAAC,aAAa,CAAC,GAAG,IAAI,CAACrM,WAAW,CAAC2K,mBAAmB,CAACjkB,KAAK;IAC9E;IACA,IAAI,IAAI,CAACsZ,WAAW,CAAC4K,wBAAwB,CAAClkB,KAAK,EAAE;MACnD,IAAI,CAAC2lB,WAAW,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAACrM,WAAW,CAAC4K,wBAAwB,CAAClkB,KAAK;IACxF;IAEA,OAAO,IAAI,CAAC2lB,WAAW;EACzB;EAEAC,qBAAqBA,CAACC,GAAsB;IAC1C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAAC/f,MAAM,IAAI,CAAC,KAAK8f,CAAC,CAAC9f,MAAM,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEA0e,YAAYA,CAAA;IACV,OAAO,IAAI,CAACje,aAAa,CAACuf,6BAA6B,CAAC;MACtDzd,IAAI,EAAE,IAAI,CAACkd,WAAW;KACvB,CAAC,CAAC5R,IAAI,CACL0D,0CAAG,CAAC5O,GAAG,IAAG;MACR,IAAIA,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACmd,SAAS,GAAGtd,GAAG,CAACE,OAAO;QAC5B,IAAI,CAACzB,YAAY,GAAGuB,GAAG,CAACC,UAAW;MACrC;IACF,CAAC,CAAC,CACH;EACH;EAIAsd,0BAA0BA,CAAA;IACxB;IACA,IAAI,CAACb,gBAAgB,EAAE;IACvB,IAAI,CAACX,YAAY,EAAE,CAACld,SAAS,EAAE;EACjC;EACA6c,gBAAgBA,CAAA;IACd,IAAI,CAAChC,iBAAiB,CAAC8D,6CAA6C,CAAC;MACnE5d,IAAI,EAAE;QACJ6d,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;;KAEZ,CAAC,CAACxS,IAAI,CACL0D,0CAAG,CAAC5O,GAAG,IAAG;MACR,IAAIA,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACqU,oBAAoB,GAAGxU,GAAG,CAACE,OAAO,EAAEe,MAAM,GAAGjB,GAAG,CAACE,OAAO,CAAC0c,GAAG,CAAC5c,GAAG,IAAG;UACtE,OAAO;YACLsP,cAAc,EAAEtP,GAAG,CAACsP,cAAc;YAClCsB,GAAG,EAAE5Q,GAAG,CAAC4Q;WACV;QACH,CAAC,CAAC,GAAG,EAAE;QAEP,IAAIzB,+FAAmB,CAAC2L,iBAAiB,CAAC1L,0EAAW,CAAC2L,YAAY,CAAC,IAAI,IAAI,IACtE5L,+FAAmB,CAAC2L,iBAAiB,CAAC1L,0EAAW,CAAC2L,YAAY,CAAC,IAAItP,SAAS,IAC5E0D,+FAAmB,CAAC2L,iBAAiB,CAAC1L,0EAAW,CAAC2L,YAAY,CAAC,IAAI,EAAE,EAAE;UAC1E,IAAIC,eAAe,GAAGC,IAAI,CAACC,KAAK,CAAC/L,+FAAmB,CAAC2L,iBAAiB,CAAC1L,0EAAW,CAAC2L,YAAY,CAAC,CAAC;UACjG,IAAIC,eAAe,CAACrK,kBAAkB,IAAI,IAAI,IAAIqK,eAAe,CAACrK,kBAAkB,IAAIlF,SAAS,EAAE;YACjG,IAAIjR,KAAK,GAAG,IAAI,CAACga,oBAAoB,CAACqI,SAAS,CAAErY,CAAM,IAAKA,CAAC,CAACoM,GAAG,IAAIoK,eAAe,CAACrK,kBAAkB,CAACC,GAAG,CAAC;YAC5G,IAAI,CAACF,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAAC6D,oBAAoB,CAACha,KAAK,CAAC;UACxE,CAAC,MAAM;YACL,IAAI,CAACkW,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAAC6D,oBAAoB,CAAC,CAAC,CAAC;UACpE;QACF,CAAC,MACI;UACH,IAAI,CAAC9D,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAAC6D,oBAAoB,CAAC,CAAC,CAAC;QACpE;MACF;IACF,CAAC,CAAC,EACF5F,0CAAG,CAAC,MAAK;MACP;MACA,IAAI,CAAC8N,gBAAgB,EAAE;MACvBiB,UAAU,CAAC,MAAK;QACd,IAAI,CAAC5B,YAAY,EAAE,CAACld,SAAS,EAAE;MACjC,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,CACH,CAACA,SAAS,EAAE;EACf;EA6CAU,YAAYA,CAACsR,GAAQ,EAAEpQ,GAAQ;IAC7B,IAAI,CAAC2R,cAAc,GAAG,EAAE;IACxB,IAAI,CAACtU,aAAa,CAACuC,6BAA6B,CAAC;MAC/CT,IAAI,EAAE;QAAEU,QAAQ,EAAEuQ;MAAG;KACtB,CAAC,CAAChS,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACgT,WAAW,GAAG;UACjB,GAAGnT,GAAG,CAACE,OAAO;UACdmU,eAAe,EAAErU,GAAG,CAACE,OAAO,CAAC0d,gBAAgB,GAAG,IAAI9c,IAAI,CAACd,GAAG,CAACE,OAAO,CAAC0d,gBAAgB,CAAC,GAAGnS,SAAS;UAClG8I,aAAa,EAAEvU,GAAG,CAACE,OAAO,CAAC2d,cAAc,GAAG,IAAI/c,IAAI,CAACd,GAAG,CAACE,OAAO,CAAC2d,cAAc,CAAC,GAAGpS;SACpF;QAED,IAAIzL,GAAG,CAACE,OAAO,CAAC4d,YAAY,EAAE;UAC5B,IAAI,CAAC1L,cAAc,CAACzB,kBAAkB,GAAG,IAAI,CAACoN,eAAe,CAAC,IAAI,CAACvJ,oBAAoB,EAAE,KAAK,EAAExU,GAAG,CAACE,OAAO,CAAC4d,YAAY,CAAC;QAC3H;QACA,IAAI,CAAC1L,cAAc,CAACC,kBAAkB,GAAG,IAAI,CAAC0L,eAAe,CAAC,IAAI,CAACxL,OAAO,CAACC,gBAAgB,EAAE,OAAO,EAAExS,GAAG,CAACE,OAAO,CAACwR,UAAU,CAAC;QAC7H,IAAI1R,GAAG,CAACE,OAAO,CAACoR,UAAU,EAAE;UAC1B,IAAI,CAACc,cAAc,CAAC0B,kBAAkB,GAAG,IAAI,CAACiK,eAAe,CAAC,IAAI,CAACxL,OAAO,CAACkC,gBAAgB,EAAE,OAAO,EAAEzU,GAAG,CAACE,OAAO,CAACoR,UAAU,CAAC;QAC/H,CAAC,MAAM;UACL,IAAI,CAACc,cAAc,CAAC0B,kBAAkB,GAAG,IAAI,CAACvB,OAAO,CAACkC,gBAAgB,CAAC,CAAC,CAAC;QAC3E;QACA,IAAI,CAACrC,cAAc,CAACQ,iBAAiB,GAAG,IAAI,CAACmL,eAAe,CAAC,IAAI,CAACxL,OAAO,CAACO,eAAe,EAAE,OAAO,EAAE9S,GAAG,CAACE,OAAO,CAACoa,SAAS,CAAC;QAE1H,IAAIta,GAAG,CAACE,OAAO,CAAC4d,YAAY,EAAE;UAC5B,IAAI,IAAI,CAAClI,aAAa,EAAE;YACtB,IAAI,CAACA,aAAa,CAAChV,YAAY,GAAGZ,GAAG,CAACE,OAAO,CAAC4d,YAAY;UAC5D;QACF;QACA,IAAI,CAACngB,aAAa,CAAC+B,IAAI,CAACe,GAAG,CAAC;MAC9B;IAEF,CAAC,CAAC;EACJ;EAGAsd,eAAeA,CAACjc,KAAY,EAAE1D,GAAW,EAAEhH,KAAU;IACnD,OAAO0K,KAAK,CAACsZ,IAAI,CAACrZ,IAAI,IAAIA,IAAI,CAAC3D,GAAG,CAAC,KAAKhH,KAAK,CAAC;EAChD;EAGAkZ,eAAeA,CAAC7P,GAAQ,EAAEsB,IAAS;IACjC,IAAI,CAACxC,YAAY,CAACwC,IAAI,CAAC8O,GAAG,EAAEpQ,GAAG,CAAC;EAClC;EAEAwP,SAASA,CAACxP,GAAQ;IAChB,IAAI,CAACmV,aAAa,GAAG;MACnBC,aAAa,EAAE,EAAE;MACjBxY,MAAM,EAAEoO,SAAS;MACjBsK,eAAe,EAAEtK;KAClB;IACD,IAAI,CAAC9N,aAAa,CAAC+B,IAAI,CAACe,GAAG,CAAC;EAC9B;EAKArH,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAO7B,mCAAM,CAAC6B,WAAW,CAAC,CAACuI,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEAqT,cAAcA,CAACxU,GAAQ;IACrB,IAAI,CAAC0S,WAAW,CAACyK,gBAAgB,GAAG,IAAI,CAACzK,WAAW,CAACkB,eAAe,GAAG,IAAI,CAACjb,UAAU,CAAC,IAAI,CAAC+Z,WAAW,CAACkB,eAAe,CAAC,GAAG,EAAE,EAC3H,IAAI,CAAClB,WAAW,CAAC0K,cAAc,GAAG,IAAI,CAAC1K,WAAW,CAACoB,aAAa,GAAG,IAAI,CAACnb,UAAU,CAAC,IAAI,CAAC+Z,WAAW,CAACoB,aAAa,CAAC,GAAG,EAAE,EAEvH,IAAI,CAACyJ,kBAAkB,GAAG;MACxB1K,aAAa,EAAE,IAAI,CAACH,WAAW,CAACG,aAAa;MAC7CjC,UAAU,EAAE,IAAI,CAAC8B,WAAW,CAAC/V,UAAU;MACvCkD,QAAQ,EAAE,IAAI,CAAC6S,WAAW,CAAC8K,GAAG;MAC9B3M,UAAU,EAAE,IAAI,CAACc,cAAc,CAAC0B,kBAAkB,GAAG,IAAI,CAAC1B,cAAc,CAAC0B,kBAAkB,CAAC1c,KAAK,GAAG,IAAI;MACxGma,SAAS,EAAE,IAAI,CAAC4B,WAAW,CAAC5B,SAAS;MACrCO,SAAS,EAAE,IAAI,CAACqB,WAAW,CAACrB,SAAS;MACrC4B,KAAK,EAAE,IAAI,CAACP,WAAW,CAACO,KAAK;MAC7B2G,WAAW,EAAE,IAAI,CAAClH,WAAW,CAACK,WAAW;MACzC9B,UAAU,EAAE,IAAI,CAACU,cAAc,CAACC,kBAAkB,GAAG,IAAI,CAACD,cAAc,CAACC,kBAAkB,CAACjb,KAAK,GAAG,IAAI;MACxGwc,MAAM,EAAE,IAAI,CAACT,WAAW,CAACS,MAAM;MAC/B0G,SAAS,EAAE,IAAI,CAAClI,cAAc,CAACQ,iBAAiB,GAAG,IAAI,CAACR,cAAc,CAACQ,iBAAiB,CAACxb,KAAK,GAAG,IAAI;MACrGwmB,gBAAgB,EAAE,IAAI,CAACzK,WAAW,CAACyK,gBAAgB;MACnDC,cAAc,EAAE,IAAI,CAAC1K,WAAW,CAAC0K;KAClC;IACH,IAAI,CAAC9c,UAAU,EAAE;IACjB,IAAI,IAAI,CAACnD,KAAK,CAACoD,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACjD,OAAO,CAACkD,aAAa,CAAC,IAAI,CAACtD,KAAK,CAACoD,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAAClD,aAAa,CAACogB,0BAA0B,CAAC;MAC5Cte,IAAI,EAAE,IAAI,CAACoe;KACZ,CAAC,CAAC9S,IAAI,CACL0D,0CAAG,CAAC5O,GAAG,IAAG;MACR,IAAIA,GAAG,CAACG,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACnC,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;QAClCZ,GAAG,CAACa,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACtD,OAAO,CAAC+E,YAAY,CAAC/C,GAAG,CAACoL,OAAQ,CAAC;QACvC3K,GAAG,CAACa,KAAK,EAAE;MACb;IACF,CAAC,CAAC,EACFqN,gDAAS,CAAC,MAAM,IAAI,CAACoN,YAAY,EAAE,CAAC,CACrC,CAACld,SAAS,EAAE;EACf;EAGAsf,QAAQA,CAAC1d,GAAQ;IACf,IAAI2d,OAAO,GAAkB;MAC3B9K,aAAa,EAAE,IAAI,CAACH,WAAW,CAACG,aAAa;MAC7CjC,UAAU,EAAE,IAAI,CAAC8B,WAAW,CAAC/V,UAAU;MACvCkD,QAAQ,EAAE,IAAI,CAAC6S,WAAW,CAAC8K,GAAG;MAC9B3M,UAAU,EAAE,IAAI,CAAC6B,WAAW,CAAC7B,UAAU;MACvCC,SAAS,EAAE,IAAI,CAAC4B,WAAW,CAAC5B,SAAS;MACrCO,SAAS,EAAE,IAAI,CAACqB,WAAW,CAACrB,SAAS;MACrC4B,KAAK,EAAE,IAAI,CAACP,WAAW,CAACO,KAAK;MAC7B2G,WAAW,EAAE,IAAI,CAAClH,WAAW,CAACK,WAAW;MACzC9B,UAAU,EAAE,IAAI,CAACyB,WAAW,CAACzB,UAAU;MACvCkC,MAAM,EAAE,IAAI,CAACT,WAAW,CAACS,MAAM;MAC/B0G,SAAS,EAAE,IAAI,CAACnH,WAAW,CAACmH;KAC7B;IACD,IAAI,CAACxc,aAAa,CAACogB,0BAA0B,CAAC;MAC5Cte,IAAI,EAAEwe;KACP,CAAC,CAACvf,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACG,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACnC,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;QAClCZ,GAAG,CAACa,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EAEJ;EAEAtE,OAAOA,CAACyD,GAAQ;IACdA,GAAG,CAACa,KAAK,EAAE;EACb;EAEA+c,YAAYA,CAACvb,IAAS,EAAE7D,EAAQ;IAC9B,MAAMqf,KAAK,GAAGrf,EAAE,GAAGA,EAAE,GAAG,IAAI,CAACyR,WAAW,CAACC,kBAAkB,CAACC,GAAG;IAC/D,IAAI,CAAC3G,MAAM,CAACsU,QAAQ,CAAC,CAAC,+BAA+Bzb,IAAI,EAAE,EAAEwb,KAAK,CAAC,CAAC;EACtE;EAEA7N,4BAA4BA,CAAC3N,IAAS,EAAE5D,WAAgB,EAAEG,OAAY;IACpE,IAAI,CAAC4K,MAAM,CAACsU,QAAQ,CAAC,CAAC,+BAA+Bzb,IAAI,EAAE,EAAE5D,WAAW,EAAEG,OAAO,CAAC,CAAC;EACrF;EAEA4R,cAAcA,CAAClP,IAAS;IACtB,IAAIyc,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB,IAAI,CAAC1gB,aAAa,CAAC2gB,oCAAoC,CAAC;QACtD7e,IAAI,EAAEmC,IAAI,CAAC8O;OACZ,CAAC,CAAChS,SAAS,CAACmB,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACnC,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;QACpC;MACF,CAAC,CAAC;IACJ;EACF;EAIAN,UAAUA,CAAA;IACR,IAAI,CAACnD,KAAK,CAAC8D,KAAK,EAAE;IAClB,IAAI,CAAC9D,KAAK,CAAC+D,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACwR,WAAW,CAAC8K,GAAG,CAAC;IACnD,IAAI,CAACrgB,KAAK,CAAC+D,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACqc,kBAAkB,CAAC3M,UAAU,CAAC;IACjE,IAAI,CAACzT,KAAK,CAAC8gB,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACV,kBAAkB,CAAC3M,UAAU,EAAE,EAAE,CAAC;IAC5E,IAAI,CAACzT,KAAK,CAAC+D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACwR,WAAW,CAAC9V,MAAM,CAAC;IACpD,IAAI,CAACO,KAAK,CAAC8gB,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAACV,kBAAkB,CAAC1K,aAAa,EAAE,EAAE,CAAC;IACjF;IACA;IACA;IACA,IAAI,CAAC1V,KAAK,CAAC+gB,OAAO,CAAC,QAAQ,EAAE,IAAI,CAACX,kBAAkB,CAACtK,KAAK,EAAE,IAAI,CAAC1J,OAAO,CAAC4U,WAAW,CAAC;IACrF,IAAI,CAAChhB,KAAK,CAACihB,aAAa,CAAC,QAAQ,EAAE,IAAI,CAACb,kBAAkB,CAACpK,MAAM,CAAC;IAClE,IAAI,CAAChW,KAAK,CAAC+D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACqc,kBAAkB,CAAC1D,SAAS,CAAC;IAC9D,IAAI,CAAC1c,KAAK,CAAC+D,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACyQ,cAAc,CAAC0B,kBAAkB,CAAC1c,KAAK,CAAC;IAC3E,IAAI,CAACwG,KAAK,CAAC+D,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACyQ,cAAc,CAACC,kBAAkB,CAACjb,KAAK,CAAC;IAC3E,IAAI,IAAI,CAAC+b,WAAW,CAACyK,gBAAgB,EAAE;MACrC,IAAI,CAAChgB,KAAK,CAAC+D,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACwR,WAAW,CAAC0K,cAAc,CAAC;IAClE;IACA,IAAI,IAAI,CAAC1K,WAAW,CAAC0K,cAAc,EAAE;MACnC,IAAI,CAACjgB,KAAK,CAAC+D,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACwR,WAAW,CAACyK,gBAAgB,CAAC;IACpE;IACA,IAAI,CAAChgB,KAAK,CAACkhB,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC3L,WAAW,CAACyK,gBAAgB,GAAG,IAAI,CAACzK,WAAW,CAACyK,gBAAgB,GAAG,EAAE,EAAE,IAAI,CAACzK,WAAW,CAAC0K,cAAc,GAAG,IAAI,CAAC1K,WAAW,CAAC0K,cAAc,GAAG,EAAE,CAAC;EAC9L;EAEAkB,uBAAuBA,CAAA;IACrB,IAAI,CAACnhB,KAAK,CAAC8D,KAAK,EAAE;IAClB,IAAI,CAAC9D,KAAK,CAAC+D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACiU,aAAa,CAAChV,YAAY,CAAC;IAC5D,IAAI,CAAChD,KAAK,CAAC+D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACiU,aAAa,CAACC,aAAa,CAAC;IAC7D,IAAI,CAACjY,KAAK,CAAC8gB,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC9I,aAAa,CAACC,aAAa,EAAE,EAAE,CAAC;IAC1E,IAAI,CAACjY,KAAK,CAACohB,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAACpJ,aAAa,CAACvY,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC;IAChF,IAAI,CAACO,KAAK,CAACohB,sBAAsB,CAAC,SAAS,EAAE,IAAI,CAACpJ,aAAa,CAACG,eAAe,EAAE,CAAC,EAAE,GAAG,CAAC;EAC1F;EAGAN,gBAAgBA,CAAChV,GAAQ;IACvB,IAAI,CAACmV,aAAa,CAAChV,YAAY,GAAG,IAAI,CAAC8P,WAAW,CAACC,kBAAkB,CAACC,GAAG,EACvE,IAAI,CAACmO,uBAAuB,EAAE;IAChC,IAAI,IAAI,CAACnhB,KAAK,CAACoD,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACjD,OAAO,CAACkD,aAAa,CAAC,IAAI,CAACtD,KAAK,CAACoD,aAAa,CAAC;MACpD;IACF;IACA,IAAI,CAACyY,qBAAqB,CAACwF,yCAAyC,CAAC;MACnErf,IAAI,EAAE,IAAI,CAACgW;KACZ,CAAC,CAAC1K,IAAI,CACL0D,0CAAG,CAAC5O,GAAG,IAAG;MACR,IAAIA,GAAG,CAACG,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACnC,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;QAClCZ,GAAG,CAACa,KAAK,EAAE;MACb;IACF,CAAC,CAAC,EACFqN,gDAAS,CAAC,MAAM,IAAI,CAACoN,YAAY,EAAE,CAAC,CACrC,CAACld,SAAS,EAAE;EACf,CAAC,CAAE;EACGuS,aAAaA,CAAC8N,MAAW,EAAEnd,IAAS;IAAA,IAAAod,KAAA;IAAA,OAAAC,4JAAA;MACxCD,KAAI,CAAC9F,YAAY,GAAGtX,IAAI;MACxBod,KAAI,CAAC7G,cAAc,GAAG,EAAE;MACxB6G,KAAI,CAAC7F,WAAW,GAAG,CAAC;MACpB6F,KAAI,CAACzE,kBAAkB,GAAG,CAAC,CAAC,CAAC;MAC7ByE,KAAI,CAACvH,mBAAmB,GAAG,IAAI,CAAC,CAAC;MACjC;MACAuH,KAAI,CAAC5E,iBAAiB,GAAG,KAAK;MAC9B4E,KAAI,CAAC3E,uBAAuB,GAAG,CAAC;MAChC2E,KAAI,CAAC5F,mBAAmB,GAAG,CAAC;MAC5B4F,KAAI,CAAC3F,gBAAgB,GAAG,CAAC;MACzB2F,KAAI,CAAC1E,mBAAmB,GAAG,IAAI;MAE/B;MACA,IAAI;QACF,MAAM4E,QAAQ,SAASF,KAAI,CAACvF,gBAAgB,CAAC0F,qBAAqB,CAACvd,IAAI,CAAC8O,GAAG,CAAC,CAAC0O,SAAS,EAAE;QAExF,IAAIF,QAAQ,IAAIA,QAAQ,CAAClf,UAAU,KAAK,CAAC,IAAIkf,QAAQ,CAACnf,OAAO,EAAE;UAC7D;UACAif,KAAI,CAACzE,kBAAkB,GAAG2E,QAAQ,CAACnf,OAAO,CAACsf,mBAAmB,IAAI,CAAC;UACnE;UACA,IAAIH,QAAQ,CAACnf,OAAO,CAAC2R,gBAAgB,KAAK,CAAC,EAAE;YAAE;YAC7CsN,KAAI,CAACvH,mBAAmB,GAAG,KAAK;UAClC,CAAC,MAAM;YACLuH,KAAI,CAACvH,mBAAmB,GAAG,IAAI;UACjC;UAEA;UACAuH,KAAI,CAAC1E,mBAAmB,GAAG,IAAI;UAC/B0E,KAAI,CAAC5E,iBAAiB,GAAG,KAAK;UAC9B4E,KAAI,CAAC3E,uBAAuB,GAAG,CAAC;UAEhC;UACA,IAAI6E,QAAQ,CAACnf,OAAO,CAACuf,KAAK,IAAIC,KAAK,CAACC,OAAO,CAACN,QAAQ,CAACnf,OAAO,CAACuf,KAAK,CAAC,EAAE;YACnE;YACAN,KAAI,CAAC7G,cAAc,GAAG+G,QAAQ,CAACnf,OAAO,CAACuf,KAAK,CAAC7C,GAAG,CAAEgD,KAAU,KAAM;cAChEC,QAAQ,EAAER,QAAQ,CAACnf,OAAO,CAACI,QAAQ,IAAIyB,IAAI,CAAC8O,GAAG;cAC/CiP,YAAY,EAAET,QAAQ,CAACnf,OAAO,CAAC6f,YAAY;cAC3C9I,SAAS,EAAE2I,KAAK,CAACI,SAAS,IAAI,EAAE;cAChC1I,KAAK,EAAEsI,KAAK,CAACK,KAAK,IAAI,EAAE;cACxB9I,UAAU,EAAEyI,KAAK,CAACM,UAAU,IAAI,CAAC;cACjC1I,MAAM,EAAEoI,KAAK,CAACO,MAAM,IAAI,CAAC;cACzBC,OAAO,EAAER,KAAK,CAAClC,OAAO,IAAI,CAAC;cAC3BnmB,kBAAkB,EAAEqoB,KAAK,CAACroB,kBAAkB,IAAIqoB,KAAK,CAACroB,kBAAkB,GAAG,CAAC,GAAGqoB,KAAK,CAACroB,kBAAkB,GAAGA,+EAAkB,CAAC8oB,GAAG;cAChIC,OAAO,EAAEV,KAAK,CAACW,OAAO,IAAI,EAAE;cAC5BC,gBAAgB,EAAEZ,KAAK,CAAC/N;aACzB,CAAC,CAAC;YACHsN,KAAI,CAAC/H,cAAc,EAAE;UACvB,CAAC,MAAM,CAEP;QACF,CAAC,MAAM,CAEP;MACF,CAAC,CAAC,OAAOqJ,KAAK,EAAE;QACd/c,OAAO,CAAC+c,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;MAEAtB,KAAI,CAACxhB,aAAa,CAAC+B,IAAI,CAACwf,MAAM,EAAE;QAC9BwB,OAAO,EAAE3e,IAAI;QACb4e,oBAAoB,EAAE;OACvB,CAAC;IAAC;EACL;EAEA;EACA1I,kBAAkBA,CAAA;IAChB,IAAI,CAACyC,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACpC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACV,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAAC0B,WAAW,GAAG,CAAC;IACpB,IAAI,CAACE,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACD,mBAAmB,GAAG,CAAC;IAC5B,IAAI,CAACkB,mBAAmB,GAAG,IAAI;IAE/B;IACA,IAAI,CAACzc,OAAO,CAACqD,aAAa,CAAC,eAAe,CAAC;EAC7C;EACA;EACAgV,gBAAgBA,CAAA;IACd,IAAI,CAACiC,cAAc,CAAC/U,IAAI,CAAC;MACvBsc,QAAQ,EAAE,IAAI,CAACxG,YAAY,EAAExI,GAAG,IAAI,CAAC;MACrCoG,SAAS,EAAE,EAAE;MACbK,KAAK,EAAE,EAAE;MACTH,UAAU,EAAE,CAAC;MACbK,MAAM,EAAE,CAAC;MACT4I,OAAO,EAAE,CAAC;MACV7oB,kBAAkB,EAAEA,+EAAkB,CAAC8oB,GAAG;MAC1CC,OAAO,EAAE;KACV,CAAC;EACJ;EACA;EACM/J,gBAAgBA,CAAA;IAAA,IAAAqK,MAAA;IAAA,OAAAxB,4JAAA;MACpB,IAAI;QACF,IAAI,CAACwB,MAAI,CAACvH,YAAY,EAAExI,GAAG,EAAE;UAC3B+P,MAAI,CAAC5iB,OAAO,CAAC+E,YAAY,CAAC,QAAQ,CAAC;UACnC;QACF;QAEA,MAAM8d,OAAO,GAAG;UACdjgB,YAAY,EAAEggB,MAAI,CAAClQ,WAAW,EAAEC,kBAAkB,EAAEC,GAAG,IAAI,CAAC;UAC5DtQ,QAAQ,EAAEsgB,MAAI,CAACvH,YAAY,CAACxI;SAC7B;QAED,MAAMwO,QAAQ,SAASuB,MAAI,CAAChH,gBAAgB,CAACrD,gBAAgB,CAACsK,OAAO,CAAC,CAACtB,SAAS,EAAE;QAClF,IAAIF,QAAQ,EAAEyB,OAAO,IAAIzB,QAAQ,CAACplB,IAAI,EAAE;UACtC,MAAM8mB,YAAY,GAAG1B,QAAQ,CAACplB,IAAI,CAAC2iB,GAAG,CAAEpY,CAAM,KAAM;YAClDsb,YAAY,EAAEtb,CAAC,CAACub,YAAY;YAC5BF,QAAQ,EAAEe,MAAI,CAACvH,YAAY,EAAExI,GAAG;YAChCoG,SAAS,EAAEzS,CAAC,CAACwb,SAAS;YACtB1I,KAAK,EAAE9S,CAAC,CAACyb,KAAK,IAAI,EAAE;YACpB9I,UAAU,EAAE3S,CAAC,CAAC0b,UAAU;YACxB1I,MAAM,EAAEhT,CAAC,CAAC2b,MAAM;YAChBC,OAAO,EAAE5b,CAAC,CAACkZ,OAAO;YAClBnmB,kBAAkB,EAAEA,+EAAkB,CAACypB,IAAI;YAC3CV,OAAO,EAAE9b,CAAC,CAAC+b;WACZ,CAAC,CAAC;UACHK,MAAI,CAACtI,cAAc,CAAC/U,IAAI,CAAC,GAAGwd,YAAY,CAAC;UACzCH,MAAI,CAACxJ,cAAc,EAAE;UACrBwJ,MAAI,CAAC5iB,OAAO,CAACqD,aAAa,CAAC,UAAU,CAAC;QACxC,CAAC,MAAM;UACLuf,MAAI,CAAC5iB,OAAO,CAAC+E,YAAY,CAACsc,QAAQ,EAAErhB,OAAO,IAAI,UAAU,CAAC;QAC5D;MACF,CAAC,CAAC,OAAOyiB,KAAK,EAAE;QACd/c,OAAO,CAAC+c,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCG,MAAI,CAAC5iB,OAAO,CAAC+E,YAAY,CAAC,UAAU,CAAC;MACvC;IAAC;EACH;EAEA;EACM0T,gBAAgBA,CAAA;IAAA,IAAAwK,MAAA;IAAA,OAAA7B,4JAAA;MACpB,IAAI;QACF,IAAI,CAAC6B,MAAI,CAAC5H,YAAY,EAAExI,GAAG,EAAE;UAC3BoQ,MAAI,CAACjjB,OAAO,CAAC+E,YAAY,CAAC,QAAQ,CAAC;UACnC;QACF;QAEA,MAAM8d,OAAO,GAAG;UACdjgB,YAAY,EAAEqgB,MAAI,CAACvQ,WAAW,EAAEC,kBAAkB,EAAEC,GAAG,IAAI,CAAC;UAC5DtQ,QAAQ,EAAE2gB,MAAI,CAAC5H,YAAY,CAACxI;SAC7B;QAED,MAAMwO,QAAQ,SAAS4B,MAAI,CAACrH,gBAAgB,CAACnD,gBAAgB,CAACoK,OAAO,CAAC,CAACtB,SAAS,EAAE;QAClF,IAAIF,QAAQ,EAAEyB,OAAO,IAAIzB,QAAQ,CAACplB,IAAI,EAAE;UACtC,MAAMinB,YAAY,GAAG7B,QAAQ,CAACplB,IAAI,CAAC2iB,GAAG,CAAEpY,CAAM,KAAM;YAClDsb,YAAY,EAAEtb,CAAC,CAACub,YAAY;YAC5BF,QAAQ,EAAEoB,MAAI,CAAC5H,YAAY,EAAExI,GAAG;YAChCoG,SAAS,EAAEzS,CAAC,CAACwb,SAAS;YACtB1I,KAAK,EAAE9S,CAAC,CAACyb,KAAK,IAAI,EAAE;YACpB9I,UAAU,EAAE3S,CAAC,CAAC0b,UAAU;YACxB1I,MAAM,EAAEhT,CAAC,CAAC2b,MAAM;YAChBC,OAAO,EAAE5b,CAAC,CAACkZ,OAAO;YAClBnmB,kBAAkB,EAAEA,+EAAkB,CAAC4pB,EAAE;YAAE;YAC3Cb,OAAO,EAAE9b,CAAC,CAAC+b,OAAO,IAAI;WACvB,CAAC,CAAC;UACHU,MAAI,CAAC3I,cAAc,CAAC/U,IAAI,CAAC,GAAG2d,YAAY,CAAC;UACzCD,MAAI,CAAC7J,cAAc,EAAE;UACrB6J,MAAI,CAACjjB,OAAO,CAACqD,aAAa,CAAC,UAAU,CAAC;QACxC,CAAC,MAAM;UACL4f,MAAI,CAACjjB,OAAO,CAAC+E,YAAY,CAACsc,QAAQ,EAAErhB,OAAO,IAAI,UAAU,CAAC;QAC5D;MACF,CAAC,CAAC,OAAOyiB,KAAK,EAAE;QACd/c,OAAO,CAAC+c,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCQ,MAAI,CAACjjB,OAAO,CAAC+E,YAAY,CAAC,UAAU,CAAC;MACvC;IAAC;EACH;EAEA;EACA8T,mBAAmBA,CAACrc,KAAa;IAC/B,MAAMuH,IAAI,GAAG,IAAI,CAACuW,cAAc,CAAC9d,KAAK,CAAC;IACvC,IAAI,CAAC8d,cAAc,CAAClU,MAAM,CAAC5J,KAAK,EAAE,CAAC,CAAC;IACpC,IAAI,CAAC4c,cAAc,EAAE;EACvB;EAEA;EACAA,cAAcA,CAAA;IACZ,IAAI,CAACkC,WAAW,GAAG,IAAI,CAAChB,cAAc,CAAC8I,MAAM,CAAC,CAACC,GAAG,EAAEtf,IAAI,KAAI;MAC1D,OAAOsf,GAAG,GAAItf,IAAI,CAACoV,UAAU,GAAGpV,IAAI,CAACyV,MAAO;IAC9C,CAAC,EAAE,CAAC,CAAC;IACL,IAAI,CAAC8J,mBAAmB,EAAE;EAC5B;EAEA;EACAA,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAAC/H,mBAAmB,GAAGgI,IAAI,CAACC,KAAK,CAAC,IAAI,CAAClI,WAAW,GAAG,IAAI,CAAC;IAC9D,IAAI,CAACE,gBAAgB,GAAG,IAAI,CAACF,WAAW,GAAG,IAAI,CAACC,mBAAmB;EACrE;EAEA;EACA1B,cAAcA,CAAC4J,MAAc;IAC3B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAAClgB,MAAM,CAAC6f,MAAM,CAAC;EACnB;EAIA;EACMhJ,aAAaA,CAAChY,GAAQ;IAAA,IAAAshB,MAAA;IAAA,OAAA3C,4JAAA;MAC1B,IAAI2C,MAAI,CAACzJ,cAAc,CAACrX,MAAM,KAAK,CAAC,EAAE;QACpC8gB,MAAI,CAAC/jB,OAAO,CAAC+E,YAAY,CAAC,UAAU,CAAC;QACrC;MACF;MAEA;MACA,MAAMif,YAAY,GAAGD,MAAI,CAACzJ,cAAc,CAAC/T,MAAM,CAACxC,IAAI,IAClD,CAACA,IAAI,CAACkV,SAAS,CAACgL,IAAI,EAAE,CACvB;MAED,IAAID,YAAY,CAAC/gB,MAAM,GAAG,CAAC,EAAE;QAC3B8gB,MAAI,CAAC/jB,OAAO,CAAC+E,YAAY,CAAC,iBAAiB,CAAC;QAC5C;MACF;MAAE,IAAI;QACJ,MAAM8d,OAAO,GAAG;UACdxhB,OAAO,EAAE0iB,MAAI,CAAC1I,YAAY,CAACxI,GAAG;UAC9BqR,KAAK,EAAEH,MAAI,CAACzJ,cAAc;UAC1B6J,WAAW,EAAEJ,MAAI,CAACrH,kBAAkB;UAAE;UACtC;UACA0H,UAAU,EAAEL,MAAI,CAACtH,mBAAmB;UAAE;UACtC4H,UAAU,EAAEN,MAAI,CAACxH,iBAAiB;UAAI;UACtC+H,aAAa,EAAEP,MAAI,CAACvH,uBAAuB,CAAC;SAC7C;QAED,MAAM6E,QAAQ,SAAS0C,MAAI,CAACnI,gBAAgB,CAACnB,aAAa,CAACoI,OAAO,CAAC,CAACtB,SAAS,EAAE;QAC/E,IAAIF,QAAQ,EAAEyB,OAAO,EAAE;UACrBiB,MAAI,CAAC/jB,OAAO,CAACqD,aAAa,CAAC,SAAS,CAAC;UACrCZ,GAAG,CAACa,KAAK,EAAE;QACb,CAAC,MAAM;UACLygB,MAAI,CAAC/jB,OAAO,CAAC+E,YAAY,CAACsc,QAAQ,EAAErhB,OAAO,IAAI,MAAM,CAAC;QACxD;MACF,CAAC,CAAC,OAAOyiB,KAAK,EAAE;QACdsB,MAAI,CAAC/jB,OAAO,CAAC+E,YAAY,CAAC,SAAS,CAAC;MACtC;IAAC;EACH;EAEA;EACMwf,eAAeA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAApD,4JAAA;MACnB,IAAI;QACF,MAAMza,IAAI,SAA2B6d,MAAI,CAAC5I,gBAAgB,CAAC2I,eAAe,CAACC,MAAI,CAACnJ,YAAY,CAACxI,GAAG,CAAC,CAAC0O,SAAS,EAAE;QAC7G,IAAI5a,IAAI,EAAE;UACR,MAAMS,GAAG,GAAG3F,MAAM,CAACgjB,GAAG,CAACC,eAAe,CAAC/d,IAAI,CAAC;UAC5C,MAAMge,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAG1d,GAAG;UACfud,IAAI,CAACI,QAAQ,GAAG,OAAOP,MAAI,CAACnJ,YAAY,CAAChI,UAAU,IAAImR,MAAI,CAACnJ,YAAY,CAAChc,MAAM,OAAO;UACtFslB,IAAI,CAAC7oB,KAAK,EAAE;UACZ2F,MAAM,CAACgjB,GAAG,CAACO,eAAe,CAAC5d,GAAG,CAAC;QACjC,CAAC,MAAM;UACLod,MAAI,CAACxkB,OAAO,CAAC+E,YAAY,CAAC,iBAAiB,CAAC;QAC9C;MACF,CAAC,CAAC,OAAO0d,KAAK,EAAE;QACd+B,MAAI,CAACxkB,OAAO,CAAC+E,YAAY,CAAC,SAAS,CAAC;MACtC;IAAC;EACH;EAEA;EACAiW,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACV,cAAc,CAACrX,MAAM,KAAK,CAAC,EAAE;MACpC,IAAI,CAACjD,OAAO,CAAC+E,YAAY,CAAC,YAAY,CAAC;MACvC;IACF;IAEA,IAAI;MACF;MACA,MAAMkgB,YAAY,GAAG,IAAI,CAACC,oBAAoB,EAAE;MAEhD;MACA,MAAMC,WAAW,GAAG1jB,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,mDAAmD,CAAC;MAClG,IAAIyjB,WAAW,EAAE;QACfA,WAAW,CAACP,QAAQ,CAACljB,IAAI,EAAE;QAC3ByjB,WAAW,CAACP,QAAQ,CAACQ,KAAK,CAACH,YAAY,CAAC;QACxCE,WAAW,CAACP,QAAQ,CAACthB,KAAK,EAAE;QAE5B;QACA6hB,WAAW,CAAChgB,MAAM,GAAG;UACnBwa,UAAU,CAAC,MAAK;YACdwF,WAAW,CAACE,KAAK,EAAE;YACnB;UACF,CAAC,EAAE,GAAG,CAAC;QACT,CAAC;MACH,CAAC,MAAM;QACL,IAAI,CAACrlB,OAAO,CAAC+E,YAAY,CAAC,yBAAyB,CAAC;MACtD;IACF,CAAC,CAAC,OAAO0d,KAAK,EAAE;MACd/c,OAAO,CAAC+c,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChC,IAAI,CAACziB,OAAO,CAAC+E,YAAY,CAAC,YAAY,CAAC;IACzC;EACF;EAEA;EACQmgB,oBAAoBA,CAAA;IAC1B;IACA,MAAMI,QAAQ,GAAG7U,sFAAkB;IAEnC;IACA,MAAM8U,WAAW,GAAG,IAAIziB,IAAI,EAAE,CAAC0iB,kBAAkB,CAAC,OAAO,CAAC;IAC1D,MAAMC,aAAa,GAAG,IAAI,CAAC/S,WAAW,CAACC,kBAAkB,EAAErB,cAAc,IAAI,EAAE;IAE/E;IACA,IAAIoU,SAAS,GAAG,EAAE;IAClB,IAAI,CAACpL,cAAc,CAACqL,OAAO,CAAC,CAAC5hB,IAAI,EAAEvH,KAAK,KAAI;MAC1C,MAAMopB,QAAQ,GAAG7hB,IAAI,CAACoV,UAAU,GAAGpV,IAAI,CAACyV,MAAM;MAC9C,MAAMqM,aAAa,GAAG,IAAI,CAAC/L,oBAAoB,CAAC/V,IAAI,CAACxK,kBAAkB,CAAC;MACxE,MAAMusB,IAAI,GAAG/hB,IAAI,CAACuV,KAAK,IAAI,EAAE;MAC7BoM,SAAS,IAAI;;sCAEmBlpB,KAAK,GAAG,CAAC;kBAC7BuH,IAAI,CAACkV,SAAS;qCACK,IAAI,CAACY,cAAc,CAAC9V,IAAI,CAACoV,UAAU,CAAC;sCACnC2M,IAAI;sCACJ/hB,IAAI,CAACyV,MAAM;qCACZ,IAAI,CAACK,cAAc,CAAC+L,QAAQ,CAAC;sCAC5BC,aAAa;;SAE1C;IACL,CAAC,CAAC;IAEF;IACA,MAAME,iBAAiB,GAAG,IAAI,CAACtJ,mBAAmB,GAAG;;YAE7C,IAAI,CAACF,iBAAiB,KAAK,IAAI,CAACC,uBAAuB,MAAM,IAAI,CAAC3C,cAAc,CAAC,IAAI,CAAC0B,mBAAmB,CAAC;;OAE/G,GAAG,EAAE;IAER;IACA,MAAMyK,IAAI,GAAGV,QAAQ,CAClBW,OAAO,CAAC,oBAAoB,EAAER,aAAa,CAAC,CAC5CQ,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC5K,YAAY,EAAEhI,UAAU,IAAI,EAAE,CAAC,CAC9D4S,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC5K,YAAY,EAAEhc,MAAM,IAAI,EAAE,CAAC,CACtD4mB,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC5K,YAAY,EAAE/F,aAAa,IAAI,EAAE,CAAC,CACpE2Q,OAAO,CAAC,gBAAgB,EAAEV,WAAW,CAAC,CACtCU,OAAO,CAAC,gBAAgB,EAAEP,SAAS,CAAC,CACpCO,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAACpM,cAAc,CAAC,IAAI,CAACyB,WAAW,CAAC,CAAC,CACrE2K,OAAO,CAAC,wBAAwB,EAAEF,iBAAiB,CAAC,CACpDE,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAACpM,cAAc,CAAC,IAAI,CAAC2B,gBAAgB,CAAC,CAAC,CACvEyK,OAAO,CAAC,oBAAoB,EAAE,IAAInjB,IAAI,EAAE,CAACojB,cAAc,CAAC,OAAO,CAAC,CAAC;IAEpE,OAAOF,IAAI;EACb;EAGA;EACM3L,aAAaA,CAAC5X,GAAQ;IAAA,IAAA0jB,MAAA;IAAA,OAAA/E,4JAAA;MAC1B,IAAI+E,MAAI,CAAC7L,cAAc,CAACrX,MAAM,KAAK,CAAC,EAAE;QACpCkjB,MAAI,CAACnmB,OAAO,CAAC+E,YAAY,CAAC,UAAU,CAAC;QACrC;MACF;MAEA,IAAI,CAACohB,MAAI,CAACzJ,kBAAkB,EAAE;QAC5ByJ,MAAI,CAACnmB,OAAO,CAAC+E,YAAY,CAAC,UAAU,CAAC;QACrC;MACF;MAEA,IAAI;QACF,MAAMsc,QAAQ,SAAS8E,MAAI,CAACvK,gBAAgB,CAACvB,aAAa,CAAC8L,MAAI,CAACzJ,kBAAkB,CAAC,CAAC6E,SAAS,EAAE;QAE/F,IAAIF,QAAQ,CAACyB,OAAO,EAAE;UACpBqD,MAAI,CAACnmB,OAAO,CAACqD,aAAa,CAAC,UAAU,CAAC;UACtCqC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;YACtBwe,WAAW,EAAEgC,MAAI,CAACzJ,kBAAkB;YACpC1c,OAAO,EAAEqhB,QAAQ,CAACrhB;WACnB,CAAC;QACJ,CAAC,MAAM;UACLmmB,MAAI,CAACnmB,OAAO,CAAC+E,YAAY,CAACsc,QAAQ,CAACrhB,OAAO,IAAI,SAAS,CAAC;UACxD0F,OAAO,CAAC+c,KAAK,CAAC,UAAU,EAAEpB,QAAQ,CAACrhB,OAAO,CAAC;QAC7C;QAEAyC,GAAG,CAACa,KAAK,EAAE;MACb,CAAC,CAAC,OAAOmf,KAAK,EAAE;QACd0D,MAAI,CAACnmB,OAAO,CAAC+E,YAAY,CAAC,SAAS,CAAC;QACpCW,OAAO,CAAC+c,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IAAC;EACH;EAEA;EACA3I,oBAAoBA,CAAC+L,aAAiC;IACpD,QAAQA,aAAa;MACnB,KAAKtsB,+EAAkB,CAACypB,IAAI;QAC1B,OAAO,MAAM;MACf,KAAKzpB,+EAAkB,CAAC8oB,GAAG;QACzB,OAAO,KAAK;MACd,KAAK9oB,+EAAkB,CAAC4pB,EAAE;QACxB,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF;EAEAvP,sBAAsBA,CAACwS,MAAc;IACnC,QAAQA,MAAM;MACZ,KAAKlV,yFAAmB,CAACmV,GAAG;QAC1B,OAAO,KAAK;MACd,KAAKnV,yFAAmB,CAACoV,GAAG;QAC1B,OAAO,KAAK;MACd,KAAKpV,yFAAmB,CAACqV,GAAG;QAC1B,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;;;uCAnhCW7W,4BAA4B,EAAA/V,gEAAA,CAAA2N,2EAAA,GAAA3N,gEAAA,CAAA6N,yEAAA,GAAA7N,gEAAA,CAAA+N,4DAAA,GAAA/N,gEAAA,CAAAiO,oFAAA,GAAAjO,gEAAA,CAAAoO,qFAAA,GAAApO,gEAAA,CAAAsO,oEAAA,GAAAtO,gEAAA,CAAAsO,4EAAA,GAAAtO,gEAAA,CAAAsO,wEAAA,GAAAtO,gEAAA,CAAAwO,+EAAA,GAAAxO,gEAAA,CAAA0O,oDAAA,GAAA1O,gEAAA,CAAAoU,+EAAA,GAAApU,gEAAA,CAAAqU,oFAAA,GAAArU,gEAAA,CAAAuV,iFAAA;IAAA;EAAA;;;YAA5BQ,4BAA4B;MAAAnH,SAAA;MAAAC,SAAA,WAAAoe,mCAAAle,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UC9DvC/O,6DADF,iBAA0B,qBACR;UACdA,wDAAA,qBAAiC;UACnCA,2DAAA,EAAiB;UAEfA,6DADF,mBAAc,YACyB;UAAAA,qDAAA,gRAA6C;UAAAA,2DAAA,EAAK;UAIjFA,6DAHN,aAA8B,aACN,cACqC,gBACT;UAAAA,qDAAA,oBAAE;UAAAA,2DAAA,EAAQ;UACxDA,6DAAA,qBACkD;UADtBA,+DAAA,2BAAAktB,0EAAA1oB,MAAA;YAAAxE,4DAAA,CAAAoP,GAAA;YAAApP,iEAAA,CAAAgP,GAAA,CAAA+J,WAAA,CAAAC,kBAAA,EAAAxU,MAAA,MAAAwK,GAAA,CAAA+J,WAAA,CAAAC,kBAAA,GAAAxU,MAAA;YAAA,OAAAxE,0DAAA,CAAAwE,MAAA;UAAA,EAA4C;UACtExE,yDAAA,4BAAAmtB,2EAAA;YAAAntB,4DAAA,CAAAoP,GAAA;YAAA,OAAApP,0DAAA,CAAkBgP,GAAA,CAAA4W,0BAAA,EAA4B;UAAA,EAAC;UAC/C5lB,yDAAA,KAAAotB,kDAAA,wBAAoE;UAK1EptB,2DAFI,EAAY,EACR,EACF;UAGFA,6DAFJ,cAAsB,eACqC,iBACX;UAAAA,qDAAA,oBAAE;UAAAA,2DAAA,EAAQ;UACtDA,6DAAA,qBAAsE;UAA3DA,+DAAA,2BAAAqtB,0EAAA7oB,MAAA;YAAAxE,4DAAA,CAAAoP,GAAA;YAAApP,iEAAA,CAAAgP,GAAA,CAAA+J,WAAA,CAAAoD,kBAAA,EAAA3X,MAAA,MAAAwK,GAAA,CAAA+J,WAAA,CAAAoD,kBAAA,GAAA3X,MAAA;YAAA,OAAAxE,0DAAA,CAAAwE,MAAA;UAAA,EAA4C;UACrDxE,yDAAA,KAAAstB,kDAAA,wBAAgE;UAKtEttB,2DAFI,EAAY,EACR,EACF;UAIFA,6DAFJ,cAAsB,eAC8B,iBACJ;UAAAA,qDAAA,eAC5C;UAAAA,2DAAA,EAAQ;UAENA,6DADF,yBAA4B,iBACiE;UAAhCA,+DAAA,2BAAAutB,sEAAA/oB,MAAA;YAAAxE,4DAAA,CAAAoP,GAAA;YAAApP,iEAAA,CAAAgP,GAAA,CAAA+J,WAAA,CAAA8K,KAAA,EAAArf,MAAA,MAAAwK,GAAA,CAAA+J,WAAA,CAAA8K,KAAA,GAAArf,MAAA;YAAA,OAAAxE,0DAAA,CAAAwE,MAAA;UAAA,EAA+B;UAC5FxE,2DADE,EAA2F,EAC7E;UAChBA,6DAAA,iBAA0C;UAAAA,qDAAA,UAC1C;UAAAA,2DAAA,EAAQ;UAENA,6DADF,yBAA4B,iBACuD;UAA9BA,+DAAA,2BAAAwtB,sEAAAhpB,MAAA;YAAAxE,4DAAA,CAAAoP,GAAA;YAAApP,iEAAA,CAAAgP,GAAA,CAAA+J,WAAA,CAAA+K,GAAA,EAAAtf,MAAA,MAAAwK,GAAA,CAAA+J,WAAA,CAAA+K,GAAA,GAAAtf,MAAA;YAAA,OAAAxE,0DAAA,CAAAwE,MAAA;UAAA,EAA6B;UAGtFxE,2DAHM,EAAiF,EACnE,EACZ,EACF;UAENA,wDAAA,cAWM;UAIFA,6DAFJ,cAAsB,eACqC,iBACX;UAC1CA,qDAAA,sBACF;UAAAA,2DAAA,EAAQ;UACRA,6DAAA,qBAAuF;UAA3DA,+DAAA,2BAAAytB,0EAAAjpB,MAAA;YAAAxE,4DAAA,CAAAoP,GAAA;YAAApP,iEAAA,CAAAgP,GAAA,CAAA+J,WAAA,CAAAyK,kBAAA,EAAAhf,MAAA,MAAAwK,GAAA,CAAA+J,WAAA,CAAAyK,kBAAA,GAAAhf,MAAA;YAAA,OAAAxE,0DAAA,CAAAwE,MAAA;UAAA,EAA4C;UACtExE,yDAAA,KAAA0tB,kDAAA,wBAAgE;UAKtE1tB,2DAFI,EAAY,EACR,EACF;UAIFA,6DAFJ,cAAsB,eACqC,iBACX;UAC1CA,qDAAA,kCACF;UAAAA,2DAAA,EAAQ;UACRA,6DAAA,qBAAyF;UAA3DA,+DAAA,2BAAA2tB,0EAAAnpB,MAAA;YAAAxE,4DAAA,CAAAoP,GAAA;YAAApP,iEAAA,CAAAgP,GAAA,CAAA+J,WAAA,CAAA2B,kBAAA,EAAAlW,MAAA,MAAAwK,GAAA,CAAA+J,WAAA,CAAA2B,kBAAA,GAAAlW,MAAA;YAAA,OAAAxE,0DAAA,CAAAwE,MAAA;UAAA,EAA4C;UACxExE,yDAAA,KAAA4tB,kDAAA,wBAAgE;UAKtE5tB,2DAFI,EAAY,EACR,EACF;UAIFA,6DAFJ,cAAsB,eACqC,iBACZ;UACzCA,qDAAA,sBACF;UAAAA,2DAAA,EAAQ;UACRA,6DAAA,qBAAsF;UAA1DA,+DAAA,2BAAA6tB,0EAAArpB,MAAA;YAAAxE,4DAAA,CAAAoP,GAAA;YAAApP,iEAAA,CAAAgP,GAAA,CAAA+J,WAAA,CAAAkC,iBAAA,EAAAzW,MAAA,MAAAwK,GAAA,CAAA+J,WAAA,CAAAkC,iBAAA,GAAAzW,MAAA;YAAA,OAAAxE,0DAAA,CAAAwE,MAAA;UAAA,EAA2C;UACrExE,yDAAA,KAAA8tB,kDAAA,wBAA+D;UAKrE9tB,2DAFI,EAAY,EACR,EACF;UAGFA,6DAFJ,cAAsB,eACqC,iBACd;UACvCA,qDAAA,sBACF;UAAAA,2DAAA,EAAQ;UACRA,6DAAA,qBAAqF;UAAzDA,+DAAA,2BAAA+tB,0EAAAvpB,MAAA;YAAAxE,4DAAA,CAAAoP,GAAA;YAAApP,iEAAA,CAAAgP,GAAA,CAAA+J,WAAA,CAAA6K,gBAAA,EAAApf,MAAA,MAAAwK,GAAA,CAAA+J,WAAA,CAAA6K,gBAAA,GAAApf,MAAA;YAAA,OAAAxE,0DAAA,CAAAwE,MAAA;UAAA,EAA0C;UACpExE,yDAAA,KAAAguB,kDAAA,wBAAgE;UAKtEhuB,2DAFI,EAAY,EACR,EACF;UAIFA,6DAFJ,cAAsB,eACqC,iBACV;UAC3CA,qDAAA,kCACF;UAAAA,2DAAA,EAAQ;UACRA,6DAAA,qBAA0F;UAA5DA,+DAAA,2BAAAiuB,0EAAAzpB,MAAA;YAAAxE,4DAAA,CAAAoP,GAAA;YAAApP,iEAAA,CAAAgP,GAAA,CAAA+J,WAAA,CAAA2K,mBAAA,EAAAlf,MAAA,MAAAwK,GAAA,CAAA+J,WAAA,CAAA2K,mBAAA,GAAAlf,MAAA;YAAA,OAAAxE,0DAAA,CAAAwE,MAAA;UAAA,EAA6C;UACzExE,yDAAA,KAAAkuB,kDAAA,wBAAiE;UAKvEluB,2DAFI,EAAY,EACR,EACF;UAIFA,6DAFJ,cAAsB,eACqC,iBACL;UAChDA,qDAAA,wCACF;UAAAA,2DAAA,EAAQ;UACRA,6DAAA,qBAAgG;UAAjEA,+DAAA,2BAAAmuB,0EAAA3pB,MAAA;YAAAxE,4DAAA,CAAAoP,GAAA;YAAApP,iEAAA,CAAAgP,GAAA,CAAA+J,WAAA,CAAA4K,wBAAA,EAAAnf,MAAA,MAAAwK,GAAA,CAAA+J,WAAA,CAAA4K,wBAAA,GAAAnf,MAAA;YAAA,OAAAxE,0DAAA,CAAAwE,MAAA;UAAA,EAAkD;UAC/ExE,yDAAA,KAAAouB,kDAAA,wBAAsE;UAK5EpuB,2DAFI,EAAY,EACR,EACF;UAENA,wDAAA,cAEM;UAKFA,6DAFJ,eAAuB,eACoC,kBACO;UAArBA,yDAAA,mBAAAquB,+DAAA;YAAAruB,4DAAA,CAAAoP,GAAA;YAAA,OAAApP,0DAAA,CAASgP,GAAA,CAAAgV,QAAA,EAAU;UAAA,EAAC;UAC3DhkB,qDAAA,sBAAG;UAAAA,wDAAA,aAA6B;UAGtCA,2DAFI,EAAS,EACL,EACF;UAGJA,6DADF,eAAuB,eAC+B;UAClDA,yDAAA,KAAAsuB,+CAAA,qBAAmG;UAGnGtuB,6DAAA,kBAAqF;UAA5CA,yDAAA,mBAAAuuB,+DAAA;YAAAvuB,4DAAA,CAAAoP,GAAA;YAAA,OAAApP,0DAAA,CAASgP,GAAA,CAAA0X,YAAA,CAAa,mBAAmB,CAAC;UAAA,EAAC;UAClF1mB,qDAAA,8CACF;UAAAA,2DAAA,EAAS;UAITA,6DAAA,kBAAiE;UAAxBA,yDAAA,mBAAAwuB,+DAAA;YAAAxuB,4DAAA,CAAAoP,GAAA;YAAA,OAAApP,0DAAA,CAASgP,GAAA,CAAAqV,WAAA,EAAa;UAAA,EAAC;UAC9DrkB,qDAAA,oDACF;UAAAA,2DAAA,EAAS;UACTA,6DAAA,oBAA4G;UAAzDA,yDAAA,oBAAAyuB,+DAAAjqB,MAAA;YAAAxE,4DAAA,CAAAoP,GAAA;YAAA,OAAApP,0DAAA,CAAUgP,GAAA,CAAAuC,cAAA,CAAA/M,MAAA,CAAsB;UAAA,EAAC;UAApFxE,2DAAA,EAA4G;UAC5GA,6DAAA,kBAAiE;UAA7BA,yDAAA,mBAAA0uB,+DAAA;YAAA1uB,4DAAA,CAAAoP,GAAA;YAAA,OAAApP,0DAAA,CAASgP,GAAA,CAAAwV,gBAAA,EAAkB;UAAA,EAAC;UAC9DxkB,qDAAA,gEACF;UAGNA,2DAHM,EAAS,EACL,EACF,EACF;UAOEA,6DALR,eAAmC,iBAC+D,aACvF,cACgD,cAErB;UAAAA,qDAAA,oBAAE;UAAAA,2DAAA,EAAK;UACrCA,6DAAA,cAA8B;UAAAA,qDAAA,oBAAE;UAAAA,2DAAA,EAAK;UACrCA,6DAAA,cAA8B;UAAAA,qDAAA,oBAAE;UAAAA,2DAAA,EAAK;UACrCA,6DAAA,cAA8B;UAAAA,qDAAA,oBAAE;UAAAA,2DAAA,EAAK;UACrCA,6DAAA,cAA8B;UAAAA,qDAAA,oBAAE;UAAAA,2DAAA,EAAK;UACrCA,6DAAA,cAA8B;UAAAA,qDAAA,gCAAI;UAAAA,2DAAA,EAAK;UACvCA,6DAAA,cAA8B;UAAAA,qDAAA,iCAAI;UAAAA,2DAAA,EAAK;UACvCA,6DAAA,eAA8B;UAAAA,qDAAA,uCAAK;UAAAA,2DAAA,EAAK;UACxCA,6DAAA,eAA8B;UAAAA,qDAAA,qBAAE;UAAAA,2DAAA,EAAK;UACrCA,6DAAA,eAA8B;UAAAA,qDAAA,qBAAE;UAEpCA,2DAFoC,EAAK,EAClC,EACC;UACRA,6DAAA,cAAO;UACLA,yDAAA,MAAA2uB,4CAAA,mBAAmD;UA8C3D3uB,2DAHM,EAAQ,EACF,EACJ,EACO;UAEbA,6DADF,2BAAsD,2BAES;UAD7CA,+DAAA,wBAAA4uB,6EAAApqB,MAAA;YAAAxE,4DAAA,CAAAoP,GAAA;YAAApP,iEAAA,CAAAgP,GAAA,CAAAnI,SAAA,EAAArC,MAAA,MAAAwK,GAAA,CAAAnI,SAAA,GAAArC,MAAA;YAAA,OAAAxE,0DAAA,CAAAwE,MAAA;UAAA,EAAoB;UAClCxE,yDAAA,wBAAA4uB,6EAAApqB,MAAA;YAAAxE,4DAAA,CAAAoP,GAAA;YAAA,OAAApP,0DAAA,CAAcgP,GAAA,CAAApF,WAAA,CAAApF,MAAA,CAAmB;UAAA,EAAC;UAGxCxE,2DAFI,EAAiB,EACF,EACT;UAuKVA,yDArKA,MAAA6uB,qDAAA,gCAAA7uB,qEAAA,CAAmE,MAAA8uB,qDAAA,iCAAA9uB,qEAAA,CAsIF,MAAA+uB,qDAAA,kCAAA/uB,qEAAA,CA+BJ;;;UA5YvBA,wDAAA,IAA4C;UAA5CA,+DAAA,YAAAgP,GAAA,CAAA+J,WAAA,CAAAC,kBAAA,CAA4C;UAE1ChZ,wDAAA,EAAuB;UAAvBA,yDAAA,YAAAgP,GAAA,CAAA6N,oBAAA,CAAuB;UAS1C7c,wDAAA,GAA4C;UAA5CA,+DAAA,YAAAgP,GAAA,CAAA+J,WAAA,CAAAoD,kBAAA,CAA4C;UACzBnc,wDAAA,EAAmB;UAAnBA,yDAAA,YAAAgP,GAAA,CAAA8N,gBAAA,CAAmB;UAYY9c,wDAAA,GAA+B;UAA/BA,+DAAA,YAAAgP,GAAA,CAAA+J,WAAA,CAAA8K,KAAA,CAA+B;UAKvC7jB,wDAAA,GAA6B;UAA7BA,+DAAA,YAAAgP,GAAA,CAAA+J,WAAA,CAAA+K,GAAA,CAA6B;UAuBtD9jB,wDAAA,GAA4C;UAA5CA,+DAAA,YAAAgP,GAAA,CAAA+J,WAAA,CAAAyK,kBAAA,CAA4C;UAC1CxjB,wDAAA,EAAmB;UAAnBA,yDAAA,YAAAgP,GAAA,CAAAqT,gBAAA,CAAmB;UAYnBriB,wDAAA,GAA4C;UAA5CA,+DAAA,YAAAgP,GAAA,CAAA+J,WAAA,CAAA2B,kBAAA,CAA4C;UAC5C1a,wDAAA,EAAmB;UAAnBA,yDAAA,YAAAgP,GAAA,CAAA6L,gBAAA,CAAmB;UAYrB7a,wDAAA,GAA2C;UAA3CA,+DAAA,YAAAgP,GAAA,CAAA+J,WAAA,CAAAkC,iBAAA,CAA2C;UACzCjb,wDAAA,EAAkB;UAAlBA,yDAAA,YAAAgP,GAAA,CAAAmM,eAAA,CAAkB;UAWpBnb,wDAAA,GAA0C;UAA1CA,+DAAA,YAAAgP,GAAA,CAAA+J,WAAA,CAAA6K,gBAAA,CAA0C;UACxC5jB,wDAAA,EAAmB;UAAnBA,yDAAA,YAAAgP,GAAA,CAAAmT,gBAAA,CAAmB;UAYnBniB,wDAAA,GAA6C;UAA7CA,+DAAA,YAAAgP,GAAA,CAAA+J,WAAA,CAAA2K,mBAAA,CAA6C;UAC7C1jB,wDAAA,EAAoB;UAApBA,yDAAA,YAAAgP,GAAA,CAAAsT,iBAAA,CAAoB;UAYnBtiB,wDAAA,GAAkD;UAAlDA,+DAAA,YAAAgP,GAAA,CAAA+J,WAAA,CAAA4K,wBAAA,CAAkD;UACnD3jB,wDAAA,EAAyB;UAAzBA,yDAAA,YAAAgP,GAAA,CAAAuT,sBAAA,CAAyB;UAsBbviB,wDAAA,GAAc;UAAdA,yDAAA,SAAAgP,GAAA,CAAAS,QAAA,CAAc;UAsCnCzP,wDAAA,IAAe;UAAfA,yDAAA,YAAAgP,GAAA,CAAA2W,SAAA,CAAe;UAgD1B3lB,wDAAA,GAAoB;UAApBA,+DAAA,SAAAgP,GAAA,CAAAnI,SAAA,CAAoB;UAAuB7G,yDAAtB,aAAAgP,GAAA,CAAApI,QAAA,CAAqB,mBAAAoI,GAAA,CAAAlI,YAAA,CAAgC;;;qBD/KlF+P,0DAAY,EAAAlB,qDAAA,EAAAA,kDAAA,EAAEiB,mEAAY,EAAAqY,iEAAA,EAAAA,gEAAA,EAAAA,4DAAA,EAAAA,+DAAA,EAAAA,yDAAA,EAAAA,yDAAA,EAAAA,oDAAA,EAAAlhB,4DAAA,EAAAA,gEAAA,EAAAA,kEAAA,EAAAA,kEAAA,EAAAA,gEAAA,EAAAA,6DAAA,EAAAA,8DAAA,EAAAA,8DAAA,EAAAA,iEAAA,EAAAA,8DAAA,EAAAA,4DAAA,EAAAA,kEAAA,EAAAA,kEAAA,EAAA+hB,sEAAA,EAAAE,6FAAA,EAAAE,kFAAA,EAAEnZ,+DAAkB,EAAEG,mEAAmB;MAAArB,MAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AE3DhC;AACW;AACZ;AACa;AACkD;AAClB;AACF;AACE;AACM;AAC/C;AAC6B;AAC8B;AACvB;;;AAqBhF,MAAOya,yBAAyB;;;uCAAzBA,yBAAyB;IAAA;EAAA;;;YAAzBA;IAAyB;EAAA;;;gBATlCzZ,0DAAY,EACZL,wFAAsB,EACtBI,mEAAY,EACZwZ,mEAAW,EACX3f,6DAAc,EACd4f,yDAAY,CAACE,OAAO,EAAE;IAAA;EAAA;;;uHAIbD,yBAAyB;IAAAE,YAAA,GAjBlC3qB,sHAA8B,EAC9BmQ,sHAA8B,EAC9BC,oGAAwB,EACxBC,kGAAwB,EACxBC,oGAAwB,EACxBC,0GAA0B;IAAAM,OAAA,GAG1BG,0DAAY,EACZL,wFAAsB,EACtBI,mEAAY,EACZwZ,mEAAW,EACX3f,6DAAc,EAAA9C,yDAAA,EAEdpO,0FAAuB;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;AC3ByC;AAMS;;;;;;;;;;;;;ICEjES,4DAAA,oBAA+E;IAC7EA,oDAAA,GACF;IAAAA,0DAAA,EAAY;;;;IAFgDA,wDAAA,UAAAywB,WAAA,CAAkB;IAC5EzwB,uDAAA,EACF;IADEA,gEAAA,MAAAywB,WAAA,CAAA/pB,KAAA,MACF;;;;;;IAkDI1G,4DAFJ,SAAyD,cACpC,sBAE0B;IADfA,wDAAA,2BAAA0wB,wFAAAlsB,MAAA;MAAA,MAAAmsB,MAAA,GAAA3wB,2DAAA,CAAAK,GAAA,EAAAwC,KAAA;MAAA,MAAA+tB,MAAA,GAAA5wB,2DAAA;MAAA,OAAAA,yDAAA,CAAiB4wB,MAAA,CAAAC,gBAAA,CAAArsB,MAAA,EAAAmsB,MAAA,CAA6B;IAAA,EAAC;IAEzE3wB,4DAAA,eAA0B;IAAAA,oDAAA,4CAAO;IAGvCA,0DAHuC,EAAO,EAC5B,EACV,EACH;;;;;IAJCA,uDAAA,GAAwC;IAAxCA,wDAAA,YAAA4wB,MAAA,CAAAE,uBAAA,CAAAH,MAAA,EAAwC;;;;;IALhD3wB,4DAAA,SAA6B;IAC3BA,uDAAA,SAAS;IACTA,wDAAA,IAAA+wB,kDAAA,iBAAyD;IAQ3D/wB,0DAAA,EAAK;;;;IARmBA,uDAAA,GAAiB;IAAjBA,wDAAA,YAAA4wB,MAAA,CAAAjL,SAAA,IAAiB;;;;;;IAuBnC3lB,4DAFJ,SAA8B,cACT,YACI;IAAAA,oDAAA,GAAqD;IAAAA,0DAAA,EAAI;IAC9EA,4DAAA,sBAA4D;IAAhCA,8DAAA,2BAAAgxB,wFAAAxsB,MAAA;MAAA,MAAAysB,QAAA,GAAAjxB,2DAAA,CAAAyE,GAAA,EAAAxD,SAAA;MAAAjB,gEAAA,CAAAixB,QAAA,CAAAC,WAAA,EAAA1sB,MAAA,MAAAysB,QAAA,CAAAC,WAAA,GAAA1sB,MAAA;MAAA,OAAAxE,yDAAA,CAAAwE,MAAA;IAAA,EAA+B;IACzDxE,4DAAA,eAA0B;IAAAA,oDAAA,+BAAI;IAGpCA,0DAHoC,EAAO,EACzB,EACV,EACH;;;;IALoBA,uDAAA,GAAqD;IAArDA,gEAAA,KAAAixB,QAAA,CAAAvX,UAAA,mBAAAuX,QAAA,CAAAvrB,MAAA,KAAqD;IAC9C1F,uDAAA,EAA+B;IAA/BA,8DAAA,YAAAixB,QAAA,CAAAC,WAAA,CAA+B;;;;;;IAV3DlxB,4DAHN,SAAkC,SAC5B,cACiB,QACd;IAAAA,oDAAA,aAAM;IAAAA,0DAAA,EAAI;IACbA,4DAAA,sBAC6C;IAA3CA,wDAAA,2BAAAmxB,mFAAA3sB,MAAA;MAAA,MAAA4sB,MAAA,GAAApxB,2DAAA,CAAAe,GAAA,EAAAE,SAAA;MAAA,MAAA2vB,MAAA,GAAA5wB,2DAAA;MAAA,OAAAA,yDAAA,CAAiB4wB,MAAA,CAAAS,YAAA,CAAA7sB,MAAA,EAAA4sB,MAAA,CAAwB;IAAA,EAAC;IAC1CpxB,4DAAA,eAA0B;IAAAA,oDAAA,2CAAM;IAGtCA,0DAHsC,EAAO,EAC3B,EACV,EACH;IACLA,wDAAA,IAAAsxB,kDAAA,iBAA8B;IAQhCtxB,0DAAA,EAAK;;;;;IAd6BA,uDAAA,GAAqC;IAArCA,wDAAA,YAAA4wB,MAAA,CAAAW,oBAAA,CAAAH,MAAA,EAAqC;IAM/CpxB,uDAAA,GAAM;IAANA,wDAAA,YAAAoxB,MAAA,CAAM;;;;;IAxBhCpxB,4DAFJ,cAAuD,gBACmC,YAC/E;IACLA,wDAAA,IAAAwxB,6CAAA,iBAA6B;IAW/BxxB,0DAAA,EAAQ;IACRA,4DAAA,YAAO;IACLA,wDAAA,IAAAyxB,6CAAA,iBAAkC;IAqBxCzxB,0DAFI,EAAQ,EACF,EACJ;;;;IAlCKA,uDAAA,GAAsB;IAAtBA,wDAAA,SAAA4wB,MAAA,CAAAjL,SAAA,CAAArc,MAAA,CAAsB;IAaPtJ,uDAAA,GAAY;IAAZA,wDAAA,YAAA4wB,MAAA,CAAAjL,SAAA,CAAY;;;ADlCpC,MAAO1P,wBAAyB,SAAQnW,yEAAa;EACzDgG,YACUC,MAAmB,EACnBC,aAA8B,EAC9BG,aAA2B,EAC3BC,KAAqB,EACrBE,QAAkB,EAClBD,OAAuB,EACvBiM,MAAc,EACd/L,aAA2B;IAGnC,KAAK,CAACR,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAG,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAE,QAAQ,GAARA,QAAQ;IACR,KAAAD,OAAO,GAAPA,OAAO;IACP,KAAAiM,MAAM,GAANA,MAAM;IACN,KAAA/L,aAAa,GAAbA,aAAa;IAOvB,KAAA0c,uBAAuB,GAAU,CAAC;MAAExjB,KAAK,EAAE,EAAE;MAAEiH,KAAK,EAAE;IAAI,CAAE,CAAC;IAkD7D,KAAAgrB,WAAW,GAAG,KAAK;EArDnB;EAKAC,eAAeA,CAAA;IACb,IAAI,CAACxrB,aAAa,CAACyrB,gCAAgC,CAAC;MAClD3pB,IAAI,EAAE;QACJgB,YAAY,EAAE,IAAI,CAAC1B;;KAEtB,CAAC,CAACL,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACya,uBAAuB,GAAG,CAAC;UAC9BxjB,KAAK,EAAE,EAAE;UAAEiH,KAAK,EAAE;SACnB,EAAE,GAAG2B,GAAG,CAACE,OAAO,CAAC0c,GAAG,CAACxZ,CAAC,IAAG;UACxB,OAAO;YAAEhM,KAAK,EAAEgM,CAAC;YAAE/E,KAAK,EAAE+E;UAAC,CAAE;QAC/B,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC;EACJ;EAMA1B,KAAKA,CAAA;IACH,IAAI,CAACgP,WAAW,GAAG;MACjB8K,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,GAAG;MACR+N,qBAAqB,EAAE,IAAI,CAAC5O,uBAAuB,CAAC,CAAC;KACtD;EACH;EAEA6O,YAAYA,CAACC,YAA+B;IAC1C,MAAMC,WAAW,GAAwB,EAAE;IAC3C;IACA,MAAMC,YAAY,GAAGlK,KAAK,CAACmK,IAAI,CAAC,IAAIC,GAAG,CACrCJ,YAAY,CAAC9M,GAAG,CAACmN,QAAQ,IAAIA,QAAQ,CAAC1sB,MAAM,CAAC,CAACkH,MAAM,CAACylB,KAAK,IAAIA,KAAK,KAAK,IAAI,CAAa,CAC1F,CAAC;IACF;IACA,KAAK,MAAMA,KAAK,IAAIJ,YAAY,EAAE;MAChCD,WAAW,CAACpmB,IAAI,CAAC,EAAE,CAAC;IACtB;IACA;IACA,KAAK,MAAMwmB,QAAQ,IAAIL,YAAY,EAAE;MACnC,MAAMO,UAAU,GAAGL,YAAY,CAACxnB,OAAO,CAAC2nB,QAAQ,CAAC1sB,MAAgB,CAAC,CAAC,CAAC;MACpE,IAAI4sB,UAAU,KAAK,CAAC,CAAC,EAAE;QACrBN,WAAW,CAACM,UAAU,CAAC,CAAC1mB,IAAI,CAAC;UAAE,GAAGwmB,QAAQ;UAAElB,WAAW,EAAEkB,QAAQ,CAACjY,SAAS,KAAK,KAAK,GAAG,IAAI,GAAG;QAAK,CAAE,CAAC;MACzG,CAAC,CAAC;IACJ;IACA,OAAO6X,WAAW;EACpB;EAIA3M,qBAAqBA,CAACC,GAAsB;IAC1C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAAC/f,MAAM,IAAI,CAAC,KAAK8f,CAAC,CAAC9f,MAAM,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEA0e,YAAYA,CAAA;IACV,IAAI,CAACsN,WAAW,GAAG,KAAK;IACxB,IAAI,IAAI,CAACnqB,WAAW,EAAE;MACpB,IAAI,CAACpB,aAAa,CAACuf,6BAA6B,CAAC;QAC/Czd,IAAI,EAAE;UACJgB,YAAY,EAAE,IAAI,CAAC1B,WAAW;UAC9B2W,aAAa,EAAE,IAAI,CAACnF,WAAW,CAAC8Y,qBAAqB,CAACpyB,KAAK,IAAI,IAAI;UACnEiG,MAAM,EAAE;YACNme,KAAK,EAAE,IAAI,CAAC9K,WAAW,CAAC8K,KAAK;YAC7BC,GAAG,EAAE,IAAI,CAAC/K,WAAW,CAAC+K;WACvB;UACDgC,OAAO,EAAE;;OAEZ,CAAC,CAAC5e,SAAS,CAACmB,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;UACvB,IAAIH,GAAG,CAACE,OAAO,EAAE;YACf,MAAMgqB,IAAI,GAAG,IAAI,CAAClN,qBAAqB,CAAChd,GAAG,CAACE,OAAO,CAAC;YACpD,IAAI,CAACod,SAAS,GAAG,IAAI,CAACmM,YAAY,CAACS,IAAI,CAAC;YACxC,IAAI,CAACb,WAAW,GAAG,IAAI;UACzB;QACF;MACF,CAAC,CAAC;IACJ;EACF;EAGArkB,MAAMA,CAAA;IACJ,IAAI,CAAC9G,aAAa,CAACqF,IAAI,CAAC;MACtB0B,MAAM;MACNC,OAAO,EAAE,IAAI,CAAChG;KACf,CAAC;IACF,IAAI,CAACjB,QAAQ,CAACkH,IAAI,EAAE;EACtB;EAEAgZ,QAAQA,CAAA;IACN,IAAIgM,SAAS,GAAG,IAAI,CAAC7M,SAAS,CAAC8M,IAAI,EAAE,CAACxN,GAAG,CAAE7a,IAAS,IAAI;MACtD,OAAO;QACL+P,SAAS,EAAE,CAAC/P,IAAI,CAAC8mB,WAAW;QAC5BvoB,QAAQ,EAAEyB,IAAI,CAAC8O;OAChB;IACH,CAAC,CAAC;IAEF,IAAI,CAAC/S,aAAa,CAACusB,8BAA8B,CAAC;MAChDzqB,IAAI,EAAE;QACJ0qB,IAAI,EAAE,CAAC;QACPC,IAAI,EAAEJ;;KAET,CAAC,CAACtrB,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACnC,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;QAClCqC,OAAO,CAACC,GAAG,CAAC3D,GAAG,CAAC;QAChB,IAAI,CAAC+b,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EACJ;EAGAmN,oBAAoBA,CAACsB,GAAU;IAC7B,OAAOA,GAAG,CAACC,KAAK,CAAE1oB,IAA2B,IAAKA,IAAI,CAAC8mB,WAAW,CAAC;EACrE;EAEAJ,uBAAuBA,CAACjuB,KAAa;IACnC,IAAI,IAAI,CAAC6uB,WAAW,EAAE;MAEpB,IAAI7uB,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAAC8iB,SAAS,CAAC,CAAC,CAAC,CAACrc,MAAM,EAAE;QAClD,MAAM,IAAIypB,KAAK,CAAC,8DAA8D,CAAC;MACjF;MACA,KAAK,MAAMC,SAAS,IAAI,IAAI,CAACrN,SAAS,EAAE;QACtC,IAAI9iB,KAAK,IAAImwB,SAAS,CAAC1pB,MAAM,IAAI,CAAC0pB,SAAS,CAACnwB,KAAK,CAAC,CAACquB,WAAW,EAAE;UAC9D,OAAO,KAAK,CAAC,CAAC;QAChB;MACF;MACA,OAAO,IAAI,CAAC,CAAC;IACf;IACA,OAAO,KAAK;EACd;EAEAL,gBAAgBA,CAACoC,OAAgB,EAAEpwB,KAAa;IAC9C,IAAIA,KAAK,GAAG,CAAC,EAAE;MACb,MAAM,IAAIkwB,KAAK,CAAC,qDAAqD,CAAC;IACxE;IACA,KAAK,MAAMC,SAAS,IAAI,IAAI,CAACrN,SAAS,EAAE;MACtC,IAAI9iB,KAAK,GAAGmwB,SAAS,CAAC1pB,MAAM,EAAE;QAAE;QAC9B0pB,SAAS,CAACnwB,KAAK,CAAC,CAACquB,WAAW,GAAG+B,OAAO;MACxC;IACF;EACF;EAEA5B,YAAYA,CAAC4B,OAAgB,EAAEJ,GAAU;IACvC,KAAK,MAAMzoB,IAAI,IAAIyoB,GAAG,EAAE;MACtBzoB,IAAI,CAAC8mB,WAAW,GAAG+B,OAAO;IAC5B;EACF;EAKSjsB,QAAQA,CAAA;IACf,IAAI,CAAC+R,WAAW,GAAG;MACjB8Y,qBAAqB,EAAE,IAAI,CAAC5O,uBAAuB,CAAC,CAAC,CAAC;MACtDY,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE;KACN;IACD,IAAI,CAAC1d,KAAK,CAACa,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACG,WAAW,GAAGD,EAAE;QACrB,IAAI,CAACqqB,eAAe,EAAE;QACtB,IAAI,CAACvN,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EAEJ;EAEAxa,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAChD,SAAS,GAAGgD,OAAO;EAC1B;EAGAqpB,MAAMA,CAACpqB,GAAQ;IACb,IAAI,CAAC9C,aAAa,CAAC+B,IAAI,CAACe,GAAG,CAAC;EAC9B;EAEAzD,OAAOA,CAACyD,GAAQ;IACdA,GAAG,CAACa,KAAK,EAAE;EACb;EAEAwpB,gBAAgBA,CAAChoB,IAAS;IACxB,IAAI,CAACmH,MAAM,CAACsU,QAAQ,CAAC,CAAC,+BAA+Bzb,IAAI,EAAE,EAAE,IAAI,CAAC5D,WAAW,CAAC,CAAC;EACjF;;;uCA1MW0O,wBAAwB,EAAAjW,+DAAA,CAAA2N,0EAAA,GAAA3N,+DAAA,CAAA6N,2DAAA,GAAA7N,+DAAA,CAAA+N,mEAAA,GAAA/N,+DAAA,CAAAiO,2DAAA,GAAAjO,+DAAA,CAAAoO,qDAAA,GAAApO,+DAAA,CAAAsO,mFAAA,GAAAtO,+DAAA,CAAAiO,mDAAA,GAAAjO,+DAAA,CAAAwO,+EAAA;IAAA;EAAA;;;YAAxByH,wBAAwB;MAAArH,SAAA;MAAAwkB,QAAA,GAAApzB,wEAAA;MAAAszB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA7H,QAAA,WAAA8H,kCAAA1kB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrCnC/O,4DADF,iBAA0B,qBACR;UACdA,uDAAA,qBAAiC;UACnCA,0DAAA,EAAiB;UACjBA,4DAAA,mBAAc;UACZA,uDAAA,YAA0C;UAIpCA,4DAHN,aAA8B,aACN,aACqC,eACT;UAAAA,oDAAA,mBAAE;UAAAA,0DAAA,EAAQ;UACxDA,4DAAA,oBAA2F;UAA/DA,8DAAA,2BAAA0zB,sEAAAlvB,MAAA;YAAAxE,gEAAA,CAAAgP,GAAA,CAAA+J,WAAA,CAAA8Y,qBAAA,EAAArtB,MAAA,MAAAwK,GAAA,CAAA+J,WAAA,CAAA8Y,qBAAA,GAAArtB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+C;UACzExE,wDAAA,KAAA2zB,8CAAA,uBAA+E;UAMrF3zB,0DAHI,EAAY,EACR,EAEF;UAGFA,4DAFJ,cAAsB,cAC8B,iBACJ;UAAAA,oDAAA,eAC5C;UAAAA,0DAAA,EAAQ;UAENA,4DADF,yBAA4B,iBACoE;UAAhCA,8DAAA,2BAAA4zB,kEAAApvB,MAAA;YAAAxE,gEAAA,CAAAgP,GAAA,CAAA+J,WAAA,CAAA8K,KAAA,EAAArf,MAAA,MAAAwK,GAAA,CAAA+J,WAAA,CAAA8K,KAAA,GAAArf,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+B;UAC/FxE,0DADE,EAA8F,EAChF;UAChBA,4DAAA,iBAA0C;UAAAA,oDAAA,UAC1C;UAAAA,0DAAA,EAAQ;UAENA,4DADF,yBAA4B,iBAC4D;UAA9BA,8DAAA,2BAAA6zB,kEAAArvB,MAAA;YAAAxE,gEAAA,CAAAgP,GAAA,CAAA+J,WAAA,CAAA+K,GAAA,EAAAtf,MAAA,MAAAwK,GAAA,CAAA+J,WAAA,CAAA+K,GAAA,GAAAtf,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAG3FxE,0DAHM,EAAsF,EACxE,EACZ,EACF;UAGFA,4DAFJ,eAAsB,eAC2B,kBACY;UAAlBA,wDAAA,mBAAA8zB,2DAAA;YAAA,OAAS9kB,GAAA,CAAAjF,KAAA,EAAO;UAAA,EAAC;UACtD/J,oDAAA,sBACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAA2D;UAAzBA,wDAAA,mBAAA+zB,2DAAA;YAAA,OAAS/kB,GAAA,CAAAoV,YAAA,EAAc;UAAA,EAAC;UACxDpkB,oDAAA,sBAAG;UAAAA,uDAAA,aAA6B;UAGtCA,0DAFI,EAAS,EACL,EACF;UAGFA,4DAFJ,eAAuB,eAC0B,kBACmC;UAAhDA,wDAAA,mBAAAg0B,2DAAA;YAAA,OAAShlB,GAAA,CAAAmkB,gBAAA,CAAiB,mBAAmB,CAAC;UAAA,EAAC;UAC7EnzB,oDAAA,gDACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAAoF;UAA/CA,wDAAA,mBAAAi0B,2DAAA;YAAA,OAASjlB,GAAA,CAAAmkB,gBAAA,CAAiB,kBAAkB,CAAC;UAAA,EAAC;UACjFnzB,oDAAA,gDACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAAgF;UAAhDA,wDAAA,mBAAAk0B,2DAAA;YAAA,OAASllB,GAAA,CAAAmkB,gBAAA,CAAiB,mBAAmB,CAAC;UAAA,EAAC;UAC7EnzB,oDAAA,0CACF;UAGNA,0DAHM,EAAS,EACL,EACF,EACF;UACNA,wDAAA,KAAAm0B,wCAAA,kBAAuD;UAsCzDn0B,0DAAA,EAAe;UAITA,4DAHN,0BAAsD,eAChC,eACgC,kBACG;UAAnBA,wDAAA,mBAAAo0B,2DAAA;YAAA,OAASplB,GAAA,CAAA3B,MAAA,EAAQ;UAAA,EAAC;UAChDrN,oDAAA,wCACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAA8D;UAAzBA,wDAAA,mBAAAq0B,2DAAA;YAAA,OAASrlB,GAAA,CAAAoV,YAAA,EAAc;UAAA,EAAC;UAC3DpkB,oDAAA,sBACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAAqD;UAArBA,wDAAA,mBAAAs0B,2DAAA;YAAA,OAAStlB,GAAA,CAAAwX,QAAA,EAAU;UAAA,EAAC;UAClDxmB,oDAAA,sBACF;UAIRA,0DAJQ,EAAS,EACL,EACF,EACS,EACT;;;UApG4BA,uDAAA,IAA+C;UAA/CA,8DAAA,YAAAgP,GAAA,CAAA+J,WAAA,CAAA8Y,qBAAA,CAA+C;UACzC7xB,uDAAA,EAA0B;UAA1BA,wDAAA,YAAAgP,GAAA,CAAAiU,uBAAA,CAA0B;UAYIjjB,uDAAA,GAA+B;UAA/BA,8DAAA,YAAAgP,GAAA,CAAA+J,WAAA,CAAA8K,KAAA,CAA+B;UAKrC7jB,uDAAA,GAA6B;UAA7BA,8DAAA,YAAAgP,GAAA,CAAA+J,WAAA,CAAA+K,GAAA,CAA6B;UA4BzD9jB,uDAAA,IAAiB;UAAjBA,wDAAA,SAAAgP,GAAA,CAAA0iB,WAAA,CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrDW;;;;;;;;;;;;ICOxD1xB,4DAAA,oBAA+E;IAC7EA,oDAAA,GACF;IAAAA,0DAAA,EAAY;;;;IAFgDA,wDAAA,UAAAywB,WAAA,CAAkB;IAC5EzwB,uDAAA,EACF;IADEA,gEAAA,MAAAywB,WAAA,CAAA/pB,KAAA,MACF;;;;;;IAkDI1G,4DAFJ,aAAuE,cAClD,sBAE0B;IADfA,wDAAA,2BAAAu0B,wFAAA/vB,MAAA;MAAA,MAAAmsB,MAAA,GAAA3wB,2DAAA,CAAAK,GAAA,EAAAwC,KAAA;MAAA,MAAA+tB,MAAA,GAAA5wB,2DAAA;MAAA,OAAAA,yDAAA,CAAiB4wB,MAAA,CAAAC,gBAAA,CAAArsB,MAAA,EAAAmsB,MAAA,CAA6B;IAAA,EAAC;IAEzE3wB,4DAAA,eAA0B;IAAAA,oDAAA,4CAAO;IAGvCA,0DAHuC,EAAO,EAC5B,EACV,EACH;;;;;IAJCA,uDAAA,GAAwC;IAAxCA,wDAAA,YAAA4wB,MAAA,CAAAE,uBAAA,CAAAH,MAAA,EAAwC;;;;;IALhD3wB,4DAAA,aAAiD;IAC/CA,uDAAA,aAAsB;IACtBA,wDAAA,IAAAw0B,kDAAA,iBAAuE;IAQzEx0B,0DAAA,EAAK;;;;IARmBA,uDAAA,GAAiB;IAAjBA,wDAAA,YAAA4wB,MAAA,CAAAjL,SAAA,IAAiB;;;;;;IAwBnC3lB,4DAAA,sBAAuF;IAAnCA,8DAAA,2BAAAy0B,sGAAAjwB,MAAA;MAAAxE,2DAAA,CAAAyE,GAAA;MAAA,MAAAwsB,QAAA,GAAAjxB,2DAAA,GAAAiB,SAAA;MAAAjB,gEAAA,CAAAixB,QAAA,CAAAyD,cAAA,EAAAlwB,MAAA,MAAAysB,QAAA,CAAAyD,cAAA,GAAAlwB,MAAA;MAAA,OAAAxE,yDAAA,CAAAwE,MAAA;IAAA,EAAkC;IACpFxE,4DAAA,eAA0B;IACxBA,oDAAA,iCACF;IACFA,0DADE,EAAO,EACK;;;;IAJsCA,8DAAA,YAAAixB,QAAA,CAAAyD,cAAA,CAAkC;;;;;IADtF10B,4DAFJ,aAAiF,cAC5D,YACI;IAAAA,oDAAA,GAAqD;IAAAA,0DAAA,EAAI;IAC9EA,wDAAA,IAAA20B,gEAAA,0BAAuF;IAM3F30B,0DADE,EAAM,EACH;;;;IATyBA,wDAAA,aAAAixB,QAAA,CAAA9W,SAAA,uBAAkD;IAEvDna,uDAAA,GAAqD;IAArDA,gEAAA,KAAAixB,QAAA,CAAAvX,UAAA,mBAAAuX,QAAA,CAAAvrB,MAAA,KAAqD;IAC5D1F,uDAAA,EAAqB;IAArBA,wDAAA,SAAAixB,QAAA,CAAA9W,SAAA,CAAqB;;;;;;IAVnCna,4DAHN,aAAsD,aACnC,cACI,QACd;IAAAA,oDAAA,aAAM;IAAAA,0DAAA,EAAI;IACbA,4DAAA,sBAC6C;IAA3CA,wDAAA,2BAAA40B,mFAAApwB,MAAA;MAAA,MAAA4sB,MAAA,GAAApxB,2DAAA,CAAAe,GAAA,EAAAE,SAAA;MAAA,MAAA2vB,MAAA,GAAA5wB,2DAAA;MAAA,OAAAA,yDAAA,CAAiB4wB,MAAA,CAAAS,YAAA,CAAA7sB,MAAA,EAAA4sB,MAAA,CAAwB;IAAA,EAAC;IAC1CpxB,4DAAA,eAA0B;IAAAA,oDAAA,4CAAO;IAGvCA,0DAHuC,EAAO,EAC5B,EACV,EACH;IACLA,wDAAA,IAAA60B,kDAAA,iBAAiF;IAUnF70B,0DAAA,EAAK;;;;;IAhB6BA,uDAAA,GAAqC;IAArCA,wDAAA,YAAA4wB,MAAA,CAAAW,oBAAA,CAAAH,MAAA,EAAqC;IAM/CpxB,uDAAA,GAAM;IAANA,wDAAA,YAAAoxB,MAAA,CAAM;;;;;IAxBhCpxB,4DAFJ,cAAuD,gBACoC,YAChF;IACLA,wDAAA,IAAA80B,6CAAA,iBAAiD;IAWnD90B,0DAAA,EAAQ;IACRA,4DAAA,YAAO;IACLA,wDAAA,IAAA+0B,6CAAA,iBAAsD;IAuB5D/0B,0DAFI,EAAQ,EACF,EACJ;;;;IApCKA,uDAAA,GAAsB;IAAtBA,wDAAA,SAAA4wB,MAAA,CAAAjL,SAAA,CAAArc,MAAA,CAAsB;IAaPtJ,uDAAA,GAAY;IAAZA,wDAAA,YAAA4wB,MAAA,CAAAjL,SAAA,CAAY;;;ADjCpC,MAAOxP,wBAAyB,SAAQrW,yEAAa;EACzDgG,YACUC,MAAmB,EACnBC,aAA8B,EAC9BG,aAA2B,EAC3BC,KAAqB,EACrBE,QAAkB,EAClBD,OAAuB,EACvBiM,MAAc;IAGtB,KAAK,CAACvM,MAAM,CAAC;IATL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAG,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAE,QAAQ,GAARA,QAAQ;IACR,KAAAD,OAAO,GAAPA,OAAO;IACP,KAAAiM,MAAM,GAANA,MAAM;IAOhB,KAAA2Q,uBAAuB,GAAU,CAAC;MAAExjB,KAAK,EAAE,EAAE;MAAEiH,KAAK,EAAE;IAAI,CAAE,CAAC;IAyC7D,KAAAgrB,WAAW,GAAG,KAAK;EA5CnB;EAKAC,eAAeA,CAAA;IACb,IAAI,CAACxrB,aAAa,CAACyrB,gCAAgC,CAAC;MAClD3pB,IAAI,EAAE;QACJgB,YAAY,EAAE,IAAI,CAAC1B;;KAEtB,CAAC,CAACL,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACya,uBAAuB,GAAG,CAAC;UAC9BxjB,KAAK,EAAE,EAAE;UAAEiH,KAAK,EAAE;SACnB,EAAE,GAAG2B,GAAG,CAACE,OAAO,CAAC0c,GAAG,CAACxZ,CAAC,IAAG;UACxB,OAAO;YAAEhM,KAAK,EAAEgM,CAAC;YAAE/E,KAAK,EAAE+E;UAAC,CAAE;QAC/B,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC;EACJ;EAIAqmB,YAAYA,CAACC,YAA+B;IAC1C,MAAMC,WAAW,GAA2B,EAAE;IAC9C,MAAMC,YAAY,GAAGlK,KAAK,CAACmK,IAAI,CAAC,IAAIC,GAAG,CACrCJ,YAAY,CAAC9M,GAAG,CAACmN,QAAQ,IAAIA,QAAQ,CAAC1sB,MAAM,CAAC,CAACkH,MAAM,CAACylB,KAAK,IAAIA,KAAK,KAAK,IAAI,CAAa,CAC1F,CAAC;IACF,KAAK,MAAMA,KAAK,IAAIJ,YAAY,EAAE;MAChCD,WAAW,CAACpmB,IAAI,CAAC,EAAE,CAAC;IACtB;IACA,KAAK,MAAMwmB,QAAQ,IAAIL,YAAY,EAAE;MACnC,MAAMO,UAAU,GAAGL,YAAY,CAACxnB,OAAO,CAAC2nB,QAAQ,CAAC1sB,MAAgB,CAAC,CAAC,CAAC;MACpE,IAAI4sB,UAAU,KAAK,CAAC,CAAC,EAAE;QACrB,IAAI0C,OAAO,GAAuB;UAAE,GAAG5C;QAAQ,CAAE;QACjD,IAAIA,QAAQ,CAACjY,SAAS,EAAE;UACtB6a,OAAO,GAAG;YAAE,GAAGA,OAAO;YAAEN,cAAc,EAAGtC,QAAQ,CAACzY,UAAU,IAAIyY,QAAQ,CAACzY,UAAU,IAAI,CAAC,GAAI,IAAI,GAAG;UAAK,CAAE;QAC5G;QACAqY,WAAW,CAACM,UAAU,CAAC,CAAC1mB,IAAI,CAACopB,OAAO,CAAC;MACvC;IACF;IACA,OAAOhD,WAAW;EACpB;EAIA3M,qBAAqBA,CAACC,GAAsB;IAC1C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAAC/f,MAAM,IAAI,CAAC,KAAK8f,CAAC,CAAC9f,MAAM,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEA0e,YAAYA,CAAA;IACV,IAAI,CAACsN,WAAW,GAAG,KAAK;IACxB,IAAI,IAAI,CAACnqB,WAAW,EAAE;MACpB,IAAI,CAACpB,aAAa,CAACuf,6BAA6B,CAAC;QAC/Czd,IAAI,EAAE;UACJgB,YAAY,EAAE,IAAI,CAAC1B,WAAW;UAC9B2W,aAAa,EAAE,IAAI,CAACnF,WAAW,CAAC8Y,qBAAqB,CAACpyB,KAAK,IAAI,IAAI;UACnEiG,MAAM,EAAE;YACNme,KAAK,EAAE,IAAI,CAAC9K,WAAW,CAAC8K,KAAK;YAC7BC,GAAG,EAAE,IAAI,CAAC/K,WAAW,CAAC+K;WACvB;UACDgC,OAAO,EAAE;;OAEZ,CAAC,CAAC5e,SAAS,CAACmB,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;UACtC,MAAM+pB,IAAI,GAAG,IAAI,CAAClN,qBAAqB,CAAChd,GAAG,CAACE,OAAO,CAAC;UACpD,IAAI,CAACod,SAAS,GAAG,IAAI,CAACmM,YAAY,CAACS,IAAI,CAAC;UACxC,IAAI,CAACb,WAAW,GAAG,IAAI;UACvB3lB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC2Z,SAAS,CAAC;QAC/C;MACF,CAAC,CAAC;IACJ;EACF;EAGAtY,MAAMA,CAAA;IAAK,IAAI,CAAC/G,QAAQ,CAACkH,IAAI,EAAE;EAAC;EAEhCgZ,QAAQA,CAAA;IACN,IAAIgM,SAAS,GAAG,IAAI,CAAC7M,SAAS,CAAC8M,IAAI,EAAE,CAACxN,GAAG,CAAE7a,IAAS,IAAI;MACtD,OAAO;QACLuP,UAAU,EAAEvP,IAAI,CAACsqB,cAAc,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;QAChD/rB,QAAQ,EAAEyB,IAAI,CAAC8O,GAAG;QAClBiB,SAAS,EAAE/P,IAAI,CAAC+P;OACjB;IACH,CAAC,CAAC;IAEF,IAAI,CAAChU,aAAa,CAACusB,8BAA8B,CAAC;MAChDzqB,IAAI,EAAE;QACJ0qB,IAAI,EAAE,CAAC;QACPC,IAAI,EAAEJ,SAAS,CAAC5lB,MAAM,CAAEnB,CAAM,IAAKA,CAAC,CAAC0O,SAAS;;KAEjD,CAAC,CAACjT,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACnC,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAAC0a,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EACJ;EAEAmN,oBAAoBA,CAACsB,GAAU;IAC7B,IAAIoC,KAAK,GAAG,CAAC;IACb,KAAK,IAAIjqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6nB,GAAG,CAACvpB,MAAM,EAAE0B,CAAC,EAAE,EAAE;MACnC,MAAMZ,IAAI,GAAGyoB,GAAG,CAAC7nB,CAAC,CAAC;MACnB,IAAI,CAACZ,IAAI,CAACsqB,cAAc,IAAItqB,IAAI,CAAC+P,SAAS,EAAE;QAC1C,OAAO,KAAK;MACd;MACA,IAAI,CAAC/P,IAAI,CAAC+P,SAAS,EAAE;QACnB8a,KAAK,GAAGA,KAAK,GAAG,CAAC;MACnB;IACF;IACA,IAAIA,KAAK,KAAKpC,GAAG,CAACvpB,MAAM,EAAE;MACxB,OAAO,KAAK,EAAC;IACf;IACA,OAAO,IAAI;EACb;EAEAwnB,uBAAuBA,CAACjuB,KAAa;IACnC,IAAI,IAAI,CAAC6uB,WAAW,EAAE;MACpB,IAAI7uB,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAAC8iB,SAAS,CAAC,CAAC,CAAC,CAACrc,MAAM,EAAE;QAClD,MAAM,IAAIypB,KAAK,CAAC,8DAA8D,CAAC;MACjF;MACA,IAAIkC,KAAK,GAAG,CAAC;MAEb,KAAK,MAAMjC,SAAS,IAAI,IAAI,CAACrN,SAAS,EAAE;QACtC,IAAIqN,SAAS,CAACnwB,KAAK,CAAC,CAACsX,SAAS,EAAE;UAE9B,IAAItX,KAAK,IAAImwB,SAAS,CAAC1pB,MAAM,IAAI,CAAC0pB,SAAS,CAACnwB,KAAK,CAAC,CAAC6xB,cAAc,EAAE;YACjE,OAAO,KAAK,CAAC,CAAC;UAChB;QACF,CAAC,MAAM;UACLO,KAAK,GAAGA,KAAK,GAAG,CAAC;QACnB;QACA,IAAIA,KAAK,KAAK,IAAI,CAACtP,SAAS,CAACrc,MAAM,EAAE;UACnC,OAAO,KAAK,EAAC;QACf;MACF;MACA,OAAO,IAAI,CAAC,CAAC;IACf;IACA,OAAO,KAAK;EACd;EAEAunB,gBAAgBA,CAACoC,OAAgB,EAAEpwB,KAAa;IAC9C,IAAIA,KAAK,GAAG,CAAC,EAAE;MACb,MAAM,IAAIkwB,KAAK,CAAC,qDAAqD,CAAC;IACxE;IACA,KAAK,MAAMC,SAAS,IAAI,IAAI,CAACrN,SAAS,EAAE;MACtC,IAAI9iB,KAAK,GAAGmwB,SAAS,CAAC1pB,MAAM,EAAE;QAAE;QAC9B,IAAI0pB,SAAS,CAACnwB,KAAK,CAAC,CAACsX,SAAS,EAAE;UAC9B6Y,SAAS,CAACnwB,KAAK,CAAC,CAAC6xB,cAAc,GAAGzB,OAAO;QAC3C;MACF;IACF;EACF;EAEA5B,YAAYA,CAAC4B,OAAgB,EAAEJ,GAAU;IACvC,KAAK,MAAMzoB,IAAI,IAAIyoB,GAAG,EAAE;MACtB,IAAIzoB,IAAI,CAAC+P,SAAS,EAAE;QAClB/P,IAAI,CAACsqB,cAAc,GAAGzB,OAAO;MAC/B;IACF;EACF;EAKSjsB,QAAQA,CAAA;IACf,IAAI,CAAC+R,WAAW,GAAG;MACjB8Y,qBAAqB,EAAE,IAAI,CAAC5O,uBAAuB,CAAC,CAAC,CAAC;MACtDY,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE;KACN;IACD,IAAI,CAAC1d,KAAK,CAACa,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACG,WAAW,GAAGD,EAAE;QACrB,IAAI,CAACqqB,eAAe,EAAE;QACtB,IAAI,CAACvN,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EAEJ;EAEAxa,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAChD,SAAS,GAAGgD,OAAO;EAC1B;EAEAE,KAAKA,CAAA;IACH,IAAI,CAACgP,WAAW,GAAG;MACjB8K,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,GAAG;MACR+N,qBAAqB,EAAE,IAAI,CAAC5O,uBAAuB,CAAC,CAAC;KACtD;EACH;EAEAiQ,MAAMA,CAACpqB,GAAQ;IACb,IAAI,CAAC9C,aAAa,CAAC+B,IAAI,CAACe,GAAG,CAAC;EAC9B;EAEAzD,OAAOA,CAACyD,GAAQ;IACdA,GAAG,CAACa,KAAK,EAAE;EACb;EAEAwpB,gBAAgBA,CAAChoB,IAAS;IACxB,IAAI,CAACmH,MAAM,CAACsU,QAAQ,CAAC,CAAC,+BAA+Bzb,IAAI,EAAE,EAAE,IAAI,CAAC5D,WAAW,CAAC,CAAC;EACjF;;;uCAzNW4O,wBAAwB,EAAAnW,+DAAA,CAAA2N,0EAAA,GAAA3N,+DAAA,CAAA6N,2DAAA,GAAA7N,+DAAA,CAAA+N,mEAAA,GAAA/N,+DAAA,CAAAiO,2DAAA,GAAAjO,+DAAA,CAAAoO,qDAAA,GAAApO,+DAAA,CAAAsO,mFAAA,GAAAtO,+DAAA,CAAAiO,mDAAA;IAAA;EAAA;;;YAAxBkI,wBAAwB;MAAAvH,SAAA;MAAAwkB,QAAA,GAAApzB,wEAAA;MAAAszB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA7H,QAAA,WAAAuJ,kCAAAnmB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtCnC/O,4DADF,iBAA0B,qBACR;UACdA,uDAAA,qBAAiC;UACnCA,0DAAA,EAAiB;UACjBA,4DAAA,mBAAc;UACZA,uDAAA,YAA0C;UAIpCA,4DAHN,aAA8B,aACN,aACqC,eACT;UAAAA,oDAAA,mBAAE;UAAAA,0DAAA,EAAQ;UACxDA,4DAAA,oBAA2F;UAA/DA,8DAAA,2BAAAm1B,sEAAA3wB,MAAA;YAAAxE,gEAAA,CAAAgP,GAAA,CAAA+J,WAAA,CAAA8Y,qBAAA,EAAArtB,MAAA,MAAAwK,GAAA,CAAA+J,WAAA,CAAA8Y,qBAAA,GAAArtB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+C;UACzExE,wDAAA,KAAAo1B,8CAAA,uBAA+E;UAMrFp1B,0DAHI,EAAY,EACR,EAEF;UAGFA,4DAFJ,cAAsB,cAC8B,iBACJ;UAAAA,oDAAA,eAC5C;UAAAA,0DAAA,EAAQ;UAENA,4DADF,yBAA4B,iBACoE;UAAhCA,8DAAA,2BAAAq1B,kEAAA7wB,MAAA;YAAAxE,gEAAA,CAAAgP,GAAA,CAAA+J,WAAA,CAAA8K,KAAA,EAAArf,MAAA,MAAAwK,GAAA,CAAA+J,WAAA,CAAA8K,KAAA,GAAArf,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+B;UAC/FxE,0DADE,EAA8F,EAChF;UAChBA,4DAAA,iBAA0C;UAAAA,oDAAA,UAC1C;UAAAA,0DAAA,EAAQ;UAENA,4DADF,yBAA4B,iBAC4D;UAA9BA,8DAAA,2BAAAs1B,kEAAA9wB,MAAA;YAAAxE,gEAAA,CAAAgP,GAAA,CAAA+J,WAAA,CAAA+K,GAAA,EAAAtf,MAAA,MAAAwK,GAAA,CAAA+J,WAAA,CAAA+K,GAAA,GAAAtf,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAG3FxE,0DAHM,EAAsF,EACxE,EACZ,EACF;UAGFA,4DAFJ,eAAsB,eAC2B,kBACY;UAAlBA,wDAAA,mBAAAu1B,2DAAA;YAAA,OAASvmB,GAAA,CAAAjF,KAAA,EAAO;UAAA,EAAC;UACtD/J,oDAAA,sBACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAA2D;UAAzBA,wDAAA,mBAAAw1B,2DAAA;YAAA,OAASxmB,GAAA,CAAAoV,YAAA,EAAc;UAAA,EAAC;UACxDpkB,oDAAA,sBAAG;UAAAA,uDAAA,aAA6B;UAGtCA,0DAFI,EAAS,EACL,EACF;UAGFA,4DAFJ,eAAuB,eAC0B,kBACmC;UAAhDA,wDAAA,mBAAAy1B,2DAAA;YAAA,OAASzmB,GAAA,CAAAmkB,gBAAA,CAAiB,mBAAmB,CAAC;UAAA,EAAC;UAC7EnzB,oDAAA,gDACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAAoF;UAA/CA,wDAAA,mBAAA01B,2DAAA;YAAA,OAAS1mB,GAAA,CAAAmkB,gBAAA,CAAiB,kBAAkB,CAAC;UAAA,EAAC;UACjFnzB,oDAAA,gDACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAAgF;UAAhDA,wDAAA,mBAAA21B,2DAAA;YAAA,OAAS3mB,GAAA,CAAAmkB,gBAAA,CAAiB,mBAAmB,CAAC;UAAA,EAAC;UAC7EnzB,oDAAA,0CACF;UAGNA,0DAHM,EAAS,EACL,EACF,EACF;UACNA,wDAAA,KAAA41B,wCAAA,kBAAuD;UAwCzD51B,0DAAA,EAAe;UAITA,4DAHN,0BAAsD,eAChC,eACgC,kBACG;UAAnBA,wDAAA,mBAAA61B,2DAAA;YAAA,OAAS7mB,GAAA,CAAA3B,MAAA,EAAQ;UAAA,EAAC;UAChDrN,oDAAA,wCACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAA8D;UAAzBA,wDAAA,mBAAA81B,2DAAA;YAAA,OAAS9mB,GAAA,CAAAoV,YAAA,EAAc;UAAA,EAAC;UAC3DpkB,oDAAA,sBACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAAqD;UAArBA,wDAAA,mBAAA+1B,2DAAA;YAAA,OAAS/mB,GAAA,CAAAwX,QAAA,EAAU;UAAA,EAAC;UAClDxmB,oDAAA,sBACF;UAIRA,0DAJQ,EAAS,EACL,EACF,EACS,EACT;;;UAtG4BA,uDAAA,IAA+C;UAA/CA,8DAAA,YAAAgP,GAAA,CAAA+J,WAAA,CAAA8Y,qBAAA,CAA+C;UACzC7xB,uDAAA,EAA0B;UAA1BA,wDAAA,YAAAgP,GAAA,CAAAiU,uBAAA,CAA0B;UAYIjjB,uDAAA,GAA+B;UAA/BA,8DAAA,YAAAgP,GAAA,CAAA+J,WAAA,CAAA8K,KAAA,CAA+B;UAKrC7jB,uDAAA,GAA6B;UAA7BA,8DAAA,YAAAgP,GAAA,CAAA+J,WAAA,CAAA+K,GAAA,CAA6B;UA4BzD9jB,uDAAA,IAAiB;UAAjBA,wDAAA,SAAAgP,GAAA,CAAA0iB,WAAA,CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpDW;;;;;;;;;;;;;;;ICOxD1xB,4DAAA,oBAA+E;IAC7EA,oDAAA,GACF;IAAAA,0DAAA,EAAY;;;;IAFgDA,wDAAA,UAAAywB,WAAA,CAAkB;IAC5EzwB,uDAAA,EACF;IADEA,gEAAA,MAAAywB,WAAA,CAAA/pB,KAAA,MACF;;;;;;IAkDI1G,4DADF,cAAqE,gBACsB;IAA/BA,8DAAA,2BAAAg2B,wFAAAxxB,MAAA;MAAAxE,2DAAA,CAAAK,GAAA;MAAA,MAAA41B,QAAA,GAAAj2B,2DAAA,GAAAiB,SAAA;MAAAjB,gEAAA,CAAAi2B,QAAA,CAAAvc,UAAA,EAAAlV,MAAA,MAAAyxB,QAAA,CAAAvc,UAAA,GAAAlV,MAAA;MAAA,OAAAxE,yDAAA,CAAAwE,MAAA;IAAA,EAA8B;IAAxFxE,0DAAA,EAAyF;IAAAA,uDAAA,SAAI;IAC7FA,4DAAA,eAC6B;IAA3BA,wDAAA,mBAAAk2B,+EAAA;MAAAl2B,2DAAA,CAAAK,GAAA;MAAA,MAAA41B,QAAA,GAAAj2B,2DAAA,GAAAiB,SAAA;MAAA,MAAA2vB,MAAA,GAAA5wB,2DAAA;MAAA,OAAAA,yDAAA,CAAS4wB,MAAA,CAAAuF,SAAA,CAAAF,QAAA,CAAgB;IAAA,EAAC;IAC1Bj2B,oDAAA,qBACF;IAAAA,0DAAA,EAAO;IACPA,4DAAA,eAEuC;IAArCA,wDAAA,mBAAAo2B,+EAAA;MAAAp2B,2DAAA,CAAAK,GAAA;MAAA,MAAAwQ,MAAA,GAAA7Q,2DAAA;MAAA,MAAAi2B,QAAA,GAAAplB,MAAA,CAAA5P,SAAA;MAAA,MAAAo1B,MAAA,GAAAxlB,MAAA,CAAAhO,KAAA;MAAA,MAAA+tB,MAAA,GAAA5wB,2DAAA;MAAA,OAAAA,yDAAA,CAAS4wB,MAAA,CAAA0F,cAAA,CAAAL,QAAA,EAAAI,MAAA,CAA0B;IAAA,EAAC;IACpCr2B,oDAAA,iCACF;IACFA,0DADE,EAAO,EACH;;;;IAVsDA,uDAAA,EAA8B;IAA9BA,8DAAA,YAAAi2B,QAAA,CAAAvc,UAAA,CAA8B;IAMtF1Z,uDAAA,GAAsE;IAAtEA,wDAAA,YAAAA,6DAAA,IAAAw2B,GAAA,EAAAP,QAAA,CAAAvc,UAAA,SAAsE;;;;;;IAMxE1Z,4DADF,cAA2D,eAEhB;IAAvCA,wDAAA,mBAAAy2B,+EAAA;MAAAz2B,2DAAA,CAAAyE,GAAA;MAAA,MAAAwxB,QAAA,GAAAj2B,2DAAA,GAAAiB,SAAA;MAAA,OAAAjB,yDAAA,CAAAi2B,QAAA,CAAArwB,MAAA,IAAAqwB,QAAA,CAAArwB,MAAA;IAAA,EAAsC;IAAC5F,oDAAA,4CAAO;IAClDA,0DADkD,EAAO,EACnD;;;;;IAhBRA,4DAAA,SAA0D;IAaxDA,wDAZA,IAAA02B,wDAAA,kBAAqE,IAAAC,wDAAA,kBAYV;IAI7D32B,0DAAA,EAAK;;;;IAhBGA,uDAAA,EAA2B;IAA3BA,wDAAA,SAAAi2B,QAAA,CAAArwB,MAAA,UAA2B;IAY3B5F,uDAAA,EAA2B;IAA3BA,wDAAA,SAAAi2B,QAAA,CAAArwB,MAAA,UAA2B;;;;;IAd/B5F,4DADN,aAAiD,SAC3C,eAAwB;IAAAA,oDAAA,mBAAE;IAAOA,0DAAP,EAAO,EAAK;IAC1CA,wDAAA,IAAA42B,kDAAA,iBAA0D;IAkB5D52B,0DAAA,EAAK;;;;IAlBmBA,uDAAA,GAAkB;IAAlBA,wDAAA,YAAA4wB,MAAA,CAAAiG,aAAA,CAAkB;;;;;IAuBtC72B,4DADF,SAAuB,QAClB;IAAAA,oDAAA,GAAiB;IACtBA,0DADsB,EAAI,EACrB;;;;IADAA,uDAAA,GAAiB;IAAjBA,+DAAA,CAAA82B,MAAA,IAAApxB,MAAA,CAAiB;;;;;IAGpB1F,4DAAA,YAA2C;IAAAA,oDAAA,GAAgC;IAAAA,0DAAA,EAAI;;;;IAApCA,uDAAA,EAAgC;IAAhCA,+DAAA,CAAA+2B,QAAA,CAAArd,UAAA,WAAgC;;;;;;IAEzE1Z,4DADF,cAAwF,gBACG;IAA/BA,8DAAA,2BAAAg3B,wFAAAxyB,MAAA;MAAAxE,2DAAA,CAAA4C,IAAA;MAAA,MAAAm0B,QAAA,GAAA/2B,2DAAA,GAAAiB,SAAA;MAAAjB,gEAAA,CAAA+2B,QAAA,CAAArd,UAAA,EAAAlV,MAAA,MAAAuyB,QAAA,CAAArd,UAAA,GAAAlV,MAAA;MAAA,OAAAxE,yDAAA,CAAAwE,MAAA;IAAA,EAA8B;IAAxFxE,0DAAA,EAAyF;IAAAA,uDAAA,SAAI;IAC7FA,4DAAA,eACiC;IAA/BA,wDAAA,mBAAAi3B,+EAAA;MAAAj3B,2DAAA,CAAA4C,IAAA;MAAA,MAAAm0B,QAAA,GAAA/2B,2DAAA,GAAAiB,SAAA;MAAA,MAAA2vB,MAAA,GAAA5wB,2DAAA;MAAA,OAAAA,yDAAA,CAAS4wB,MAAA,CAAAsG,aAAA,CAAAH,QAAA,CAAoB;IAAA,EAAC;IAC9B/2B,oDAAA,qBACF;IAAAA,0DAAA,EAAO;IACPA,4DAAA,eACuG;IAA9BA,wDAAA,mBAAAm3B,+EAAA;MAAAn3B,2DAAA,CAAA4C,IAAA;MAAA,MAAAm0B,QAAA,GAAA/2B,2DAAA,GAAAiB,SAAA;MAAA,MAAA2vB,MAAA,GAAA5wB,2DAAA;MAAA,OAAAA,yDAAA,CAAS4wB,MAAA,CAAAwG,YAAA,CAAAL,QAAA,CAAmB;IAAA,EAAC;IACpG/2B,oDAAA,qBACF;IACFA,0DADE,EAAO,EACH;;;;IATsDA,uDAAA,EAA8B;IAA9BA,8DAAA,YAAA+2B,QAAA,CAAArd,UAAA,CAA8B;IAMtF1Z,uDAAA,GAAsE;IAAtEA,wDAAA,YAAAA,6DAAA,IAAAw2B,GAAA,EAAAO,QAAA,CAAArd,UAAA,SAAsE;;;;;;IAKxE1Z,4DADF,cAAyE,eAEzC;IAA5BA,wDAAA,mBAAAq3B,+EAAA;MAAAr3B,2DAAA,CAAAs3B,IAAA;MAAA,MAAAP,QAAA,GAAA/2B,2DAAA,GAAAiB,SAAA;MAAA,MAAA2vB,MAAA,GAAA5wB,2DAAA;MAAA,OAAAA,yDAAA,CAAS4wB,MAAA,CAAA2G,UAAA,CAAAR,QAAA,CAAiB;IAAA,EAAC;IAAC/2B,oDAAA,gCAAK;IACrCA,0DADqC,EAAO,EACtC;;;;;IAhBRA,4DAAA,aAAiF;IAa/EA,wDAZA,IAAAw3B,sDAAA,gBAA2C,IAAAC,wDAAA,kBAC6C,IAAAC,wDAAA,kBAWf;IAI3E13B,0DAAA,EAAK;;;;IAjByBA,wDAAA,aAAA+2B,QAAA,CAAA5c,SAAA,uBAAkD;IAC1Ena,uDAAA,EAAmB;IAAnBA,wDAAA,UAAA+2B,QAAA,CAAAnxB,MAAA,CAAmB;IACjB5F,uDAAA,EAA8C;IAA9CA,wDAAA,SAAA+2B,QAAA,CAAA5c,SAAA,IAAA4c,QAAA,CAAAnxB,MAAA,UAA8C;IAW9C5F,uDAAA,EAA+C;IAA/CA,wDAAA,SAAA+2B,QAAA,CAAA5c,SAAA,IAAA4c,QAAA,CAAAnxB,MAAA,UAA+C;;;;;IAjBzD5F,4DAAA,aAAsD;IAIpDA,wDAHA,IAAA23B,kDAAA,iBAAuB,IAAAC,kDAAA,iBAG0D;IAkBnF53B,0DAAA,EAAK;;;;IArBEA,uDAAA,EAAgB;IAAhBA,wDAAA,SAAA82B,MAAA,CAAAxtB,MAAA,CAAgB;IAGCtJ,uDAAA,EAAM;IAANA,wDAAA,YAAA82B,MAAA,CAAM;;;;;IA5BhC92B,4DAFJ,cAAuD,gBACmC,YAC/E;IACLA,wDAAA,IAAA63B,6CAAA,iBAAiD;IAqBnD73B,0DAAA,EAAQ;IACRA,4DAAA,YAAO;IACLA,wDAAA,IAAA83B,6CAAA,iBAAsD;IAyB5D93B,0DAFI,EAAQ,EACF,EACJ;;;;IAhDKA,uDAAA,GAAsB;IAAtBA,wDAAA,SAAA4wB,MAAA,CAAAjL,SAAA,CAAArc,MAAA,CAAsB;IAuBPtJ,uDAAA,GAAY;IAAZA,wDAAA,YAAA4wB,MAAA,CAAAjL,SAAA,CAAY;;;ADvDpC,MAAOzP,wBAAyB,SAAQpW,yEAAa;EACzDgG,YACUC,MAAmB,EACnBC,aAA8B,EAC9BG,aAA2B,EAC3BC,KAAqB,EACrBE,QAAkB,EAClBD,OAAuB,EACvBiM,MAAc;IAEtB,KAAK,CAACvM,MAAM,CAAC;IARL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAG,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAE,QAAQ,GAARA,QAAQ;IACR,KAAAD,OAAO,GAAPA,OAAO;IACP,KAAAiM,MAAM,GAANA,MAAM;IAMhB,KAAA2Q,uBAAuB,GAAU,CAAC;MAAExjB,KAAK,EAAE,EAAE;MAAEiH,KAAK,EAAE;IAAI,CAAE,CAAC;IA4C7D,KAAAgrB,WAAW,GAAG,KAAK;EA/CnB;EAKAC,eAAeA,CAAA;IACb,IAAI,CAACxrB,aAAa,CAACyrB,gCAAgC,CAAC;MAClD3pB,IAAI,EAAE;QACJgB,YAAY,EAAE,IAAI,CAAC1B;;KAEtB,CAAC,CAACL,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACya,uBAAuB,GAAG,CAAC;UAC9BxjB,KAAK,EAAE,EAAE;UAAEiH,KAAK,EAAE;SACnB,EAAE,GAAG2B,GAAG,CAACE,OAAO,CAAC0c,GAAG,CAACxZ,CAAC,IAAG;UACxB,OAAO;YAAEhM,KAAK,EAAEgM,CAAC;YAAE/E,KAAK,EAAE+E;UAAC,CAAE;QAC/B,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC;EACJ;EAQAqmB,YAAYA,CAACC,YAA+B;IAC1C,MAAMC,WAAW,GAAwB,EAAE;IAC3C;IACA,MAAMC,YAAY,GAAGlK,KAAK,CAACmK,IAAI,CAAC,IAAIC,GAAG,CACrCJ,YAAY,CAAC9M,GAAG,CAACmN,QAAQ,IAAIA,QAAQ,CAAC1sB,MAAM,CAAC,CAACkH,MAAM,CAACylB,KAAK,IAAIA,KAAK,KAAK,IAAI,CAAa,CAC1F,CAAC;IACF;IACA,KAAK,MAAMA,KAAK,IAAIJ,YAAY,EAAE;MAChCD,WAAW,CAACpmB,IAAI,CAAC,EAAE,CAAC;IACtB;IACA;IACA,KAAK,MAAMwmB,QAAQ,IAAIL,YAAY,EAAE;MACnC,MAAMO,UAAU,GAAGL,YAAY,CAACxnB,OAAO,CAAC2nB,QAAQ,CAAC1sB,MAAgB,CAAC,CAAC,CAAC;MACpE,IAAI4sB,UAAU,KAAK,CAAC,CAAC,EAAE;QACrBN,WAAW,CAACM,UAAU,CAAC,CAAC1mB,IAAI,CAACwmB,QAAQ,CAAC;MACxC,CAAC,CAAC;IACJ;IACA,OAAOJ,WAAW;EACpB;EAIA3M,qBAAqBA,CAACC,GAAsB;IAC1C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAAC/f,MAAM,IAAI,CAAC,KAAK8f,CAAC,CAAC9f,MAAM,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEA0e,YAAYA,CAAA;IACV,IAAI,CAACsN,WAAW,GAAG,KAAK;IACxB,IAAI,IAAI,CAACnqB,WAAW,EAAE;MACpB,IAAI,CAACpB,aAAa,CAACuf,6BAA6B,CAAC;QAC/Czd,IAAI,EAAE;UACJgB,YAAY,EAAE,IAAI,CAAC1B,WAAW;UAC9B2W,aAAa,EAAE,IAAI,CAACnF,WAAW,CAAC8Y,qBAAqB,CAACpyB,KAAK,IAAI,IAAI;UACnEiG,MAAM,EAAE;YACNme,KAAK,EAAE,IAAI,CAAC9K,WAAW,CAAC8K,KAAK;YAC7BC,GAAG,EAAE,IAAI,CAAC/K,WAAW,CAAC+K;WACvB;UACDgC,OAAO,EAAE;;OAEZ,CAAC,CAAC5e,SAAS,CAACmB,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;UACtC,MAAM+pB,IAAI,GAAG,IAAI,CAAClN,qBAAqB,CAAChd,GAAG,CAACE,OAAO,CAAC;UACpD,IAAI,CAACod,SAAS,GAAG,IAAI,CAACmM,YAAY,CAACS,IAAI,CAAC;UACxC,IAAI,CAACsE,aAAa,GAAG,IAAI,CAAClR,SAAS,CAAC,CAAC,CAAC,CAACV,GAAG,CAAE7a,IAAqB,IAAI;YACnE,OAAO;cACLsP,UAAU,EAAE,EAAE;cACd9T,MAAM,EAAE;aACT;UACH,CAAC,CAAC;UACF,IAAI,CAAC+f,SAAS,CAACqG,OAAO,CAAE+L,OAAY,IAAI;YACtC,IAAIA,OAAO,CAAC5d,SAAS,EAAE;cACrB4d,OAAO,CAAC,QAAQ,CAAC,GAAG,KAAK;YAC3B;UACF,CAAC,CAAC;UACF,IAAI,CAACrG,WAAW,GAAG,IAAI;QACzB;MACF,CAAC,CAAC;IACJ;EACF;EAEArkB,MAAMA,CAAA;IAAK,IAAI,CAAC/G,QAAQ,CAACkH,IAAI,EAAE;EAAC;EAEhCgZ,QAAQA,CAAA;IACN,IAAIgM,SAAS,GAAG,IAAI,CAAC7M,SAAS,CAAC8M,IAAI,EAAE,CAACxN,GAAG,CAAE7a,IAAS,IAAI;MACtD,OAAO;QACL+P,SAAS,EAAE/P,IAAI,CAAC+P,SAAS;QACzBxR,QAAQ,EAAEyB,IAAI,CAAC8O,GAAG;QAClBQ,UAAU,EAAEtP,IAAI,CAACsP;OAClB;IACH,CAAC,CAAC;IAEF,IAAI,CAACvT,aAAa,CAACusB,8BAA8B,CAAC;MAChDzqB,IAAI,EAAE;QACJ0qB,IAAI,EAAE,CAAC;QACPC,IAAI,EAAEJ,SAAS,CAAC5lB,MAAM,CAAEnB,CAAM,IAAKA,CAAC,CAAC0O,SAAS;;KAEjD,CAAC,CAACjT,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACnC,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAAC0a,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EACJ;EAGAmT,UAAUA,CAAC/xB,KAAU;IACnBA,KAAK,CAACI,MAAM,GAAG,CAACJ,KAAK,CAACI,MAAM;IAC5B,IAAI,CAACoyB,iBAAiB,GAAGxyB,KAAK,CAACkU,UAAU;EAC3C;EAEAwd,aAAaA,CAAC1xB,KAAU;IACtBA,KAAK,CAACI,MAAM,GAAG,CAACJ,KAAK,CAACI,MAAM;IAC5B,IAAI,IAAI,CAACoyB,iBAAiB,EAAE;MAC1BxyB,KAAK,CAACkU,UAAU,GAAG,IAAI,CAACse,iBAAiB;IAC3C;EACF;EAEAZ,YAAYA,CAAChtB,IAAS;IACpB,IAAI,CAACjE,aAAa,CAACusB,8BAA8B,CAAC;MAChDzqB,IAAI,EAAE;QACJ0qB,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,CAAC;UACLzY,SAAS,EAAE/P,IAAI,CAAC+P,SAAS;UACzBxR,QAAQ,EAAEyB,IAAI,CAAC8O,GAAG;UAClBQ,UAAU,EAAEtP,IAAI,CAACsP;SAClB;;KAEJ,CAAC,CAACxS,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACnC,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAAC0a,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EACJ;EAEA+R,SAASA,CAAC3wB,KAAU;IAClBA,KAAK,CAACI,MAAM,GAAG,CAACJ,KAAK,CAACI,MAAM;IAC5BJ,KAAK,CAACkU,UAAU,GAAG,EAAE;EACvB;EAEA4c,cAAcA,CAAClsB,IAAS,EAAEvH,KAAa;IACrC,IAAIuH,IAAI,CAACsP,UAAU,KAAK,EAAE,EAAE;IAC5B,IAAIue,KAAK,GAAU,EAAE;IACrB,IAAI,CAACtS,SAAS,CAACqG,OAAO,CAAE+L,OAAY,IAAI;MACtC,IAAIA,OAAO,CAACl1B,KAAK,CAAC,CAACsX,SAAS,EAAE;QAC5B8d,KAAK,CAACrsB,IAAI,CAAC;UACT8N,UAAU,EAAEtP,IAAI,CAACsP,UAAU;UAC3B/Q,QAAQ,EAAEovB,OAAO,CAACl1B,KAAK,CAAC,CAACqW,GAAG;UAC5BiB,SAAS,EAAE4d,OAAO,CAACl1B,KAAK,CAAC,CAACsX;SAC3B,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,IAAI,CAAChU,aAAa,CAACusB,8BAA8B,CAAC;MAChDzqB,IAAI,EAAE;QACJ0qB,IAAI,EAAE,CAAC;QACPC,IAAI,EAAEqF;;KAET,CAAC,CAAC/wB,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACnC,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAAC0a,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EACJ;EAISpd,QAAQA,CAAA;IACf,IAAI,CAAC+R,WAAW,GAAG;MACjB8Y,qBAAqB,EAAE,IAAI,CAAC5O,uBAAuB,CAAC,CAAC,CAAC;MACtDY,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE;KACN;IACD,IAAI,CAAC1d,KAAK,CAACa,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACG,WAAW,GAAGD,EAAE;QACrB,IAAI,CAACqqB,eAAe,EAAE;QACtB,IAAI,CAACvN,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EAEJ;EAEAxa,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAChD,SAAS,GAAGgD,OAAO;EAC1B;EAGAqpB,MAAMA,CAACpqB,GAAQ;IACb,IAAI,CAAC9C,aAAa,CAAC+B,IAAI,CAACe,GAAG,CAAC;EAC9B;EAEAzD,OAAOA,CAACyD,GAAQ;IACdA,GAAG,CAACa,KAAK,EAAE;EACb;EAEAI,KAAKA,CAAA;IACH,IAAI,CAACgP,WAAW,GAAG;MACjB8K,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,GAAG;MACR+N,qBAAqB,EAAE,IAAI,CAAC5O,uBAAuB,CAAC,CAAC;KACtD;EACH;EAEAkQ,gBAAgBA,CAAChoB,IAAS;IACxB,IAAI,CAACmH,MAAM,CAACsU,QAAQ,CAAC,CAAC,+BAA+Bzb,IAAI,EAAE,EAAE,IAAI,CAAC5D,WAAW,CAAC,CAAC;EACjF;;;uCAnOW2O,wBAAwB,EAAAlW,+DAAA,CAAA2N,0EAAA,GAAA3N,+DAAA,CAAA6N,2DAAA,GAAA7N,+DAAA,CAAA+N,mEAAA,GAAA/N,+DAAA,CAAAiO,2DAAA,GAAAjO,+DAAA,CAAAoO,qDAAA,GAAApO,+DAAA,CAAAsO,mFAAA,GAAAtO,+DAAA,CAAAiO,mDAAA;IAAA;EAAA;;;YAAxBiI,wBAAwB;MAAAtH,SAAA;MAAAwkB,QAAA,GAAApzB,wEAAA;MAAAszB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA7H,QAAA,WAAAuM,kCAAAnpB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1BnC/O,4DADF,iBAA0B,qBACR;UACdA,uDAAA,qBAAiC;UACnCA,0DAAA,EAAiB;UACjBA,4DAAA,mBAAc;UACZA,uDAAA,YAA0C;UAIpCA,4DAHN,aAA8B,aACN,aACqC,eACT;UAAAA,oDAAA,mBAAE;UAAAA,0DAAA,EAAQ;UACxDA,4DAAA,oBAA2F;UAA/DA,8DAAA,2BAAAm4B,sEAAA3zB,MAAA;YAAAxE,gEAAA,CAAAgP,GAAA,CAAA+J,WAAA,CAAA8Y,qBAAA,EAAArtB,MAAA,MAAAwK,GAAA,CAAA+J,WAAA,CAAA8Y,qBAAA,GAAArtB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+C;UACzExE,wDAAA,KAAAo4B,8CAAA,uBAA+E;UAMrFp4B,0DAHI,EAAY,EACR,EAEF;UAGFA,4DAFJ,cAAsB,cAC8B,iBACJ;UAAAA,oDAAA,eAC5C;UAAAA,0DAAA,EAAQ;UAENA,4DADF,yBAA4B,iBACoE;UAAhCA,8DAAA,2BAAAq4B,kEAAA7zB,MAAA;YAAAxE,gEAAA,CAAAgP,GAAA,CAAA+J,WAAA,CAAA8K,KAAA,EAAArf,MAAA,MAAAwK,GAAA,CAAA+J,WAAA,CAAA8K,KAAA,GAAArf,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+B;UAC/FxE,0DADE,EAA8F,EAChF;UAChBA,4DAAA,iBAA0C;UAAAA,oDAAA,UAC1C;UAAAA,0DAAA,EAAQ;UAENA,4DADF,yBAA4B,iBAC4D;UAA9BA,8DAAA,2BAAAs4B,kEAAA9zB,MAAA;YAAAxE,gEAAA,CAAAgP,GAAA,CAAA+J,WAAA,CAAA+K,GAAA,EAAAtf,MAAA,MAAAwK,GAAA,CAAA+J,WAAA,CAAA+K,GAAA,GAAAtf,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAG3FxE,0DAHM,EAAsF,EACxE,EACZ,EACF;UAGFA,4DAFJ,eAAsB,eAC2B,kBACY;UAAlBA,wDAAA,mBAAAu4B,2DAAA;YAAA,OAASvpB,GAAA,CAAAjF,KAAA,EAAO;UAAA,EAAC;UACtD/J,oDAAA,sBACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAA2D;UAAzBA,wDAAA,mBAAAw4B,2DAAA;YAAA,OAASxpB,GAAA,CAAAoV,YAAA,EAAc;UAAA,EAAC;UACxDpkB,oDAAA,sBAAG;UAAAA,uDAAA,aAA6B;UAGtCA,0DAFI,EAAS,EACL,EACF;UAGFA,4DAFJ,eAAuB,eAC0B,kBACmC;UAAhDA,wDAAA,mBAAAy4B,2DAAA;YAAA,OAASzpB,GAAA,CAAAmkB,gBAAA,CAAiB,mBAAmB,CAAC;UAAA,EAAC;UAC7EnzB,oDAAA,gDACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAAoF;UAA/CA,wDAAA,mBAAA04B,2DAAA;YAAA,OAAS1pB,GAAA,CAAAmkB,gBAAA,CAAiB,kBAAkB,CAAC;UAAA,EAAC;UACjFnzB,oDAAA,gDACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAAgF;UAAhDA,wDAAA,mBAAA24B,2DAAA;YAAA,OAAS3pB,GAAA,CAAAmkB,gBAAA,CAAiB,mBAAmB,CAAC;UAAA,EAAC;UAC7EnzB,oDAAA,0CACF;UAGNA,0DAHM,EAAS,EACL,EACF,EACF;UACNA,wDAAA,KAAA44B,wCAAA,kBAAuD;UAoDzD54B,0DAAA,EAAe;UAITA,4DAHN,0BAAsD,eAChC,eACgC,kBACG;UAAnBA,wDAAA,mBAAA64B,2DAAA;YAAA,OAAS7pB,GAAA,CAAA3B,MAAA,EAAQ;UAAA,EAAC;UAChDrN,oDAAA,wCACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAA8D;UAAzBA,wDAAA,mBAAA84B,2DAAA;YAAA,OAAS9pB,GAAA,CAAAoV,YAAA,EAAc;UAAA,EAAC;UAC3DpkB,oDAAA,sBACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAAqD;UAArBA,wDAAA,mBAAA+4B,2DAAA;YAAA,OAAS/pB,GAAA,CAAAwX,QAAA,EAAU;UAAA,EAAC;UAClDxmB,oDAAA,sBACF;UAIRA,0DAJQ,EAAS,EACL,EACF,EACS,EACT;;;UAlH4BA,uDAAA,IAA+C;UAA/CA,8DAAA,YAAAgP,GAAA,CAAA+J,WAAA,CAAA8Y,qBAAA,CAA+C;UACzC7xB,uDAAA,EAA0B;UAA1BA,wDAAA,YAAAgP,GAAA,CAAAiU,uBAAA,CAA0B;UAYIjjB,uDAAA,GAA+B;UAA/BA,8DAAA,YAAAgP,GAAA,CAAA+J,WAAA,CAAA8K,KAAA,CAA+B;UAKrC7jB,uDAAA,GAA6B;UAA7BA,8DAAA,YAAAgP,GAAA,CAAA+J,WAAA,CAAA+K,GAAA,CAA6B;UA4BzD9jB,uDAAA,IAAiB;UAAjBA,wDAAA,SAAAgP,GAAA,CAAA0iB,WAAA,CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChDW;AAES;AAC5C;;;;;;;;;;;;;;;;;;;ICsBrB1xB,6DADF,aAA0E,SACpE;IAAAA,qDAAA,GAA4E;IAAAA,2DAAA,EAAK;IACrFA,6DAAA,SAAI;IAAAA,qDAAA,GAAuB;IAAAA,2DAAA,EAAK;IAChCA,6DAAA,SAAI;IAAAA,qDAAA,GAAwD;;IAAAA,2DAAA,EAAK;IACjEA,6DAAA,SAAI;IAAAA,qDAAA,GAA+C;;IAAAA,2DAAA,EAAK;IAEtDA,6DADF,cAA6B,kBAEO;IAAhCA,yDAAA,mBAAAg5B,uEAAA;MAAA,MAAAC,OAAA,GAAAj5B,4DAAA,CAAA4Q,GAAA,EAAA3P,SAAA;MAAA,MAAA4P,MAAA,GAAA7Q,4DAAA;MAAA,OAAAA,0DAAA,CAAS6Q,MAAA,CAAAhJ,eAAA,CAAAoxB,OAAA,CAAqB;IAAA,EAAC;IAACj5B,qDAAA,oBAAE;IAExCA,2DAFwC,EAAS,EAC1C,EACF;;;;IARCA,wDAAA,GAA4E;IAA5EA,gEAAA,CAAAi5B,OAAA,CAAArf,SAAA,6BAAAqf,OAAA,CAAArf,SAAA,iCAA4E;IAC5E5Z,wDAAA,GAAuB;IAAvBA,gEAAA,CAAAi5B,OAAA,CAAAnoB,aAAA,CAAuB;IACvB9Q,wDAAA,GAAwD;IAAxDA,gEAAA,CAAAi5B,OAAA,CAAAloB,SAAA,GAAA/Q,0DAAA,OAAAi5B,OAAA,CAAAloB,SAAA,OAAwD;IACxD/Q,wDAAA,GAA+C;IAA/CA,gEAAA,CAAAA,0DAAA,QAAAi5B,OAAA,CAAAC,eAAA,EAA+C;IAEEl5B,wDAAA,GAAwB;IAAxBA,yDAAA,cAAAi5B,OAAA,CAAAz1B,KAAA,CAAwB;;;;;;IAsE3ExD,6DAHJ,SAAuD,SAEjD,sBAC0D;IAAhCA,+DAAA,2BAAAm5B,kGAAA30B,MAAA;MAAA,MAAA40B,UAAA,GAAAp5B,4DAAA,CAAAyE,GAAA,EAAAxD,SAAA;MAAAjB,iEAAA,CAAAo5B,UAAA,CAAAC,SAAA,EAAA70B,MAAA,MAAA40B,UAAA,CAAAC,SAAA,GAAA70B,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAA+B;IAE7DxE,2DADE,EAAc,EACX;IACLA,6DAAA,SAAI;IAAAA,qDAAA,GAA0C;;IAAAA,2DAAA,EAAK;IACnDA,6DAAA,SAAI;IAAAA,qDAAA,GAAqC;IAAAA,2DAAA,EAAK;IAC9CA,6DAAA,SAAI;IAAAA,qDAAA,GAA0B;IAAAA,2DAAA,EAAK;IACnCA,6DAAA,UAAI;IAAAA,qDAAA,IAAmC;IAAAA,2DAAA,EAAK;IAC5CA,6DAAA,UAAI;IAAAA,qDAAA,IAAsC;IAC5CA,2DAD4C,EAAK,EAC5C;;;;;IAR2BA,wDAAA,GAA+B;IAA/BA,+DAAA,YAAAo5B,UAAA,CAAAC,SAAA,CAA+B;IAGzDr5B,wDAAA,GAA0C;IAA1CA,gEAAA,CAAAA,0DAAA,OAAAo5B,UAAA,CAAA53B,OAAA,EAA0C;IAC1CxB,wDAAA,GAAqC;IAArCA,gEAAA,CAAA6Q,MAAA,CAAApP,UAAA,CAAA23B,UAAA,CAAA13B,WAAA,EAAqC;IACrC1B,wDAAA,GAA0B;IAA1BA,gEAAA,CAAAo5B,UAAA,CAAAz3B,YAAA,CAA0B;IAC1B3B,wDAAA,GAAmC;IAAnCA,gEAAA,CAAA6Q,MAAA,CAAApP,UAAA,CAAA23B,UAAA,CAAAx3B,SAAA,EAAmC;IACnC5B,wDAAA,GAAsC;IAAtCA,gEAAA,CAAA6Q,MAAA,CAAApP,UAAA,CAAA23B,UAAA,CAAAE,YAAA,EAAsC;;;;;;IApDlDt5B,6DADF,kBAAgD,qBAC9B;IACdA,qDAAA,GACF;IAAAA,2DAAA,EAAiB;IAEfA,6DADF,uBAA2B,SACrB;IACFA,qDAAA,yJACF;IAAAA,2DAAA,EAAK;IAGHA,6DADF,cAAwB,gBACkE;IACtFA,qDAAA,iCACF;IAAAA,2DAAA,EAAQ;IACRA,6DAAA,gBAAoG;IAAvCA,+DAAA,2BAAAu5B,sFAAA/0B,MAAA;MAAAxE,4DAAA,CAAAsR,GAAA;MAAA,MAAAT,MAAA,GAAA7Q,4DAAA;MAAAA,iEAAA,CAAA6Q,MAAA,CAAA2oB,QAAA,CAAA1oB,aAAA,EAAAtM,MAAA,MAAAqM,MAAA,CAAA2oB,QAAA,CAAA1oB,aAAA,GAAAtM,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAAoC;IACnGxE,2DADE,EAAoG,EAChG;IAGJA,6DADF,eAAwB,iBAC8D;IAClFA,qDAAA,kCACF;IAAAA,2DAAA,EAAQ;IACRA,6DAAA,uBAA6D;IAAjCA,+DAAA,2BAAAy5B,6FAAAj1B,MAAA;MAAAxE,4DAAA,CAAAsR,GAAA;MAAA,MAAAT,MAAA,GAAA7Q,4DAAA;MAAAA,iEAAA,CAAA6Q,MAAA,CAAAwoB,SAAA,EAAA70B,MAAA,MAAAqM,MAAA,CAAAwoB,SAAA,GAAA70B,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAAuB;IAAUxE,qDAAA,iCAC7D;IACFA,2DADE,EAAc,EACV;IAGJA,6DADF,eAAwB,iBACyC;IAC7DA,qDAAA,4BACF;IAAAA,2DAAA,EAAQ;IACRA,6DAAA,UAAI;IAAAA,qDAAA,sFAAa;IAEnBA,2DAFmB,EAAK,EAElB;IAKAA,6DAHN,iBAAoE,aAC3D,cACgD,UAC/C;IAAAA,qDAAA,oBAAE;IAAAA,2DAAA,EAAK;IACXA,6DAAA,UAAI;IAAAA,qDAAA,gCAAI;IAAAA,2DAAA,EAAK;IACbA,6DAAA,UAAI;IAAAA,qDAAA,gCAAI;IAAAA,2DAAA,EAAK;IACbA,6DAAA,UAAI;IAAAA,qDAAA,gCAAI;IAAAA,2DAAA,EAAK;IACbA,6DAAA,UAAI;IAAAA,qDAAA,gCAAI;IAEZA,2DAFY,EAAK,EACV,EACC;IACRA,6DAAA,aAAO;IACLA,yDAAA,KAAA05B,4DAAA,kBAAuD;IAa3D15B,2DADE,EAAQ,EACF;IAGNA,6DADF,eAAkD,iBAC4B;IAAAA,qDAAA,iCAC1E;IAAAA,6DAAA,aAAsB;IAAAA,qDAAA,wDAAQ;IAChCA,2DADgC,EAAI,EAC5B;IAERA,6DAAA,oBACwC;IAAtCA,+DAAA,2BAAA25B,0FAAAn1B,MAAA;MAAAxE,4DAAA,CAAAsR,GAAA;MAAA,MAAAT,MAAA,GAAA7Q,4DAAA;MAAAA,iEAAA,CAAA6Q,MAAA,CAAA2oB,QAAA,CAAAr0B,cAAA,EAAAX,MAAA,MAAAqM,MAAA,CAAA2oB,QAAA,CAAAr0B,cAAA,GAAAX,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAAqC;IACvCxE,qDAAA;IACFA,2DADE,EAAW,EACP;IAGJA,6DADF,eAAkD,iBAC2B;IAAAA,qDAAA,iCACzE;IAAAA,6DAAA,aAAsB;IAAAA,qDAAA,wDAAQ;IAChCA,2DADgC,EAAI,EAC5B;IACRA,6DAAA,oBAC+B;IAA7BA,+DAAA,2BAAA45B,0FAAAp1B,MAAA;MAAAxE,4DAAA,CAAAsR,GAAA;MAAA,MAAAT,MAAA,GAAA7Q,4DAAA;MAAAA,iEAAA,CAAA6Q,MAAA,CAAA2oB,QAAA,CAAA5nB,KAAA,EAAApN,MAAA,MAAAqM,MAAA,CAAA2oB,QAAA,CAAA5nB,KAAA,GAAApN,MAAA;MAAA,OAAAxE,0DAAA,CAAAwE,MAAA;IAAA,EAA4B;IAC9BxE,qDAAA;IAEJA,2DAFI,EAAW,EACP,EACO;IAEbA,6DADF,0BAAsD,kBACiB;IAAvBA,yDAAA,mBAAA65B,gFAAA;MAAA,MAAAC,MAAA,GAAA95B,4DAAA,CAAAsR,GAAA,EAAAlN,SAAA;MAAA,MAAAyM,MAAA,GAAA7Q,4DAAA;MAAA,OAAAA,0DAAA,CAAS6Q,MAAA,CAAAxL,OAAA,CAAAy0B,MAAA,CAAY;IAAA,EAAC;IAAC95B,qDAAA,oBAAE;IAAAA,2DAAA,EAAS;IAChFA,6DAAA,kBAAoE;IAAhCA,yDAAA,mBAAA+5B,gFAAA;MAAA,MAAAD,MAAA,GAAA95B,4DAAA,CAAAsR,GAAA,EAAAlN,SAAA;MAAA,MAAAyM,MAAA,GAAA7Q,4DAAA;MAAA,OAAAA,0DAAA,CAAS6Q,MAAA,CAAAmB,gBAAA,CAAA8nB,MAAA,CAAqB;IAAA,EAAC;IAAC95B,qDAAA,4CAAM;IAE9EA,2DAF8E,EAAS,EACpE,EACT;;;;IA/ENA,wDAAA,GACF;IADEA,iEAAA,wEAAA6Q,MAAA,CAAAoB,SAAA,CAAAxM,UAAA,OAAAoL,MAAA,CAAAoB,SAAA,CAAAvM,MAAA,OACF;IAUiE1F,wDAAA,GAAoC;IAApCA,+DAAA,YAAA6Q,MAAA,CAAA2oB,QAAA,CAAA1oB,aAAA,CAAoC;IAOrE9Q,wDAAA,GAAuB;IAAvBA,+DAAA,YAAA6Q,MAAA,CAAAwoB,SAAA,CAAuB;IAuBzBr5B,wDAAA,IAA6B;IAA7BA,yDAAA,YAAA6Q,MAAA,CAAAmpB,0BAAA,CAA6B;IAqBrDh6B,wDAAA,GAAqC;IAArCA,+DAAA,YAAA6Q,MAAA,CAAA2oB,QAAA,CAAAr0B,cAAA,CAAqC;IASrCnF,wDAAA,GAA4B;IAA5BA,+DAAA,YAAA6Q,MAAA,CAAA2oB,QAAA,CAAA5nB,KAAA,CAA4B;;;ADpHhC,MAAOoE,8BAA+B,SAAQlW,yEAAa;EAC/DgG,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,KAAuB,EACvBg0B,qBAA2C,EAC3C5zB,OAAuB,EACvBD,KAAqB,EACrBE,QAAkB,EAClBC,aAA2B,EAC3BJ,aAA2B;IAEnC,KAAK,CAACJ,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAg0B,qBAAqB,GAArBA,qBAAqB;IACrB,KAAA5zB,OAAO,GAAPA,OAAO;IACP,KAAAD,KAAK,GAALA,KAAK;IACL,KAAAE,QAAQ,GAARA,QAAQ;IACR,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAJ,aAAa,GAAbA,aAAa;IAiCvB,KAAA+zB,qBAAqB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC;IA0FxD,KAAAb,SAAS,GAAG,IAAI;EAxHhB;EAKSryB,QAAQA,CAAA;IACf,IAAI,CAACZ,KAAK,CAACa,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,IAAI,CAACI,WAAW,GAAG,EAAEJ,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,CAAC8yB,OAAO,GAAG,EAAEhzB,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACxC,IAAG,IAAI,CAAC8yB,OAAO,EAAE;UACf,IAAI,CAACvyB,YAAY,EAAE;QACrB;QACA,IAAI,CAACwyB,eAAe,EAAE;MACxB;IACF,CAAC,CAAC;EACJ;EAIAxyB,YAAYA,CAAA;IACV,IAAI,CAACzB,aAAa,CAACuC,6BAA6B,CAAC;MAC/CT,IAAI,EAAE;QAAEU,QAAQ,EAAE,IAAI,CAACwxB;MAAO;KAC/B,CAAC,CAACjzB,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACyJ,SAAS,GAAG5J,GAAG,CAACE,OAAO;MAC9B;IACF,CAAC,CAAC;EACJ;EAIAV,eAAeA,CAACvF,IAAU;IACxB,IAAIA,IAAI,IAAIA,IAAI,CAACkB,KAAK,EAAEsE,MAAM,CAACC,IAAI,CAACzF,IAAI,CAACkB,KAAK,EAAE,QAAQ,CAAC;EAC3D;EAMA42B,eAAeA,CAAA;IACb,IAAI,CAACH,qBAAqB,CAACI,wCAAwC,CAAC;MAClEpyB,IAAI,EAAE;QACJU,QAAQ,EAAE,IAAI,CAACwxB,OAAO;QACtBhyB,SAAS,EAAE,IAAI,CAACtB,SAAS;QACzBuB,QAAQ,EAAE,IAAI,CAACxB;;KAElB,CAAC,CAACM,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAACyK,YAAY,GAAG5K,GAAG,CAACE,OAAQ,IAAI,EAAE;QACtC,IAAI,CAACzB,YAAY,GAAGuB,GAAG,CAACC,UAAU;MACpC;IACF,CAAC,CAAC;EACJ;EAIAgyB,6BAA6BA,CAAA;IAC3B,IAAI,CAACL,qBAAqB,CAACM,sDAAsD,CAAC;MAChFtyB,IAAI,EAAE;QACJU,QAAQ,EAAE,IAAI,CAACwxB;;KAElB,CAAC,CAACjzB,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAI,CAAC2xB,0BAA0B,GAAG,EAAE;MACpC,IAAI3xB,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAACwxB,0BAA0B,GAAG3xB,GAAG,CAACE,OAAQ,IAAI,EAAE;QACpD,IAAIF,GAAG,CAACE,OAAO,CAACe,MAAM,EAAE;UACtB,IAAI,CAAC0wB,0BAA0B,GAAG3xB,GAAG,CAACE,OAAO,CAAC0c,GAAG,CAAExZ,CAAM,IAAI;YAC3D,OAAO;cAAE,GAAGA,CAAC;cAAE4tB,SAAS,EAAE;YAAK,CAAE;UACnC,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC;EACJ;EAGAjwB,UAAUA,CAAA;IACR,IAAI,CAACnD,KAAK,CAAC8D,KAAK,EAAE;IAClB,IAAI,CAAC9D,KAAK,CAAC+D,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACwvB,QAAQ,CAAC1oB,aAAa,CAAC;IAC1D,IAAI,CAAC7K,KAAK,CAAC+D,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACwvB,QAAQ,CAACr0B,cAAc,CAAC;IAC3D,IAAI,CAACc,KAAK,CAAC+D,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACwvB,QAAQ,CAAC5nB,KAAK,CAAC;EACtD;EAEAvE,MAAMA,CAAA;IACJ,IAAI,CAAC9G,aAAa,CAACqF,IAAI,CAAC;MACtB0B,MAAM;MACNC,OAAO,EAAE,IAAI,CAAChG;KACf,CAAC;IACF,IAAI,CAACjB,QAAQ,CAACkH,IAAI,EAAE;EACtB;EAEAgtB,cAAcA,CAACC,WAAkB;IAC/B,IAAIA,WAAW,IAAIA,WAAW,CAACnxB,MAAM,EAAE;MACrC,OAAOmxB,WAAW,CAAC7tB,MAAM,CAAC8tB,MAAM,IAAIA,MAAM,CAACrB,SAAS,CAAC,CAACpU,GAAG,CAACyV,MAAM,IAAIA,MAAM,CAACxhB,GAAG,CAAC;IACjF;IAAE,OAAO,EAAE;EACb;EAEAlH,gBAAgBA,CAAClJ,GAAQ;IACvB,IAAI,CAACM,UAAU,EAAE;IACjB,IAAI,IAAI,CAACnD,KAAK,CAACoD,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACjD,OAAO,CAACkD,aAAa,CAAC,IAAI,CAACtD,KAAK,CAACoD,aAAa,CAAC;MACpD;IACF;IACA,MAAM4uB,KAAK,GAAG;MACZ,GAAG,IAAI,CAACuB,QAAQ;MAChBmB,cAAc,EAAE,IAAI,CAACH,cAAc,CAAC,IAAI,CAACR,0BAA0B;KACpE;IACD,IAAI,CAACC,qBAAqB,CAACW,uCAAuC,CAAC;MACjE3yB,IAAI,EAAEgwB;KACP,CAAC,CAAC/wB,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACG,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC4xB,eAAe,EAAE;QACtB,IAAI,CAAC/zB,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;QAClCZ,GAAG,CAACa,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACtD,OAAO,CAAC+E,YAAY,CAAC/C,GAAG,CAACoL,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;EACJ;EAGA7J,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAChD,SAAS,GAAGgD,OAAO;IACxB,IAAI,CAACuwB,eAAe,EAAE;EACxB;EAGAz5B,MAAMA,CAACmI,GAAQ;IACb,IAAI,CAAC0wB,QAAQ,GAAG;MACd7wB,QAAQ,EAAE,IAAI,CAACwxB,OAAO;MACtBrpB,aAAa,EAAE,EAAE;MACjB3L,cAAc,EAAE,EAAE;MAClByM,KAAK,EAAC;KACP;IACD,IAAI,CAAC0oB,6BAA6B,EAAE;IACpC,IAAI,CAACt0B,aAAa,CAAC+B,IAAI,CAACe,GAAG,CAAC;EAC9B;EAEA+xB,WAAWA,CAAC/xB,GAAQ;IAClB,IAAI,CAAC9C,aAAa,CAAC+B,IAAI,CAACe,GAAG,CAAC;EAC9B;EAEAzD,OAAOA,CAACyD,GAAQ;IACdA,GAAG,CAACa,KAAK,EAAE;EACb;EAEAlI,UAAUA,CAACq5B,IAAW;IACpB,OAAOj7B,mCAAM,CAACi7B,IAAI,CAAC,CAAC7wB,MAAM,CAAC,kBAAkB,CAAC;EAChD;;;uCAjKW+L,8BAA8B,EAAAhW,gEAAA,CAAA2N,0EAAA,GAAA3N,gEAAA,CAAA6N,4DAAA,GAAA7N,gEAAA,CAAA+N,oFAAA,GAAA/N,gEAAA,CAAAiO,2EAAA,GAAAjO,gEAAA,CAAAoO,mFAAA,GAAApO,gEAAA,CAAAsO,4DAAA,GAAAtO,gEAAA,CAAAwO,sDAAA,GAAAxO,gEAAA,CAAA0O,+EAAA,GAAA1O,gEAAA,CAAAiO,mEAAA;IAAA;EAAA;;;YAA9B+H,8BAA8B;MAAApH,SAAA;MAAAwkB,QAAA,GAAApzB,yEAAA;MAAAszB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA7H,QAAA,WAAAoP,wCAAAhsB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UChBvC/O,6DAHJ,iBAA0B,qBACR,aAEgB;UAAAA,qDAAA,sEAAa;UAC7CA,2DAD6C,EAAM,EAClC;UAEfA,6DADF,mBAAc,YACyB;UAAAA,qDAAA,weAEL;UAAAA,2DAAA,EAAK;UAM/BA,6DAJN,aAA8B,aAEL,aAC0B,iBACqB;UAArCA,yDAAA,mBAAAg7B,iEAAA;YAAAh7B,4DAAA,CAAAoP,GAAA;YAAA,MAAA6rB,qBAAA,GAAAj7B,0DAAA;YAAA,OAAAA,0DAAA,CAASgP,GAAA,CAAArO,MAAA,CAAAs6B,qBAAA,CAA0B;UAAA,EAAC;UAC/Dj7B,qDAAA,mDAAO;UAGfA,2DAHe,EAAS,EACd,EACF,EACF;UAMEA,6DAJR,cAAmC,gBAC+D,aACvF,cACoE,cACzC;UAAAA,qDAAA,oBAAE;UAAAA,2DAAA,EAAK;UACrCA,6DAAA,cAA8B;UAAAA,qDAAA,gCAAI;UAAAA,2DAAA,EAAK;UACvCA,6DAAA,cAA8B;UAAAA,qDAAA,iCAAK;UAAAA,2DAAA,EAAK;UACxCA,6DAAA,cAA8B;UAAAA,qDAAA,oBAAE;UAAAA,2DAAA,EAAK;UACrCA,6DAAA,cAA8B;UAAAA,qDAAA,oBAAE;UAEpCA,2DAFoC,EAAK,EAClC,EACC;UACRA,6DAAA,aAAO;UACLA,yDAAA,KAAAk7B,6CAAA,kBAA0E;UAalFl7B,2DAHM,EAAQ,EACF,EACJ,EACO;UAEbA,6DADF,0BAAsD,0BAES;UAD7CA,+DAAA,wBAAAm7B,8EAAA32B,MAAA;YAAAxE,4DAAA,CAAAoP,GAAA;YAAApP,iEAAA,CAAAgP,GAAA,CAAAnI,SAAA,EAAArC,MAAA,MAAAwK,GAAA,CAAAnI,SAAA,GAAArC,MAAA;YAAA,OAAAxE,0DAAA,CAAAwE,MAAA;UAAA,EAAoB;UAClCxE,yDAAA,wBAAAm7B,8EAAA32B,MAAA;YAAAxE,4DAAA,CAAAoP,GAAA;YAAA,OAAApP,0DAAA,CAAcgP,GAAA,CAAApF,WAAA,CAAApF,MAAA,CAAmB;UAAA,EAAC;UAEtCxE,2DADE,EAAiB,EACF;UAGbA,6DAFJ,sBAAgB,eAC6B,kBACmB;UAAnBA,yDAAA,mBAAAo7B,iEAAA;YAAAp7B,4DAAA,CAAAoP,GAAA;YAAA,OAAApP,0DAAA,CAASgP,GAAA,CAAA3B,MAAA,EAAQ;UAAA,EAAC;UACzDrN,qDAAA,wCACF;UAGNA,2DAHM,EAAS,EACL,EACS,EACT;UAGVA,yDAAA,KAAAq7B,sDAAA,iCAAAr7B,qEAAA,CAAgE;;;UA7BjCA,wDAAA,IAAkB;UAAlBA,yDAAA,YAAAgP,GAAA,CAAAiE,YAAA,CAAkB;UAe7BjT,wDAAA,GAAoB;UAApBA,+DAAA,SAAAgP,GAAA,CAAAnI,SAAA,CAAoB;UAAuB7G,yDAAtB,aAAAgP,GAAA,CAAApI,QAAA,CAAqB,mBAAAoI,GAAA,CAAAlI,YAAA,CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3C1B;;;;;;;;;;;;;ICQxD9G,4DAAA,oBAA+E;IAC7EA,oDAAA,GACF;IAAAA,0DAAA,EAAY;;;;IAFgDA,wDAAA,UAAAs7B,WAAA,CAAkB;IAC5Et7B,uDAAA,EACF;IADEA,gEAAA,MAAAs7B,WAAA,CAAA50B,KAAA,MACF;;;;;IAgCA1G,4DAAA,WAAoC;IAClCA,oDAAA,GAEF;IAAAA,0DAAA,EAAO;;;;IAFLA,uDAAA,EAEF;IAFEA,gEAAA,MAAAu7B,IAAA,CAAA7hB,UAAA,aAEF;;;;;IALA1Z,4DADF,SAA6D,SACvD;IAAAA,oDAAA,GAAmB;IAAAA,0DAAA,EAAK;IAC5BA,4DAAA,SAAI;IACJA,wDAAA,IAAAw7B,gDAAA,mBAAoC;IAKtCx7B,0DADA,EAAK,EACA;;;;IAPCA,uDAAA,GAAmB;IAAnBA,+DAAA,CAAAy7B,OAAA,CAAAx4B,SAAA,CAAmB;IAEHjD,uDAAA,GAAc;IAAdA,wDAAA,YAAAy7B,OAAA,CAAAC,MAAA,CAAc;;;;;;IAyBtC17B,4DAAA,iBAAiH;IAAzCA,wDAAA,mBAAA27B,oFAAA;MAAA37B,2DAAA,CAAAyE,GAAA;MAAA,MAAAqN,MAAA,GAAA9R,2DAAA,GAAAoE,SAAA;MAAA,MAAAw3B,MAAA,GAAA57B,2DAAA;MAAA,OAAAA,yDAAA,CAAS47B,MAAA,CAAAC,yBAAA,CAAA/pB,MAAA,CAA8B;IAAA,EAAC;IAAC9R,oDAAA,+BAAI;IAAAA,0DAAA,EAAS;;;;;;IAsBxHA,4DADF,gBAAsF,sBACc;IAAtEA,8DAAA,2BAAA87B,uGAAAt3B,MAAA;MAAA,MAAAu3B,KAAA,GAAA/7B,2DAAA,CAAAg8B,IAAA,EAAA/6B,SAAA;MAAAjB,gEAAA,CAAA+7B,KAAA,CAAAE,SAAA,EAAAz3B,MAAA,MAAAu3B,KAAA,CAAAE,SAAA,GAAAz3B,MAAA;MAAA,OAAAxE,yDAAA,CAAAwE,MAAA;IAAA,EAAyB;IAACxE,wDAAA,2BAAA87B,uGAAAt3B,MAAA;MAAA,MAAAu3B,KAAA,GAAA/7B,2DAAA,CAAAg8B,IAAA,EAAA/6B,SAAA;MAAA,MAAAi7B,OAAA,GAAAl8B,2DAAA,GAAA6C,KAAA;MAAA,MAAA+4B,MAAA,GAAA57B,2DAAA;MAAA,OAAAA,yDAAA,CAAiB47B,MAAA,CAAAO,SAAA,CAAA33B,MAAA,EAAA03B,OAAA,EAAAH,KAAA,CAAyB;IAAA,EAAC;IACjG/7B,0DAAA,EAAc;IACdA,4DAAA,eAAmB;IAAAA,oDAAA,GAA0B;IAC/CA,0DAD+C,EAAO,EAC9C;;;;IAHsBA,uDAAA,EAAyB;IAAzBA,8DAAA,YAAA+7B,KAAA,CAAAE,SAAA,CAAyB;IAElCj8B,uDAAA,GAA0B;IAA1BA,+DAAA,CAAA+7B,KAAA,CAAAriB,UAAA,WAA0B;;;;;;IAnBjD1Z,4DAHJ,cACuE,cACnD,gBACyD;IACvEA,oDAAA,GACF;IACFA,0DADE,EAAQ,EACJ;IAGJA,4DADF,UAAK,gBACuE;IACxEA,oDAAA,iCACF;IAAAA,0DAAA,EAAQ;IAGNA,4DADF,gBAA6C,eACxB;IAAAA,oDAAA,gCAAK;IAAAA,0DAAA,EAAO;IAC/BA,4DAAA,uBAA0H;IAA9FA,wDAAA,2BAAAo8B,+FAAA53B,MAAA;MAAA,MAAA63B,mBAAA,GAAAr8B,2DAAA,CAAA4C,IAAA,EAAA3B,SAAA;MAAA,MAAA26B,MAAA,GAAA57B,2DAAA;MAAA,OAAAA,yDAAA,CAAiB47B,MAAA,CAAAU,QAAA,CAAA93B,MAAA,EAAA63B,mBAAA,CAAiC;IAAA,EAAC;IAA2Cr8B,oDAAA,qBAC1H;IACFA,0DADE,EAAc,EACR;IACRA,4DAAA,eAA4B;IAC1BA,wDAAA,KAAAu8B,iEAAA,oBAAsF;IAMpFv8B,4DADF,eAA+B,kBACgG;IAAnDA,wDAAA,mBAAAw8B,kFAAA;MAAA,MAAAH,mBAAA,GAAAr8B,2DAAA,CAAA4C,IAAA,EAAA3B,SAAA;MAAA,MAAA26B,MAAA,GAAA57B,2DAAA;MAAA,OAAAA,yDAAA,CAAS47B,MAAA,CAAAa,uBAAA,CAAAJ,mBAAA,CAAwC;IAAA,EAAC;IAC1Hr8B,oDAAA,sBACF;IAIRA,0DAJQ,EAAS,EACL,EACF,EACF,EACF;;;;;IA3BAA,uDAAA,GACF;IADEA,gEAAA,MAAAq8B,mBAAA,CAAAp5B,SAAA,MACF;IAUkFjD,uDAAA,GAAyC;IAAzCA,wDAAA,YAAA47B,MAAA,CAAAc,YAAA,CAAAL,mBAAA,EAAyC;IAI9Dr8B,uDAAA,GAAyB;IAAzBA,wDAAA,YAAAq8B,mBAAA,CAAAX,MAAA,CAAyB;;;;;;IA1B5F17B,4DADF,kBAA+C,qBAC7B;IACdA,oDAAA,mGACF;IAAAA,0DAAA,EAAiB;IAEfA,4DADF,uBAA2B,cACO;IAC9BA,wDAAA,IAAA28B,2DAAA,qBAAiH;IACnH38B,0DAAA,EAAM;IACNA,wDAAA,IAAA48B,wDAAA,mBACuE;IA+BzE58B,0DAAA,EAAe;IAEbA,4DADF,yBAAsD,iBACe;IAAvBA,wDAAA,mBAAA68B,2EAAA;MAAA,MAAA/qB,MAAA,GAAA9R,2DAAA,CAAAsR,GAAA,EAAAlN,SAAA;MAAA,MAAAw3B,MAAA,GAAA57B,2DAAA;MAAA,OAAAA,yDAAA,CAAS47B,MAAA,CAAAv2B,OAAA,CAAAyM,MAAA,CAAY;IAAA,EAAC;IAAC9R,oDAAA,qCAAK;IAAAA,0DAAA,EAAS;IACjFA,4DAAA,kBAAmE;IAAvBA,wDAAA,mBAAA88B,4EAAA;MAAA,MAAAhrB,MAAA,GAAA9R,2DAAA,CAAAsR,GAAA,EAAAlN,SAAA;MAAA,MAAAw3B,MAAA,GAAA57B,2DAAA;MAAA,OAAAA,yDAAA,CAAS47B,MAAA,CAAAv2B,OAAA,CAAAyM,MAAA,CAAY;IAAA,EAAC;IAAC9R,oDAAA,oBAAE;IAEzEA,0DAFyE,EAAS,EAC/D,EACT;;;;IAvCkCA,uDAAA,GAAgC;IAAhCA,wDAAA,SAAA47B,MAAA,CAAAmB,mBAAA,CAAAzzB,MAAA,CAAgC;IAG1CtJ,uDAAA,EAAwB;IAAxBA,wDAAA,YAAA47B,MAAA,CAAAmB,mBAAA,CAAwB;;;ADnDtD,MAAO3mB,0BAA2B,SAAQtW,yEAAa;EAC3DgG,YACUC,MAAmB,EACnBC,aAA8B,EAC9BG,aAA2B,EAC3BC,KAAqB,EACrBC,OAAuB;IAE/B,KAAK,CAACN,MAAM,CAAC;IANL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAG,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IAOjB,KAAA4c,uBAAuB,GAAU,CAC/B;MACExjB,KAAK,EAAE,EAAE;MAAEiH,KAAK,EAAE;KACnB,CACF;EARD;EAUAirB,eAAeA,CAAA;IACb,IAAI,CAACxrB,aAAa,CAACyrB,gCAAgC,CAAC;MAClD3pB,IAAI,EAAE;QACJgB,YAAY,EAAE,IAAI,CAAC1B;;KAEtB,CAAC,CAACL,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACya,uBAAuB,GAAG,CAAC;UAC9BxjB,KAAK,EAAE,EAAE;UAAEiH,KAAK,EAAE;SACnB,EAAE,GAAG2B,GAAG,CAACE,OAAO,CAAC0c,GAAG,CAACxZ,CAAC,IAAG;UACxB,OAAO;YAAEhM,KAAK,EAAEgM,CAAC;YAAE/E,KAAK,EAAE+E;UAAC,CAAE;QAC/B,CAAC,CAAC,CAAC;QACH,IAAI,CAACuxB,gBAAgB,GAAG,IAAI,CAAC/Z,uBAAuB,CAAC,CAAC,CAAC;QACvD,IAAI,CAACga,sBAAsB,EAAE;MAC/B;IACF,CAAC,CAAC;EACJ;EAKAA,sBAAsBA,CAAA;IACpB,IAAIhF,KAAK,GAAG;MACVhvB,YAAY,EAAE,IAAI,CAAC1B,WAAW;MAC9B2W,aAAa,EAAE,IAAI,CAAC8e,gBAAgB,CAACv9B,KAAK;MAC1C0I,SAAS,EAAE,IAAI,CAACtB,SAAS;MACzBuB,QAAQ,EAAE,IAAI,CAACxB;KAChB;IACD,IAAI,CAAC,IAAI,CAACo2B,gBAAgB,CAACv9B,KAAK,EAAE;MAChC,OAAOw4B,KAAK,CAAC/Z,aAAa;IAC5B;IACA,IAAI,CAAC/X,aAAa,CAAC+2B,uCAAuC,CAAC;MACzDj1B,IAAI,EAAEgwB;KACP,CAAC,CAAC/wB,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACu0B,mBAAmB,GAAG10B,GAAG,CAACE,OAAQ,IAAI,EAAE;QAC7C,IAAI,CAACzB,YAAY,GAAGuB,GAAG,CAACC,UAAW;MACrC;IACF,CAAC,CAAC;EACJ;EAEAg0B,QAAQA,CAACrJ,OAAgB,EAAEkK,eAAoB;IAC7C,IAAIA,eAAe,CAACzB,MAAM,IAAIyB,eAAe,CAACzB,MAAM,CAACpyB,MAAM,GAAG,CAAC,EAAE;MAC/D6zB,eAAe,CAACzB,MAAM,CAAC1P,OAAO,CAAE5hB,IAA6B,IAAMA,IAAI,CAAC6xB,SAAS,GAAGhJ,OAAQ,CAAC;IAC/F;IACA,IAAIA,OAAO,EAAE;MACX,IAAI,CAAC8J,mBAAmB,CAAC/Q,OAAO,CAAC,CAAC+L,OAAO,EAAEl1B,KAAK,KAAI;QAClD,IAAIk1B,OAAO,CAACqF,iBAAiB,KAAKD,eAAe,CAACC,iBAAiB,EAAE;UACnE,IAAIrF,OAAO,CAAC2D,MAAM,IAAI3T,KAAK,CAACC,OAAO,CAAC+P,OAAO,CAAC2D,MAAM,CAAC,EAAE;YACnD3D,OAAO,CAAC2D,MAAM,CAAC1P,OAAO,CAAC,CAAC5hB,IAAI,EAAEizB,CAAC,KAAI;cACjCjzB,IAAI,CAAC6xB,SAAS,GAAG,KAAK;YACxB,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ;EACF;EAEAE,SAASA,CAAClJ,OAAgB,EAAEqK,GAAQ,EAAEtyB,CAAM;IAC1C,IAAIioB,OAAO,EAAE;MACX,IAAI,CAAC8J,mBAAmB,CAAC/Q,OAAO,CAAC,CAAC+L,OAAO,EAAEl1B,KAAK,KAAI;QAClD,IAAIA,KAAK,KAAKy6B,GAAG,EAAE;UACjB,IAAIvF,OAAO,CAAC2D,MAAM,IAAI3T,KAAK,CAACC,OAAO,CAAC+P,OAAO,CAAC2D,MAAM,CAAC,EAAE;YACnD3D,OAAO,CAAC2D,MAAM,CAAC1P,OAAO,CAAC,CAAC5hB,IAAI,EAAEizB,CAAC,KAAI;cACjC,IAAIjzB,IAAI,CAACzB,QAAQ,KAAKqC,CAAC,CAACrC,QAAQ,EAAE;gBAChCyB,IAAI,CAAC6xB,SAAS,GAAG,KAAK;cACxB;YACF,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ;EACF;EAEAS,YAAYA,CAACS,eAAoB;IAC/B,OAAOA,eAAe,CAACzB,MAAM,CAAC5I,KAAK,CAAE1oB,IAAyB,IAAKA,IAAI,CAAC6xB,SAAS,CAAC;EACpF;EAGAsB,qBAAqBA,CAACj7B,IAAW;IAC/B,MAAM+H,MAAM,GAA8B,EAAE;IAC5C,KAAK,MAAMD,IAAI,IAAI9H,IAAI,EAAE;MACvB,KAAK,MAAMkD,KAAK,IAAI4E,IAAI,CAACsxB,MAAM,EAAE;QAC/B,IAAIl2B,KAAK,CAACy2B,SAAS,EAAE;UACnB5xB,MAAM,CAACuB,IAAI,CAAC;YACVjD,QAAQ,EAAEnD,KAAK,CAACmD,QAAQ;YACxBy0B,iBAAiB,EAAEhzB,IAAI,CAACgzB;WACzB,CAAC;QACJ;MACF;IACF;IACA,OAAO/yB,MAAM;EACf;EAEAwxB,yBAAyBA,CAAC/yB,GAAQ;IAChC,IAAI00B,mBAAmB,GAAG,IAAI,CAACD,qBAAqB,CAAC,IAAI,CAACR,mBAAmB,CAAC;IAC9E,IAAI,CAAC52B,aAAa,CAACs3B,oCAAoC,CAAE;MAAEx1B,IAAI,EAAE;QAACy1B,SAAS,EAAGF;MAAmB;KAChG,CAAC,CAACt2B,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACG,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACnC,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;QAClCZ,GAAG,CAACa,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEA8yB,uBAAuBA,CAACU,eAAoB;IAC1C,IAAIr1B,MAAM,CAAC+e,OAAO,CAAC,WAAWsW,eAAe,CAACl6B,SAAS,IAAI,CAAC,EAAE;MAC9D,IAAI,CAACkD,aAAa,CAACw3B,qCAAqC,CAAC;QAAC11B,IAAI,EAAG;UAAEm1B,iBAAiB,EAAED,eAAe,CAACC;QAAiB;MAAC,CAAC,CAAC,CAACl2B,SAAS,CAACmB,GAAG,IAAG;QACzI,IAAIA,GAAG,CAACG,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACnC,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACuzB,sBAAsB,EAAE;QAC/B;MACF,CAAC,CAAC;IACJ;EACA;EAEAlzB,KAAKA,CAAA;IACH,IAAI,CAACizB,gBAAgB,GAAG,IAAI,CAAC/Z,uBAAuB,CAAC,CAAC,CAAC;EACzD;EAKSjc,QAAQA,CAAA;IACf,IAAI,CAACZ,KAAK,CAACa,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACG,WAAW,GAAGD,EAAE;QACrB,IAAI,CAACqqB,eAAe,EAAE;MACxB;IACF,CAAC,CAAC;EACJ;EAEA/nB,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAChD,SAAS,GAAGgD,OAAO;IACxB,IAAI,CAACozB,sBAAsB,EAAE;EAC/B;EAGA/J,MAAMA,CAACpqB,GAAQ;IACb,IAAI,CAAC9C,aAAa,CAAC+B,IAAI,CAACe,GAAG,CAAC;EAC9B;EAEAzD,OAAOA,CAACyD,GAAQ;IACdA,GAAG,CAACa,KAAK,EAAE;EACb;;;uCArKWyM,0BAA0B,EAAApW,+DAAA,CAAA2N,0EAAA,GAAA3N,+DAAA,CAAA6N,2DAAA,GAAA7N,+DAAA,CAAA+N,mEAAA,GAAA/N,+DAAA,CAAAiO,2DAAA,GAAAjO,+DAAA,CAAAoO,mFAAA;IAAA;EAAA;;;YAA1BgI,0BAA0B;MAAAxH,SAAA;MAAAwkB,QAAA,GAAApzB,wEAAA;MAAAszB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA7H,QAAA,WAAAiS,oCAAA7uB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCrBrC/O,4DADF,iBAA0B,qBACR;UACdA,uDAAA,qBAAiC;UACnCA,0DAAA,EAAiB;UACjBA,4DAAA,mBAAc;UACZA,uDAAA,YAA0C;UAIpCA,4DAHN,aAA8B,aACN,aACqC,eACT;UAAAA,oDAAA,mBAAE;UAAAA,0DAAA,EAAQ;UACxDA,4DAAA,oBAA0E;UAA9CA,8DAAA,2BAAA69B,wEAAAr5B,MAAA;YAAAxE,2DAAA,CAAAoP,GAAA;YAAApP,gEAAA,CAAAgP,GAAA,CAAAguB,gBAAA,EAAAx4B,MAAA,MAAAwK,GAAA,CAAAguB,gBAAA,GAAAx4B,MAAA;YAAA,OAAAxE,yDAAA,CAAAwE,MAAA;UAAA,EAA8B;UACxDxE,wDAAA,KAAA89B,gDAAA,uBAA+E;UAMrF99B,0DAHI,EAAY,EACR,EAEF;UAGFA,4DAFJ,cAAsB,cAC2B,kBACO;UAAlBA,wDAAA,mBAAA+9B,6DAAA;YAAA/9B,2DAAA,CAAAoP,GAAA;YAAA,OAAApP,yDAAA,CAASgP,GAAA,CAAAjF,KAAA,EAAO;UAAA,EAAC;UACjD/J,oDAAA,sBACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAA0E;UAAnCA,wDAAA,mBAAAg+B,6DAAA;YAAAh+B,2DAAA,CAAAoP,GAAA;YAAA,OAAApP,yDAAA,CAASgP,GAAA,CAAAiuB,sBAAA,EAAwB;UAAA,EAAC;UACvEj9B,oDAAA,sBACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAAsD;UAAzBA,wDAAA,mBAAAi+B,6DAAA;YAAAj+B,2DAAA,CAAAoP,GAAA;YAAA,MAAA8uB,SAAA,GAAAl+B,yDAAA;YAAA,OAAAA,yDAAA,CAASgP,GAAA,CAAAkkB,MAAA,CAAAgL,SAAA,CAAc;UAAA,EAAC;UACnDl+B,oDAAA,sBACF;UAGNA,0DAHM,EAAS,EACL,EACF,EACF;UAMEA,4DAJR,eAAmC,iBAC+D,aACvF,cACgD,cACrB;UAAAA,oDAAA,oBAAE;UAAAA,0DAAA,EAAK;UACrCA,4DAAA,cAA8B;UAAAA,oDAAA,iCAAK;UAEvCA,0DAFuC,EAAK,EACrC,EACC;UACRA,4DAAA,aAAO;UACLA,wDAAA,KAAAm+B,yCAAA,iBAA6D;UAYrEn+B,0DAHM,EAAQ,EACF,EACJ,EACO;UAEbA,4DADF,0BAAsD,0BAES;UAD7CA,8DAAA,wBAAAo+B,0EAAA55B,MAAA;YAAAxE,2DAAA,CAAAoP,GAAA;YAAApP,gEAAA,CAAAgP,GAAA,CAAAnI,SAAA,EAAArC,MAAA,MAAAwK,GAAA,CAAAnI,SAAA,GAAArC,MAAA;YAAA,OAAAxE,yDAAA,CAAAwE,MAAA;UAAA,EAAoB;UAClCxE,wDAAA,wBAAAo+B,0EAAA55B,MAAA;YAAAxE,2DAAA,CAAAoP,GAAA;YAAA,OAAApP,yDAAA,CAAcgP,GAAA,CAAApF,WAAA,CAAApF,MAAA,CAAmB;UAAA,EAAC;UAGxCxE,0DAFI,EAAiB,EACF,EACT;UAGVA,wDAAA,KAAAq+B,kDAAA,iCAAAr+B,oEAAA,CAAoD;;;UArDdA,uDAAA,IAA8B;UAA9BA,8DAAA,YAAAgP,GAAA,CAAAguB,gBAAA,CAA8B;UACxBh9B,uDAAA,EAA0B;UAA1BA,wDAAA,YAAAgP,GAAA,CAAAiU,uBAAA,CAA0B;UA+BvCjjB,uDAAA,IAAyB;UAAzBA,wDAAA,YAAAgP,GAAA,CAAA+tB,mBAAA,CAAyB;UAcpC/8B,uDAAA,GAAoB;UAApBA,8DAAA,SAAAgP,GAAA,CAAAnI,SAAA,CAAoB;UAAuB7G,wDAAtB,aAAAgP,GAAA,CAAApI,QAAA,CAAqB,mBAAAoI,GAAA,CAAAlI,YAAA,CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;ACvD9C;AAEmE;;;;AAc7G,MAAOkmB,gBAAgB;EAG3BlnB,YACUw4B,mBAAwC,EACxCC,IAAgB;IADhB,KAAAD,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,IAAI,GAAJA,IAAI;IAJG,KAAAC,MAAM,GAAG,gBAAgB;EAKtC;EACJ;EACAC,gBAAgBA,CAACvV,OAAgC;IAC/C,OAAO,IAAI,CAACoV,mBAAmB,CAACI,4BAA4B,CAAC;MAAEz2B,IAAI,EAAEihB;IAAO,CAAE,CAAC;EACjF;EACA;EACAyV,gBAAgBA,CAACnU,WAAmB;IAClC,MAAMtB,OAAO,GAA4B;MAAEf,YAAY,EAAEqC;IAAW,CAAE;IACtE,OAAO,IAAI,CAAC8T,mBAAmB,CAACM,4BAA4B,CAAC;MAAE32B,IAAI,EAAEihB;IAAO,CAAE,CAAC;EACjF,CAAC,CAAE;EACHvB,qBAAqBA,CAACjgB,OAAe;IACnC,MAAMwhB,OAAO,GAA4B;MAAEhB,QAAQ,EAAExgB;IAAO,CAAE;IAC9D,OAAO,IAAI,CAAC42B,mBAAmB,CAACO,qCAAqC,CAAC;MAAE52B,IAAI,EAAEihB;IAAO,CAAE,CAAC,CAAC3V,IAAI,CAC3F0R,mDAAG,CAACyC,QAAQ,IAAG;MACb;MACA,IAAIA,QAAQ,EAAE;QACZ,OAAO;UACLlf,UAAU,EAAEkf,QAAQ,CAAClf,UAAU;UAC/BiL,OAAO,EAAEiU,QAAQ,CAACjU,OAAO;UACzBnL,UAAU,EAAEof,QAAQ,CAACpf,UAAU;UAC/BC,OAAO,EAAEmf,QAAQ,CAACnf,OAAO,CAAC;SAC3B;MACH;MACA,OAAOmf,QAAQ;IACjB,CAAC,CAAC,CACH;EACH;EACA;EACAoX,iBAAiBA,CAACC,SAA4B;IAC5C,OAAO,IAAI,CAACT,mBAAmB,CAACU,6BAA6B,CAAC;MAAE/2B,IAAI,EAAE82B;IAAS,CAAE,CAAC;EACpF,CAAC,CAAE;EACHje,aAAaA,CAACoI,OAOb;IACC;IACA,MAAMvI,cAAc,GAAyBuI,OAAO,CAACqB,KAAK,CAACtF,GAAG,CAAC7a,IAAI,IAAG;MACpE,MAAM8hB,aAAa,GAAG9hB,IAAI,CAACxK,kBAAkB,IAAIwK,IAAI,CAACxK,kBAAkB,GAAG,CAAC,GAAGwK,IAAI,CAACxK,kBAAkB,GAAGA,uEAAkB,CAAC8oB,GAAG;MAG/H,OAAO;QACLL,SAAS,EAAEje,IAAI,CAACkV,SAAS;QACzBgJ,KAAK,EAAEle,IAAI,CAACuV,KAAK,IAAI,EAAE;QACvB4I,UAAU,EAAEne,IAAI,CAACoV,UAAU;QAC3BgJ,MAAM,EAAEpe,IAAI,CAACyV,MAAM;QACnBkG,OAAO,EAAE3b,IAAI,CAACqe,OAAO,IAAI,CAAC;QAC1B7oB,kBAAkB,EAAEssB,aAAa;QACjCtD,OAAO,EAAExe,IAAI,CAACue,OAAO,IAAI;OAC1B;IACH,CAAC,CAAC;IAEF;IACA,MAAMsW,WAAW,GAAQ;MACvBt2B,QAAQ,EAAEugB,OAAO,CAACxhB,OAAO;MACzB0gB,YAAY,EAAEc,OAAO,CAACsB,WAAW,IAAI,CAAC;MAAE;MACxC1C,KAAK,EAAEnH,cAAc;MACrB;MACAue,UAAU,EAAEhW,OAAO,CAACuB,UAAU,IAAI,KAAK;MACvC0U,UAAU,EAAEjW,OAAO,CAACwB,UAAU,IAAI,EAAE;MACpC0U,aAAa,EAAElW,OAAO,CAACyB,aAAa,IAAI;KACzC;IAED;IACA5e,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEizB,WAAW,CAAC;IAEzD,OAAO,IAAI,CAACX,mBAAmB,CAACU,6BAA6B,CAAC;MAAE/2B,IAAI,EAAEg3B;IAAW,CAAE,CAAC,CAAC1rB,IAAI,CACvF0R,mDAAG,CAACyC,QAAQ,KAAK;MACfyB,OAAO,EAAEzB,QAAQ,EAAElf,UAAU,KAAK,CAAC;MACnCnC,OAAO,EAAEqhB,QAAQ,EAAElf,UAAU,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;MAC3DlG,IAAI,EAAE4mB,OAAO,CAACqB;KACO,EAAC,CACzB;EACH;EAEA;EACA8U,wBAAwBA,CAAA;IACtB;IACA,MAAMnW,OAAO,GAA4B;MACvCriB,SAAS,EAAE,CAAC;MACZD,QAAQ,EAAE;MACV;KACD;IACD,OAAO,IAAI,CAAC03B,mBAAmB,CAACI,4BAA4B,CAAC;MAAEz2B,IAAI,EAAEihB;IAAO,CAAE,CAAC,CAAC3V,IAAI,CAClF0R,mDAAG,CAACyC,QAAQ,IAAG;MACb;MACA,OAAO;QACLyB,OAAO,EAAEzB,QAAQ,CAAClf,UAAU,KAAK,CAAC;QAAE;QACpCnC,OAAO,EAAEqhB,QAAQ,CAACjU,OAAO,IAAI,EAAE;QAC/BnR,IAAI,EAAEolB,QAAQ,CAACnf,OAAO,IAAI;OACN;IACxB,CAAC,CAAC,CACH;EACH,CAAC,CAAE;EACHqW,gBAAgBA,CAACsK,OAAgC;IAC/C;IACA,OAAO,IAAI,CAACqV,IAAI,CAACe,IAAI,CACnB,GAAG,IAAI,CAAChB,mBAAmB,CAACiB,OAAO,iCAAiC,EACpErW,OAAO,CACR,CAAC3V,IAAI,CACJ0R,mDAAG,CAACyC,QAAQ,IAAG;MACb;MACA,OAAO;QACLyB,OAAO,EAAEzB,QAAQ,CAAClf,UAAU,KAAK,CAAC;QAAE;QACpCnC,OAAO,EAAEqhB,QAAQ,CAACjU,OAAO,IAAI,EAAE;QAC/BnR,IAAI,EAAEolB,QAAQ,CAACnf,OAAO,IAAI;OACN;IACxB,CAAC,CAAC,CACH;EACH,CAAC,CAAE;EACHuW,gBAAgBA,CAACoK,OAAgC;IAC/C;IACA,OAAO,IAAI,CAACqV,IAAI,CAACe,IAAI,CACnB,GAAG,IAAI,CAAChB,mBAAmB,CAACiB,OAAO,iCAAiC,EACpErW,OAAO,CACR,CAAC3V,IAAI,CACJ0R,mDAAG,CAACyC,QAAQ,IAAG;MACb;MACA,OAAO;QACLyB,OAAO,EAAEzB,QAAQ,CAAClf,UAAU,KAAK,CAAC;QAAE;QACpCnC,OAAO,EAAEqhB,QAAQ,CAACjU,OAAO,IAAI,EAAE;QAC/BnR,IAAI,EAAEolB,QAAQ,CAACnf,OAAO,IAAI;OACN;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACAi3B,mBAAmBA,CAAChV,WAAmB,EAAEpgB,IAAmB;IAC1D,MAAMq1B,QAAQ,GAAsB;MAClC92B,QAAQ,EAAEyB,IAAI,CAAC8d,QAAQ;MACvBE,YAAY,EAAEoC,WAAW;MACzB1C,KAAK,EAAE,CAAC;QACNO,SAAS,EAAEje,IAAI,CAACkV,SAAS;QACzBiJ,UAAU,EAAEne,IAAI,CAACoV,UAAU;QAC3BgJ,MAAM,EAAEpe,IAAI,CAACyV,MAAM;QACnBkG,OAAO,EAAE3b,IAAI,CAACqe,OAAO,IAAI,CAAC;QAC1B7oB,kBAAkB,EAAEwK,IAAI,CAACxK,kBAAkB,IAAIA,uEAAkB,CAAC8oB;OACnE;KACF;IACD,OAAO,IAAI,CAAC4V,mBAAmB,CAACU,6BAA6B,CAAC;MAAE/2B,IAAI,EAAEw3B;IAAQ,CAAE,CAAC,CAAClsB,IAAI,CACpF0R,mDAAG,CAACyC,QAAQ,IAAG;MACb,OAAO;QACLyB,OAAO,EAAEzB,QAAQ,CAAClf,UAAU,KAAK,CAAC;QAAE;QACpCnC,OAAO,EAAEqhB,QAAQ,CAACjU,OAAO,IAAI,EAAE;QAC/BnR,IAAI,EAAE,CAAC8H,IAAI,CAAC,CAAC;OACO;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACAwgB,eAAeA,CAACljB,OAAe;IAC7B;IACA;IACA,MAAM,IAAIqrB,KAAK,CAAC,mEAAmE,CAAC;EACtF;EAEA;EACArS,aAAaA,CAAC8J,WAAmB;IAC/B,OAAO,IAAI,CAAC+T,IAAI,CAACe,IAAI,CACnB,GAAG,IAAI,CAAChB,mBAAmB,CAACiB,OAAO,8BAA8B,EACjE/U,WAAW,CACZ,CAACjX,IAAI,CACJ0R,mDAAG,CAACyC,QAAQ,IAAG;MACb,OAAO;QACLyB,OAAO,EAAEzB,QAAQ,CAAClf,UAAU,KAAK,CAAC;QAClCnC,OAAO,EAAEqhB,QAAQ,CAACjU,OAAO,IAAI,EAAE;QAC/BnR,IAAI,EAAEolB,QAAQ,CAACnf,OAAO,IAAI;OAC3B;IACH,CAAC,CAAC,CACH;EACH;;;uCArLWykB,gBAAgB,EAAAhtB,sDAAA,CAAA2N,sFAAA,GAAA3N,sDAAA,CAAA6N,4DAAA;IAAA;EAAA;;;aAAhBmf,gBAAgB;MAAA4S,OAAA,EAAhB5S,gBAAgB,CAAA6S,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA;;;;;;;;;;;;;;;AChBb,IAAK3oB,iBAKX;AALD,WAAYA,iBAAiB;EAC3BA,iBAAA,CAAAA,iBAAA,8DAAQ;EACRA,iBAAA,CAAAA,iBAAA,kGAAW;EACXA,iBAAA,CAAAA,iBAAA,8DAAQ;EACRA,iBAAA,CAAAA,iBAAA,8DAAQ;AACV,CAAC,EALWA,iBAAiB,KAAjBA,iBAAiB;;;;;;;;;;;;;;ACAtB,IAAKE,aAIX;AAJD,WAAYA,aAAa;EACvBA,aAAA,CAAAA,aAAA,kDAAO;EACPA,aAAA,CAAAA,aAAA,kDAAO;EACPA,aAAA,CAAAA,aAAA,8DAAQ;AACV,CAAC,EAJWA,aAAa,KAAbA,aAAa;;;;;;;;;;;;;;ACAlB,IAAKE,mBAIX;AAJD,WAAYA,mBAAmB;EAC7BA,mBAAA,CAAAA,mBAAA,kDAAO;EACPA,mBAAA,CAAAA,mBAAA,kDAAO;EACPA,mBAAA,CAAAA,mBAAA,kDAAO;AACT,CAAC,EAJWA,mBAAmB,KAAnBA,mBAAmB;;;;;;;;;;;;;;ACAxB,IAAKD,cAGX;AAHD,WAAYA,cAAc;EACxBA,cAAA,CAAAA,cAAA,kDAAO;EACPA,cAAA,CAAAA,cAAA,kDAAO,EAAE;AACX,CAAC,EAHWA,cAAc,KAAdA,cAAc;;;;;;;;;;;;;;ACAnB,MAAMR,kBAAkB,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAyP1B", "sources": ["./src/app/@theme/pipes/specialChangeSource.pipe.ts", "./src/app/models/quotation.model.ts", "./src/app/pages/household-management/customer-change-picture/customer-change-picture.component.ts", "./src/app/pages/household-management/customer-change-picture/customer-change-picture.component.html", "./src/app/pages/household-management/finaldochouse-management/finaldochouse-management.component.ts", "./src/app/pages/household-management/finaldochouse-management/finaldochouse-management.component.html", "./src/app/pages/household-management/household-management-routing.module.ts", "./src/app/pages/household-management/household-management.component.ts", "./src/app/pages/household-management/household-management.component.html", "./src/app/pages/household-management/household-management.module.ts", "./src/app/pages/household-management/modify-floor-plan/modify-floor-plan.component.ts", "./src/app/pages/household-management/modify-floor-plan/modify-floor-plan.component.html", "./src/app/pages/household-management/modify-house-type/modify-house-type.component.ts", "./src/app/pages/household-management/modify-house-type/modify-house-type.component.html", "./src/app/pages/household-management/modify-household/modify-household.component.ts", "./src/app/pages/household-management/modify-household/modify-household.component.html", "./src/app/pages/household-management/sample-selection-result/sample-selection-result.component.ts", "./src/app/pages/household-management/sample-selection-result/sample-selection-result.component.html", "./src/app/pages/household-management/standard-house-plan/standard-house-plan.component.ts", "./src/app/pages/household-management/standard-house-plan/standard-house-plan.component.html", "./src/app/services/quotation.service.ts", "./src/app/shared/enum/enumHouseProgress.ts", "./src/app/shared/enum/enumPayStatus.ts", "./src/app/shared/enum/enumQuotationStatus.ts", "./src/app/shared/enum/enumSignStatus.ts", "./src/assets/template/quotation-template.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\n\r\n@Pipe({\r\n    name: 'specialChangeSource',\r\n    standalone: true\r\n})\r\nexport class SpecialChangeSourcePipe implements PipeTransform {\r\n    transform(value: number): string {\r\n        switch (value) {\r\n            case 1:\r\n                return '後台';\r\n            case 2:\r\n                return '前台';\r\n            default:\r\n                return '';\r\n        }\r\n    }\r\n}\r\n", "export enum CQuotationItemType {\r\n  /** 客變需求 */\r\n  客變需求 = 1,\r\n  /** 自定義 */\r\n  自定義 = 2,\r\n  /** 選樣 */\r\n  選樣 = 3\r\n}\r\n\r\nexport interface QuotationItem {\r\n  cQuotationID?: number;\r\n  cHouseID: number;\r\n  cItemName: string;\r\n  cUnit?: string;\r\n  cUnitPrice: number;\r\n  cCount: number;\r\n  cStatus?: number;\r\n  CQuotationItemType: CQuotationItemType;\r\n  cRemark?: string;\r\n}\r\n\r\nexport interface QuotationRequest {\r\n  houseId: number;\r\n  items: QuotationItem[];\r\n}\r\n\r\nexport interface QuotationResponse {\r\n  success: boolean;\r\n  message: string;\r\n  data?: QuotationItem[];\r\n}\r\n", "import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { Location } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { HouseService, SpecialChangeService } from 'src/services/api/services';\r\nimport { TblHouse, SpecialChangeRes } from 'src/services/api/models';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport * as moment from 'moment';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-customer-change-picture',\r\n  templateUrl: './customer-change-picture.component.html',\r\n  styleUrls: ['./customer-change-picture.component.scss'],\r\n})\r\n\r\nexport class CustomerChangePictureComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private valid: ValidationHelper,\r\n    private _specialChangeService: SpecialChangeService,\r\n    private _houseService: HouseService,\r\n\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private location: Location,\r\n    private _eventService: EventService\r\n  ) { super(_allow) }\r\n\r\n  @ViewChild('fileInput') fileInput: ElementRef;\r\n  imageUrlList: any[] = [];\r\n  isEdit = false\r\n  statusOptions: selectItem[] = [\r\n    {\r\n      value: 0,\r\n      key: 'allow',\r\n      label: '允許',\r\n    },\r\n    {\r\n      value: 1,\r\n      key: 'not allowed',\r\n      label: '不允許',\r\n    }\r\n  ]\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  selectedBuildCase: selectItem\r\n\r\n  buildCaseId: number\r\n  houseId: number\r\n  house: TblHouse\r\n  houseTitle: string\r\n\r\n  override ngOnInit(): void {\r\n    \r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id1');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        const idParam2 = params.get('id2');\r\n        const id2 = idParam2 ? +idParam2 : 0;\r\n        this.houseId = id2\r\n        this.getListSpecialChange()\r\n        this.getHouseById()\r\n      }\r\n    });\r\n  }\r\n\r\n\r\n  openPdfInNewTab(data?: any) {\r\n    if (data && data.CFileRes.CFile) window.open(data.CFileRes.CFile, '_blank');\r\n  }\r\n\r\n\r\n  listSpecialChange: any[]\r\n\r\n  getListSpecialChange() {\r\n    this._specialChangeService.apiSpecialChangeGetListSpecialChangePost$Json({ body: {\r\n      CHouseId: this.houseId,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    } }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialChange = res.Entries! ?? []\r\n        this.totalRecords = res.TotalItems;\r\n      }\r\n    })\r\n  }\r\n  \r\n  getHouseById() {\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({ body: {\r\n      CHouseID: this.houseId\r\n    } }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.house = res.Entries\r\n        this.houseTitle = `${this.house.CHousehold} ${this.house.CFloor}F`\r\n      }\r\n    })\r\n  }\r\n\r\n  SpecialChange : SpecialChangeRes\r\n  fileUrl : any\r\n  getSpecialChangeById(ref: any, CSpecialChangeID: any) {\r\n    this._specialChangeService.apiSpecialChangeGetSpecialChangeByIdPost$Json({ body: CSpecialChangeID }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.SpecialChange = res.Entries\r\n        this.formSpecialChange = {\r\n          CApproveRemark: this.SpecialChange.CApproveRemark,\r\n          CBuildCaseID: this.buildCaseId,\r\n          CDrawingName: this.SpecialChange.CDrawingName,\r\n          CHouseID: this.houseId,\r\n          SpecialChangeFiles: null \r\n        }\r\n        if(this.SpecialChange.CChangeDate) {\r\n          this.formSpecialChange.CChangeDate = new Date(this.SpecialChange.CChangeDate);\r\n        }\r\n        this.dialogService.open(ref)\r\n      }\r\n    })\r\n  }\r\n\r\n  formSpecialChange: any\r\n\r\n  onSaveSpecialChange(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._specialChangeService.apiSpecialChangeSaveSpecialChangePost$Json({ body: this.formatParam() }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getListSpecialChange()\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListSpecialChange();\r\n  }\r\n\r\n\r\n  addNew(ref: any) {\r\n    this.imageUrlList = [];\r\n    this.isEdit = false\r\n    this.formSpecialChange = {\r\n      CApproveRemark: '',\r\n      CBuildCaseID: this.buildCaseId,\r\n      CChangeDate: '',\r\n      CDrawingName: '',\r\n      CHouseID: this.houseId,\r\n      SpecialChangeFiles: null \r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onEdit(ref: any, specialChange : any ) {\r\n    this.imageUrlList = [];\r\n    this.isEdit = true\r\n    this.getSpecialChangeById(ref, specialChange.CSpecialChangeID)\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[討論日期]', this.formSpecialChange.CChangeDate)\r\n    this.valid.required('[圖面名稱]', this.formSpecialChange.CDrawingName)\r\n    this.valid.required('[審核說明]', this.formSpecialChange.CApproveRemark)\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DD');\r\n    }\r\n    return ''\r\n  }\r\n\r\n  deleteDataFields(array: any[]) {\r\n    for (const item of array) {\r\n      delete item.data;\r\n    }\r\n    return array; \r\n  }\r\n\r\n  formatParam() {\r\n    const result = {\r\n      ...this.formSpecialChange,\r\n      SpecialChangeFiles: this.imageUrlList\r\n    }\r\n    this.deleteDataFields(result.SpecialChangeFiles)\r\n\r\n    if (this.formSpecialChange.CChangeDate) {\r\n      result.CChangeDate = this.formatDate(this.formSpecialChange.CChangeDate)\r\n    }\r\n    return result\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  removeBase64Prefix(base64String: any) {\r\n    const prefixIndex = base64String.indexOf(\",\");\r\n    if (prefixIndex !== -1) {\r\n      return base64String.substring(prefixIndex + 1);\r\n    }\r\n    return base64String;\r\n  }\r\n\r\n  detectFiles(event: any) {\r\n    const files: FileList = event.target.files;\r\n    if (files && files.length > 0) {   \r\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf'];\r\n      const fileRegex = /pdf|jpg|jpeg|png/i;\r\n      for (let i = 0; i < files.length; i++) {\r\n        const file = files[i];\r\n        if (!fileRegex.test(file.type)) {\r\n          this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔');\r\n        }\r\n        if (allowedTypes.includes(file.type)) {\r\n          const reader = new FileReader();\r\n\r\n          reader.onload = (e: any) => {\r\n            const fileType = file.type.startsWith('image/') ? 2 : 1;\r\n            this.imageUrlList.push({\r\n              data: e.target.result,\r\n              CFileBlood: this.removeBase64Prefix(e.target.result),\r\n              CFileName: file.name,\r\n              CFileType: fileType\r\n            });\r\n\r\n            if (this.imageUrlList.length === files.length) {\r\n              console.log('this.imageUrlList', this.imageUrlList);\r\n              if (this.fileInput) {\r\n                this.fileInput.nativeElement.value = null;\r\n              }\r\n            }\r\n          };\r\n          reader.readAsDataURL(file);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  isPDFString(str: any): boolean {\r\n    if(str) {\r\n      return str.toLowerCase().endsWith(\".pdf\")\r\n    }\r\n    return false\r\n  }\r\n\r\n  isImage(fileType: number): boolean {\r\n    return fileType === 2;\r\n  }\r\n\r\n\r\n  isPdf(extension: string): boolean {\r\n    return extension.toLowerCase() === 'pdf';\r\n  }\r\n\r\n  listPictures: any[] = []\r\n\r\n  removeFile(index: number) {\r\n    this.imageUrlList.splice(index, 1); \r\n  }\r\n\r\n  removeImage(pictureId: number) {\r\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId)\r\n  }\r\n\r\n  uploadImage(ref: any) {\r\n  }\r\n\r\n  renameFile(event: any, index: number) {\r\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, { type: this.listPictures[index].CFile.type });\r\n    this.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n  openNewTab(url: any) {\r\n    if(url) window.open(url, \"_blank\"); \r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    戶別管理 > 洽談紀錄上傳 > {{houseTitle}}\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此上傳與該戶別客戶討論的客戶圖面，審核通過後客戶就可以在前台檢視該圖面。</h1>\r\n\r\n    <div class=\"d-flex flex-wrap\">\r\n\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-info\" (click)=\"addNew(dialogUploadDrawing)\" *ngIf=\"isCreate\">\r\n            上傳圖面</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\" class=\"text-center\">\r\n            <th scope=\"col\" class=\"col-1\">來源</th>\r\n            <th scope=\"col\" class=\"col-1\">討論日期</th>\r\n            <th scope=\"col\" class=\"col-1\">圖面名稱</th>\r\n            <th scope=\"col\" class=\"col-1\">上傳日期</th>\r\n            <th scope=\"col\" class=\"col-1\">審核狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of listSpecialChange ; let i = index\" class=\"text-center\">\r\n            <td>{{ item.CSource| specialChangeSource }}</td>\r\n            <td>{{ formatDate(item.CChangeDate)}}</td>\r\n            <td>{{ item.CDrawingName}}</td>\r\n            <td>{{formatDate(item.CCreateDT)}}</td>\r\n            <td>{{ item.CIsApprove == null ? '待審核' : ( item.CIsApprove ? \"通過\" : \"駁回\")}}</td>\r\n            <td class=\"text-center w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" *ngIf=\"isUpdate\"\r\n                (click)=\"onEdit(dialogUploadDrawing, item)\">檢視</button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n  <nb-card-footer>\r\n    <div class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm\" (click)=\"goBack()\">\r\n        返回上一頁\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n\r\n<ng-template #dialogUploadDrawing let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"min-width:600px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      戶別管理 > 洽談紀錄上傳 > {{house.CHousehold}} &nbsp; {{house.CFloor}}F\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"CChangeDate\" #CChangeDate class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          討論日期\r\n        </label>\r\n        <p-calendar [appendTo]=\"'CChangeDate'\" placeholder=\"年/月/日\" [iconDisplay]=\"'input'\" [showIcon]=\"true\"\r\n          inputId=\"icondisplay\" dateFormat=\"yy/mm/dd\" [(ngModel)]=\"formSpecialChange.CChangeDate\" [disabled]=\"isEdit\"\r\n          [showButtonBar]=\"true\" class=\"!w-[400px]\"></p-calendar>\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"cDrawingName\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          圖面名稱\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"圖面名稱\" [(ngModel)]=\"formSpecialChange.CDrawingName\"\r\n          [disabled]=\"isEdit\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"cIsEnable\" class=\" mr-4\" style=\"min-width:75px\" baseLabel>\r\n          選樣結果\r\n        </label>\r\n        <button *ngIf=\"!(isEdit && SpecialChange.CIsApprove === null)\" class=\"btn btn-info\"\r\n          (click)=\"inputFile.click()\">選擇檔案</button>\r\n        <input #inputFile type=\"file\" id=\"fileInput\" class=\"hidden\" (change)=\"detectFiles($event)\" [disabled]=\"isEdit\"\r\n          accept=\"image/jpeg, image/jpg, application/pdf\" multiple>\r\n      </div>\r\n\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label baseLabel class=\"align-self-start mr-4\" style=\"min-width:75px\">\r\n        </label>\r\n        <div class=\"flex flex-wrap mt-2\" *ngIf=\"!isEdit\">\r\n          <div *ngFor=\"let file of imageUrlList; let i = index\" class=\"relative w-24 h-24 mr-2 mb-2 border\">\r\n            <img *ngIf=\"isImage(file.CFileType)\" class=\"w-full h-full object-contain cursor-pointer\" [src]=\"file.data\">\r\n            <span *ngIf=\"!isImage(file.CFileType)\"\r\n              class=\"absolute inset-0 flex items-center justify-center cursor-pointer\">PDF</span>\r\n            <p class=\"absolute -bottom-4 left-0 w-full text-xs truncate px-1 text-center\">{{ file.CFileName }}</p>\r\n            <span class=\"absolute top-0 right-0 cursor-pointer bg-white rounded-full\" (click)=\"removeFile(i)\">\r\n              <i class=\"fa fa-times text-red-600\"></i>\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"flex flex-wrap mt-2\" *ngIf=\"isEdit\">\r\n          <div *ngFor=\"let file of SpecialChange.CFileRes; let i = index\" class=\"relative w-24 h-24 mr-2 mb-2 border\">\r\n            <img *ngIf=\"!isPDFString(file.CFile)\" class=\"w-full h-full object-contain cursor-pointer\" [src]=\"file.CFile\"\r\n              (click)=\"openNewTab(file.CFile)\">\r\n            <span *ngIf=\"isPDFString(file.CFile)\"\r\n              class=\"absolute inset-0 flex items-center justify-center cursor-pointer\"\r\n              (click)=\"openNewTab(file.CFile)\">PDF</span>\r\n            <p class=\"absolute -bottom-4 left-0 w-full text-xs truncate px-1 text-center\">{{ file.CFileName }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"cApproveRemark\" baseLabel class=\"required-field align-self-start mr-4\"\r\n          style=\"min-width:75px\">審核說明</label>\r\n        <textarea name=\"remark\" id=\"cApproveRemark\" rows=\"5\" nbInput style=\"resize: none;\" class=\"w-full\"\r\n          [disabled]=\"isEdit\" class=\"w-full\" [(ngModel)]=\"formSpecialChange.CApproveRemark\"></textarea>\r\n      </div>\r\n\r\n      <div class=\"d-flex justify-content-center\">\r\n        <button class=\"btn btn-outline-secondary m-2\" (click)=\"onClose(ref)\">取消</button>\r\n        <button class=\"btn btn-success m-2\" *ngIf=\"!isEdit\" (click)=\"onSaveSpecialChange(ref)\">送出審核</button>\r\n      </div>\r\n    </nb-card-body>\r\n  </nb-card>\r\n</ng-template>", "import { Component, DestroyRef, OnInit, ViewChild } from '@angular/core';\r\nimport { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\r\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NgFor, NgIf, NgTemplateOutlet } from '@angular/common';\r\nimport { PaginationComponent } from '../../components/pagination/pagination.component';\r\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { FinalDocumentService, HouseService } from 'src/services/api/services';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { GetFinalDocListByHouse, TblFinalDocument, TblHouse } from 'src/services/api/models';\r\nimport * as moment from 'moment';\r\nimport { FullCalendarComponent, FullCalendarModule } from '@fullcalendar/angular';\r\nimport { Calendar, CalendarOptions } from 'fullcalendar';\r\nimport interactionPlugin from '@fullcalendar/interaction';\r\nimport dayGridPlugin from '@fullcalendar/daygrid';\r\nimport timeGridPlugin from '@fullcalendar/timegrid';\r\nimport listPlugin from '@fullcalendar/list';\r\nimport bootstrapPlugin from '@fullcalendar/bootstrap';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\nimport { Location } from '@angular/common';\r\nimport { ApiFinalDocumentUploadFinalDocPost$Json$Params } from 'src/services/api/fn/final-document/api-final-document-upload-final-doc-post-json';\r\n\r\n@Component({\r\n  selector: 'app-finaldochouse-management',\r\n  standalone: true,\r\n  imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    NbCheckboxModule,\r\n    FullCalendarModule,\r\n    CalendarModule\r\n  ],\r\n  templateUrl: './finaldochouse-management.component.html',\r\n  styleUrl: './finaldochouse-management.component.scss'\r\n})\r\nexport class FinaldochouseManagementComponent extends BaseComponent implements OnInit {\r\n  @ViewChild('calendar') calendarComponent: FullCalendarComponent;\r\n  calendarApi: Calendar;\r\n  maxDate!: Date;\r\n\r\n  calendarOptions: CalendarOptions = {\r\n    plugins: [\r\n      interactionPlugin,\r\n      dayGridPlugin,\r\n      timeGridPlugin,\r\n      listPlugin,\r\n      timeGridPlugin,\r\n      bootstrapPlugin\r\n    ],\r\n    locale: 'zh-tw',\r\n    headerToolbar: {\r\n      left: 'prev',\r\n      center: 'title',\r\n      right: 'next'\r\n    },\r\n  };\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private finalDocumentService: FinalDocumentService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private destroyref: DestroyRef,\r\n    private _eventService: EventService,\r\n    private location: Location,\r\n    private _houseService: HouseService\r\n  ) {\r\n    super(_allow);\r\n    this.maxDate = new Date();\r\n  }\r\n\r\n  currentHouseID: number;\r\n  buildCaseId: number;\r\n  fileName: string | null;\r\n  file: File | null = null;\r\n  CDocumentName: string | null;\r\n  CNote: string | null;\r\n  CApproveRemark: string | null;\r\n  // request\r\n  getListFinalDocRequest: GetFinalDocListByHouse = {};\r\n  uploadFinaldocRequest: ApiFinalDocumentUploadFinalDocPost$Json$Params = {};\r\n\r\n  // response\r\n  listFinalDoc: TblFinalDocument[] = [];\r\n  houseByID: TblHouse;\r\n\r\n  override ngOnInit() {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id1');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id;\r\n        const idParam2 = params.get('id2');\r\n        const id2 = idParam2 ? +idParam2 : 0;\r\n        this.currentHouseID = id2;\r\n        this.getList();\r\n        this.getHouseById();\r\n      }\r\n    });\r\n  }\r\n\r\n  addNew(ref: any) {\r\n    this.CApproveRemark = null;\r\n    this.CDocumentName = null;\r\n    this.CNote = null;\r\n    this.file = null;\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  openPdfInNewTab(data: TblFinalDocument) {\r\n    if (data) {\r\n      if (data.CSignDate && data.CSign) {\r\n        window.open(data.CFileAfter!, '_blank');\r\n      }\r\n      else {\r\n        window.open(data.CFileBefore!, '_blank');\r\n      }\r\n    }\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n\r\n  onFileSelected(event: any) {\r\n    const file: File = event.target.files[0];\r\n    const fileRegex = /pdf/i;\r\n    if (!fileRegex.test(file.type)) {\r\n      this.message.showErrorMSG('文件格式不正確，僅允許 pdf 文件');\r\n      return;\r\n    }\r\n    if (file) {\r\n      const allowedTypes = ['application/pdf'];\r\n      if (allowedTypes.includes(file.type)) {\r\n        this.fileName = file.name;\r\n        this.file = file;\r\n      }\r\n    }\r\n  }\r\n\r\n  clearFile() {\r\n    if (this.file) {\r\n      this.file = null;\r\n      this.fileName = null;\r\n    }\r\n  }\r\n\r\n  getHouseById() {\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({\r\n      body: { CHouseID: this.currentHouseID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseByID = res.Entries\r\n      }\r\n    })\r\n  }\r\n\r\n  getList() {\r\n    this.getListFinalDocRequest.PageSize = this.pageSize;\r\n    this.getListFinalDocRequest.PageIndex = this.pageIndex;\r\n    if (this.currentHouseID != 0) {\r\n      this.getListFinalDocRequest.CHouseID = this.currentHouseID;\r\n      this.finalDocumentService.apiFinalDocumentGetListFinalDocByHousePost$Json({ body: this.getListFinalDocRequest })\r\n        .pipe()\r\n        .subscribe(res => {\r\n          if (res.StatusCode == 0) {\r\n            if (res.Entries) {\r\n              this.listFinalDoc = res.Entries;\r\n              this.totalRecords = res.TotalItems!;\r\n              if (this.listFinalDoc) {\r\n                for (let i = 0; i < this.listFinalDoc.length; i++) {\r\n                  if (this.listFinalDoc[i].CSignDate)\r\n                    this.listFinalDoc[i].CSignDate = moment(this.listFinalDoc[i].CSignDate).format(\"yyyy/MM/DD H:mm:ss\");\r\n                }\r\n              }\r\n            }\r\n          }\r\n        })\r\n    }\r\n  }\r\n\r\n  onCreateFinalDoc(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n    this.finalDocumentService.apiFinalDocumentUploadFinalDocPost$Json({\r\n      body: {\r\n        CHouseID: this.currentHouseID,\r\n        CBuildCaseID: this.buildCaseId,\r\n        CDocumentName: this.CDocumentName!,\r\n        CApproveRemark: this.CApproveRemark!,\r\n        CNote: this.CNote!,\r\n        CFile: this.file as Blob\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.getList();\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    })\r\n  }\r\n\r\n  convertToBlob(data: string | ArrayBuffer | null, mimeType: string = 'application/octet-stream'): Blob | undefined {\r\n    if (data instanceof ArrayBuffer) {\r\n      return new Blob([data], { type: mimeType });\r\n    } else if (typeof data === 'string') {\r\n      return new Blob([data], { type: mimeType });\r\n    } else {\r\n      return undefined;\r\n    }\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[文件格式不正確]', this.file)\r\n    this.valid.required('[文件名稱]', this.CDocumentName)\r\n    this.valid.required('[送審資訊]', this.CApproveRemark)\r\n    this.valid.required('[系統操作說明]', this.CNote)\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <!-- <ngx-breadcrumb></ngx-breadcrumb> -->\r\n    <div style=\"font-size: 32px;\">戶別管理 / 簽署文件歷程</div>\r\n  </nb-card-header>\r\n  <nb-card-body class=\"bg-white\">\r\n    <div class=\"col-12\">\r\n      <div class=\"row\">\r\n        <div class=\"flex form-group col-12 col-md-9 text-right\">\r\n          <span for=\"date-select1\" class=\"mr-3 mt-2\">\r\n            建立時間\r\n          </span>\r\n          <p-calendar [appendTo]=\"'body'\" placeholder='年/月/日' dateFormat=\"yy/mm/dd\"\r\n            [(ngModel)]=\"getListFinalDocRequest.CDateStart\"\r\n            [maxDate]=\"maxDate\"></p-calendar>\r\n          <span for=\"date-select1\" class=\"mr-1 ml-1 mt-2\">~</span>\r\n          <p-calendar [appendTo]=\"'body'\" placeholder='年/月/日' dateFormat=\"yy/mm/dd\"\r\n            [(ngModel)]=\"getListFinalDocRequest.CDateEnd\"\r\n            [maxDate]=\"maxDate\"></p-calendar>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-3 text-right\">\r\n          <button class=\"btn btn-info mr-2\" (click)=\"getList()\"><i class=\"fas fa-search mr-1\"></i>查詢</button>\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"col-md-12\">\r\n          <div class=\"d-flex justify-content-end w-full\">\r\n            <button class=\"btn btn-info\" (click)=\"addNew(dialogUploadFinaldoc)\">\r\n              新增文檔\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-body class=\"bg-white\">\r\n    <div class=\"col-12\">\r\n      <div class=\"table-responsive\">\r\n        <table class=\"table table-striped border \" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr style=\"background-color: #27ae60;\" class=\"d-flex text-white\">\r\n              <th scope=\"col\" class=\"col-5\">文件名稱</th>\r\n              <th scope=\"col\" class=\"col-4\">客戶簽名時間</th>\r\n              <th scope=\"col\" class=\"col-3\">連結</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let data of listFinalDoc; let i = index\" class=\"d-flex\">\r\n              <td class=\"col-5\">{{ data.CDocumentName }}</td>\r\n              <td class=\"col-4\">{{ data.CSignDate }}</td>\r\n              <td class=\"col-3\">\r\n                <button class=\"btn btn-outline-primary btn-sm m-1\" [disabled]=\"!data.CFileAfter && !data.CFileBefore\"\r\n                (click)=\"openPdfInNewTab(data)\">連結</button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <ngx-pagination [CollectionSize]=\"totalRecords\" [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\"\r\n        (PageChange)=\"getList()\">\r\n      </ngx-pagination>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer>\r\n    <div class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm\"  (click)=\"goBack()\">\r\n        返回上一頁\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialogUploadFinaldoc let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:1000px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      戶別管理 > 簽署文件歷程 > {{houseByID.CHousehold}} {{houseByID.CFloor}}F\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group d-flex align-items-baseline\">\r\n        <div class=\"d-flex flex-col col-3\">\r\n          <label for=\"file\" class=\"required-field align-self-start\" style=\"min-width:100px; position: static;\" baseLabel>文件\r\n          </label>\r\n        </div>\r\n        <div class=\"flex flex-col items-start space-y-4\">\r\n          <input type=\"file\" id=\"fileInput\" accept=\"image/jpeg, image/jpg, application/pdf\" class=\"hidden\"\r\n            style=\"display: none\" (change)=\"onFileSelected($event)\">\r\n          <label for=\"fileInput\"\r\n            class=\"cursor-pointer bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\">\r\n            <i class=\"fa-solid fa-cloud-arrow-up mr-2\"></i> 上傳\r\n          </label>\r\n          <div class=\"flex items-center space-x-2\" *ngIf=\"fileName\">\r\n            <span class=\"text-gray-600\">{{ fileName }}</span>\r\n            <button type=\"button\" (click)=\"clearFile()\" class=\"text-red-500 hover:text-red-700\">\r\n              <i class=\"fa-solid fa-trash\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"CDocumentName\" class=\"required-field align-self-start col-3\" style=\"min-width:75px\" baseLabel>\r\n          文件名稱\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"文件名稱\" [(ngModel)]=\"CDocumentName\" />\r\n      </div>\r\n\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"remark\" baseLabel class=\"required-field align-self-start col-3\">送審資訊\r\n          <p style=\"color: red\">內部審核人員查看</p>\r\n        </label>\r\n\r\n        <textarea name=\"remark\" id=\"remark\" rows=\"5\" nbInput style=\"resize: none; max-width: none\" class=\"w-full\"\r\n          [(ngModel)]=\"CApproveRemark\">\r\n        </textarea>\r\n      </div>\r\n\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"CNote\" baseLabel class=\"required-field align-self-start col-3\">摘要註記\r\n          <p style=\"color: red\">客戶於文件中查看</p>\r\n        </label>\r\n        <textarea name=\"CNote\" id=\"CNote\" rows=\"5\" nbInput style=\"resize: none; max-width: none\" class=\"w-full\"\r\n          [(ngModel)]=\"CNote\">\r\n        </textarea>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-outline-secondary m-2\" (click)=\"ref.close()\">取消</button>\r\n      <button class=\"btn btn-success m-2\" (click)=\"onCreateFinalDoc(ref)\">確認送出審核</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n", "\r\nimport { RouterModule, Routes } from \"@angular/router\";\r\nimport { NgModule } from \"@angular/core\";\r\nimport { HouseholdManagementComponent } from \"./household-management.component\";\r\nimport { CustomerChangePictureComponent } from \"./customer-change-picture/customer-change-picture.component\";\r\nimport { SampleSelectionResultComponent } from \"./sample-selection-result/sample-selection-result.component\";\r\nimport { ModifyFloorPlanComponent } from \"./modify-floor-plan/modify-floor-plan.component\";\r\nimport { ModifyHouseholdComponent } from \"./modify-household/modify-household.component\";\r\nimport { ModifyHouseTypeComponent } from \"./modify-house-type/modify-house-type.component\";\r\nimport { StandardHousePlanComponent } from \"./standard-house-plan/standard-house-plan.component\";\r\nimport { FinaldochouseManagementComponent } from \"./finaldochouse-management/finaldochouse-management.component\";\r\n\r\nconst routes: Routes = [\r\n    {\r\n        path: '',\r\n        component: HouseholdManagementComponent,\r\n    },\r\n    {\r\n        path: \"customer-change-picture/:id1/:id2\",\r\n        component: CustomerChangePictureComponent,\r\n    },\r\n    {\r\n        path: \"sample-selection-result/:id1/:id2\",\r\n        component: SampleSelectionResultComponent,\r\n    },\r\n    {\r\n        path: \"modify-floor-plan/:id\",\r\n        component: ModifyFloorPlanComponent\r\n    },\r\n    {\r\n        path: \"modify-household/:id\",\r\n        component: ModifyHouseholdComponent\r\n    },\r\n    {\r\n        path: \"modify-house-type/:id\",\r\n        component: ModifyHouseTypeComponent\r\n    },\r\n    {\r\n        path: \"standard-house-plan/:id\",\r\n        component: StandardHousePlanComponent\r\n    },\r\n    {\r\n        path: \"finaldochouse_management/:id1/:id2\",\r\n        component: FinaldochouseManagementComponent\r\n    }\r\n];\r\n\r\n@NgModule({\r\n    imports: [RouterModule.forChild(routes)],\r\n    exports: [RouterModule],\r\n})\r\nexport class HouseholdRoutingModule { }\r\n", "import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { SharedModule } from '../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { QUOTATION_TEMPLATE } from 'src/assets/template/quotation-template';\r\nimport { NbDatepickerModule, NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, HouseHoldMainService, HouseService } from 'src/services/api/services';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\n// import { TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { AddHouseHoldMain, EditHouseArgs, GetHouseListArgs, GetHouseListRes, TblHouse } from 'src/services/api/models';\r\nimport { concatMap, tap } from 'rxjs';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport * as moment from 'moment';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { EnumHouseProgress } from 'src/app/shared/enum/enumHouseProgress';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\nimport { EnumPayStatus } from 'src/app/shared/enum/enumPayStatus';\r\nimport { EnumSignStatus } from 'src/app/shared/enum/enumSignStatus';\r\nimport { EnumQuotationStatus } from 'src/app/shared/enum/enumQuotationStatus';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\r\nimport { QuotationItem, CQuotationItemType } from 'src/app/models/quotation.model';\r\nimport { QuotationService } from 'src/app/services/quotation.service';\r\n\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n  key?: string\r\n}\r\n\r\n// interface HouseDetailExtension {\r\n//   changeStartDate: string;\r\n//   changeEndDate: string;\r\n// }\r\nexport interface SearchQuery {\r\n  CBuildCaseSelected?: any | null;\r\n  CHouseTypeSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CHouseHoldSelected?: any | null;\r\n  CPayStatusSelected?: any | null;\r\n  CProgressSelected?: any | null;\r\n  CSignStatusSelected?: any | null;\r\n  CQuotationStatusSelected?: any | null;\r\n  CIsEnableSeleted?: any | null;\r\n  CFrom?: any | null;\r\n  CTo?: any | null;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-household-management',\r\n  templateUrl: './household-management.component.html',\r\n  styleUrls: ['./household-management.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbDatepickerModule, NbDateFnsDateModule,],\r\n})\r\n\r\nexport class HouseholdManagementComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseID: number = -1\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _houseHoldMainService: HouseHoldMainService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private _eventService: EventService,\r\n    private _ultilityService: UtilityService,\r\n    private quotationService: QuotationService\r\n  ) {\r\n    super(_allow)\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action == EEvent.GET_BUILDCASE && !!res.payload) {\r\n          this.tempBuildCaseID = res.payload\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  statusOptions: selectItem[] = [\r\n    {\r\n      value: 0,\r\n      key: 'allow',\r\n      label: '允許',\r\n    },\r\n    {\r\n      value: 1,\r\n      key: 'not allowed',\r\n      label: '不允許',\r\n    }\r\n  ]\r\n\r\n  cIsEnableOptions = [\r\n    {\r\n      value: null,\r\n      key: 'all',\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: true,\r\n      key: 'enable',\r\n      label: '啟用',\r\n    },\r\n    {\r\n      value: false,\r\n      key: 'deactivate',\r\n      label: '停用',\r\n    }\r\n  ]\r\n\r\n  searchQuery: SearchQuery\r\n  detailSelected: SearchQuery\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n  houseHoldOptions: any[] = [{ label: '全部', value: '' }]\r\n  progressOptions: any[] = [{ label: '全部', value: -1 }]\r\n  houseTypeOptions: any[] = [{ label: '全部', value: -1 }]\r\n  payStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n  signStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n  quotationStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n\r\n  options = {\r\n    progressOptions: this.enumHelper.getEnumOptions(EnumHouseProgress),\r\n    payStatusOptions: this.enumHelper.getEnumOptions(EnumPayStatus),\r\n    houseTypeOptions: this.enumHelper.getEnumOptions(EnumHouseType),\r\n    quotationStatusOptions: this.enumHelper.getEnumOptions(EnumQuotationStatus),\r\n  }\r\n\r\n  userBuildCaseOptions: any\r\n  initDetail = {\r\n    CHouseID: 0,\r\n    CMail: \"\",\r\n    CIsChange: false,\r\n    CPayStatus: 0,\r\n    CIsEnable: false,\r\n    CCustomerName: \"\",\r\n    CNationalID: \"\",\r\n    CProgress: \"\",\r\n    CHouseType: 0,\r\n    CHouseHold: \"\",\r\n    CPhone: \"\"\r\n  }\r\n  // 報價單相關\r\n  quotationItems: QuotationItem[] = [];\r\n  totalAmount: number = 0;\r\n  // 新增：百分比費用設定\r\n  additionalFeeName: string = '營業稅';  // 固定名稱\r\n  additionalFeePercentage: number = 5;   // 固定5%\r\n  additionalFeeAmount: number = 0;       // 百分比費用金額\r\n  finalTotalAmount: number = 0;          // 最終總金額（含百分比費用）\r\n  enableAdditionalFee: boolean = true;   // 固定啟用營業稅\r\n  currentHouse: any = null;\r\n  currentQuotationId: number = 0;\r\n  isQuotationEditable: boolean = true; // 報價單是否可編輯\r\n\r\n  override ngOnInit(): void {\r\n    this.progressOptions = [\r\n      ...this.progressOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumHouseProgress)\r\n    ]\r\n    this.houseTypeOptions = [\r\n      ...this.houseTypeOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumHouseType)\r\n    ]\r\n    this.payStatusOptions = [\r\n      ...this.payStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumPayStatus)\r\n    ]\r\n    this.signStatusOptions = [\r\n      ...this.signStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumSignStatus)\r\n    ]\r\n    this.quotationStatusOptions = [\r\n      ...this.quotationStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumQuotationStatus)\r\n    ]\r\n\r\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n      this.searchQuery = {\r\n        CBuildCaseSelected: null,\r\n        // CBuildingNameSelected: previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined\r\n        //   ? this.buildingSelectedOptions.find(x => x.value == previous_search.CBuildingNameSelected.value)\r\n        //   : this.buildingSelectedOptions[0],\r\n        CHouseHoldSelected: previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined\r\n          ? this.houseHoldOptions.find(x => x.value == previous_search.CHouseHoldSelected.value)\r\n          : this.houseHoldOptions[0],\r\n        CHouseTypeSelected: previous_search.CHouseTypeSelected != null && previous_search.CHouseTypeSelected != undefined\r\n          ? this.houseTypeOptions.find(x => x.value == previous_search.CHouseTypeSelected.value)\r\n          : this.houseTypeOptions[0],\r\n        CPayStatusSelected: previous_search.CPayStatusSelected != null && previous_search.CPayStatusSelected != undefined\r\n          ? this.payStatusOptions.find(x => x.value == previous_search.CPayStatusSelected.value)\r\n          : this.payStatusOptions[0],\r\n        CProgressSelected: previous_search.CProgressSelected != null && previous_search.CProgressSelected != undefined\r\n          ? this.progressOptions.find(x => x.value == previous_search.CProgressSelected.value)\r\n          : this.progressOptions[0],\r\n        CSignStatusSelected: previous_search.CSignStatusSelected != null && previous_search.CSignStatusSelected != undefined\r\n          ? this.signStatusOptions.find(x => x.value == previous_search.CSignStatusSelected.value)\r\n          : this.signStatusOptions[0],\r\n        CQuotationStatusSelected: previous_search.CQuotationStatusSelected != null && previous_search.CQuotationStatusSelected != undefined\r\n          ? this.quotationStatusOptions.find(x => x.value == previous_search.CQuotationStatusSelected.value)\r\n          : this.quotationStatusOptions[0],\r\n        CIsEnableSeleted: previous_search.CIsEnableSeleted != null && previous_search.CIsEnableSeleted != undefined\r\n          ? this.cIsEnableOptions.find(x => x.value == previous_search.CIsEnableSeleted.value)\r\n          : this.cIsEnableOptions[0],\r\n        CFrom: previous_search.CFrom != null && previous_search.CFrom != undefined\r\n          ? previous_search.CFrom\r\n          : '',\r\n        CTo: previous_search.CTo != null && previous_search.CTo != undefined\r\n          ? previous_search.CTo\r\n          : ''\r\n      }\r\n    }\r\n    else {\r\n      this.searchQuery = {\r\n        CBuildCaseSelected: null,\r\n        // CBuildingNameSelected: this.buildingSelectedOptions[0],\r\n        CHouseHoldSelected: this.houseHoldOptions[0],\r\n        CHouseTypeSelected: this.houseTypeOptions[0],\r\n        CPayStatusSelected: this.payStatusOptions[0],\r\n        CProgressSelected: this.progressOptions[0],\r\n        CSignStatusSelected: this.signStatusOptions[0],\r\n        CQuotationStatusSelected: this.quotationStatusOptions[0],\r\n        CIsEnableSeleted: this.cIsEnableOptions[0],\r\n        CFrom: '',\r\n        CTo: ''\r\n      }\r\n    }\r\n    this.getListBuildCase()\r\n  }\r\n\r\n  onSearch() {\r\n    let sessionSave = {\r\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\r\n      // CBuildingNameSelected: this.searchQuery.CBuildingNameSelected,\r\n      CFrom: this.searchQuery.CFrom,\r\n      CTo: this.searchQuery.CTo,\r\n      CHouseHoldSelected: this.searchQuery.CHouseHoldSelected,\r\n      CHouseTypeSelected: this.searchQuery.CHouseTypeSelected,\r\n      CIsEnableSeleted: this.searchQuery.CIsEnableSeleted,\r\n      CPayStatusSelected: this.searchQuery.CPayStatusSelected,\r\n      CProgressSelected: this.searchQuery.CProgressSelected,\r\n      CSignStatusSelected: this.searchQuery.CSignStatusSelected,\r\n      CQuotationStatusSelected: this.searchQuery.CQuotationStatusSelected\r\n    }\r\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.HOUSE_SEARCH, JSON.stringify(sessionSave));\r\n    this.getHouseList().subscribe()\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getHouseList().subscribe()\r\n  }\r\n\r\n  exportHouse() {\r\n    if (this.searchQuery.CBuildCaseSelected.cID) {\r\n      this._houseService.apiHouseExportHousePost$Json({\r\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\r\n      }).subscribe(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this._ultilityService.downloadExcelFile(\r\n            res.Entries, '戶別資訊範本'\r\n          )\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  selectedFile: File | null = null;\r\n  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;\r\n\r\n\r\n  triggerFileInput(): void {\r\n    this.fileInput.nativeElement.click();\r\n  }\r\n\r\n  onFileSelected(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files && input.files.length > 0) {\r\n      this.selectedFile = input.files[0];\r\n      this.importExcel();\r\n    }\r\n  }\r\n\r\n  importExcel(): void {\r\n    if (this.selectedFile) {\r\n      const formData = new FormData();\r\n      formData.append('CFile', this.selectedFile);\r\n      this._houseService.apiHouseImportHousePost$Json({\r\n        body: {\r\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\r\n          CFile: this.selectedFile\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(res.Message!);\r\n          this.getHouseList().subscribe()\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n  getListHouseHold() {\r\n    this._houseService.apiHouseGetListHouseHoldPost$Json({\r\n      body: { CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseHoldOptions = [{\r\n          value: '', label: '全部'\r\n        }, ...res.Entries.map(e => {\r\n          return { value: e, label: e }\r\n        })]\r\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n          && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n          && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n          if (previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined) {\r\n            let index = this.houseHoldOptions.findIndex((x: any) => x.value == previous_search.CHouseHoldSelected.value)\r\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[index]\r\n          } else {\r\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0]\r\n          }\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  houseList: any\r\n  bodyRequest: GetHouseListArgs\r\n\r\n  formatQuery() {\r\n    this.bodyRequest = {\r\n      CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    }\r\n    if (this.searchQuery.CFrom && this.searchQuery.CTo) {\r\n      this.bodyRequest['CFloor'] = { CFrom: this.searchQuery.CFrom, CTo: this.searchQuery.CTo }\r\n    }\r\n    if (this.searchQuery.CHouseHoldSelected) {\r\n      this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value\r\n    }\r\n    if (this.searchQuery.CHouseTypeSelected.value) {\r\n      this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value\r\n    }\r\n    if (typeof this.searchQuery.CIsEnableSeleted.value === \"boolean\") {\r\n      this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value\r\n    }\r\n    if (this.searchQuery.CPayStatusSelected.value) {\r\n      this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value\r\n    }\r\n    if (this.searchQuery.CProgressSelected.value) {\r\n      this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value\r\n    }\r\n    if (this.searchQuery.CSignStatusSelected.value) {\r\n      this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value\r\n    }\r\n    if (this.searchQuery.CQuotationStatusSelected.value) {\r\n      this.bodyRequest['CQuotationStatus'] = this.searchQuery.CQuotationStatusSelected.value\r\n    }\r\n\r\n    return this.bodyRequest\r\n  }\r\n\r\n  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {\r\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\r\n  }\r\n\r\n  getHouseList() {\r\n    return this._houseService.apiHouseGetHouseListPost$Json({\r\n      body: this.formatQuery()\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseList = res.Entries;\r\n          this.totalRecords = res.TotalItems!;\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n\r\n  userBuildCaseSelected: any\r\n  onSelectionChangeBuildCase() {\r\n    // this.getListBuilding()\r\n    this.getListHouseHold()\r\n    this.getHouseList().subscribe()\r\n  }\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\r\n      body: {\r\n        CIsPagi: false,\r\n        CStatus: 1,\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries?.length ? res.Entries.map(res => {\r\n            return {\r\n              CBuildCaseName: res.CBuildCaseName,\r\n              cID: res.cID\r\n            }\r\n          }) : []\r\n\r\n          if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n            let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n            if (previous_search.CBuildCaseSelected != null && previous_search.CBuildCaseSelected != undefined) {\r\n              let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == previous_search.CBuildCaseSelected.cID)\r\n              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index]\r\n            } else {\r\n              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0]\r\n            }\r\n          }\r\n          else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0]\r\n          }\r\n        }\r\n      }),\r\n      tap(() => {\r\n        // this.getListBuilding()\r\n        this.getListHouseHold()\r\n        setTimeout(() => {\r\n          this.getHouseList().subscribe();\r\n        }, 500)\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  buildingSelected: any\r\n\r\n  buildingSelectedOptions: any[] = [\r\n    {\r\n      value: '', label: '全部'\r\n    }\r\n  ]\r\n\r\n  // getListBuilding() {\r\n  //   this._houseService.apiHouseGetListBuildingPost$Json({\r\n  //     body: {\r\n  //       CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\r\n  //     }\r\n  //   }).subscribe(res => {\r\n  //     if (res.Entries && res.StatusCode == 0) {\r\n  //       this.buildingSelectedOptions = [{\r\n  //         value: '', label: '全部'\r\n  //       }, ...res.Entries.map(e => {\r\n  //         return { value: e, label: e }\r\n  //       })]\r\n  //       if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n  //         && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n  //         && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n  //         let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n  //         if (previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined) {\r\n  //           let index = this.buildingSelectedOptions.findIndex((x: any) => x.value == previous_search.CBuildingNameSelected.value)\r\n  //           this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[index]\r\n  //         } else {\r\n  //           this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0]\r\n  //         }\r\n  //       }\r\n  //       else {\r\n  //         this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0]\r\n  //       }\r\n  //     }\r\n  //   })\r\n  // }\r\n\r\n  houseDetail: TblHouse & {\r\n    changeStartDate?: any;\r\n    changeEndDate?: any\r\n  }\r\n\r\n  getHouseById(CID: any, ref: any) {\r\n    this.detailSelected = {}\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({\r\n      body: { CHouseID: CID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseDetail = {\r\n          ...res.Entries,\r\n          changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,\r\n          changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined,\r\n        }\r\n\r\n        if (res.Entries.CBuildCaseId) {\r\n          this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId)\r\n        }\r\n        this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus)\r\n        if (res.Entries.CHouseType) {\r\n          this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType)\r\n        } else {\r\n          this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1]\r\n        }\r\n        this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress)\r\n\r\n        if (res.Entries.CBuildCaseId) {\r\n          if (this.houseHoldMain) {\r\n            this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId\r\n          }\r\n        }\r\n        this.dialogService.open(ref)\r\n      }\r\n\r\n    })\r\n  }\r\n\r\n\r\n  findItemInArray(array: any[], key: string, value: any) {\r\n    return array.find(item => item[key] === value);\r\n  }\r\n\r\n\r\n  openModelDetail(ref: any, item: any) {\r\n    this.getHouseById(item.CID, ref)\r\n  }\r\n\r\n  openModel(ref: any) {\r\n    this.houseHoldMain = {\r\n      CBuildingName: '',\r\n      CFloor: undefined,\r\n      CHouseHoldCount: undefined\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  editHouseArgsParam: EditHouseArgs\r\n\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmitDetail(ref: any) {\r\n    this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '',\r\n      this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '',\r\n\r\n      this.editHouseArgsParam = {\r\n        CCustomerName: this.houseDetail.CCustomerName,\r\n        CHouseHold: this.houseDetail.CHousehold,\r\n        CHouseID: this.houseDetail.CId,\r\n        CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,\r\n        CIsChange: this.houseDetail.CIsChange,\r\n        CIsEnable: this.houseDetail.CIsEnable,\r\n        CMail: this.houseDetail.CMail,\r\n        CNationalID: this.houseDetail.CNationalId,\r\n        CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,\r\n        CPhone: this.houseDetail.CPhone,\r\n        CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,\r\n        CChangeStartDate: this.houseDetail.CChangeStartDate,\r\n        CChangeEndDate: this.houseDetail.CChangeEndDate\r\n      }\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._houseService.apiHouseEditHousePost$Json({\r\n      body: this.editHouseArgsParam\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n          ref.close();\r\n        }\r\n      }),\r\n      concatMap(() => this.getHouseList())\r\n    ).subscribe();\r\n  }\r\n\r\n\r\n  onSubmit(ref: any) {\r\n    let bodyReq: EditHouseArgs = {\r\n      CCustomerName: this.houseDetail.CCustomerName,\r\n      CHouseHold: this.houseDetail.CHousehold,\r\n      CHouseID: this.houseDetail.CId,\r\n      CHouseType: this.houseDetail.CHouseType,\r\n      CIsChange: this.houseDetail.CIsChange,\r\n      CIsEnable: this.houseDetail.CIsEnable,\r\n      CMail: this.houseDetail.CMail,\r\n      CNationalID: this.houseDetail.CNationalId,\r\n      CPayStatus: this.houseDetail.CPayStatus,\r\n      CPhone: this.houseDetail.CPhone,\r\n      CProgress: this.houseDetail.CProgress,\r\n    }\r\n    this._houseService.apiHouseEditHousePost$Json({\r\n      body: bodyReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        ref.close();\r\n      }\r\n    })\r\n\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  onNavidateId(type: any, id?: any) {\r\n    const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID\r\n    this.router.navigate([`/pages/household-management/${type}`, idURL])\r\n  }\r\n\r\n  onNavidateBuildCaseIdHouseId(type: any, buildCaseId: any, houseId: any) {\r\n    this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId])\r\n  }\r\n\r\n  resetSecureKey(item: any) {\r\n    if (confirm(\"您想重設密碼嗎？\")) {\r\n      this._houseService.apiHouseResetHouseSecureKeyPost$Json({\r\n        body: item.CID\r\n      }).subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  houseHoldMain: AddHouseHoldMain\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案名稱]', this.houseDetail.CId)\r\n    this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold)\r\n    this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50)\r\n    this.valid.required('[樓層]', this.houseDetail.CFloor)\r\n    this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50)\r\n    // if (this.editHouseArgsParam.CNationalID) {\r\n    //   this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID)\r\n    // }\r\n    this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern)\r\n    this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone)\r\n    this.valid.required('[進度]', this.editHouseArgsParam.CProgress)\r\n    this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value)\r\n    this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value)\r\n    if (this.houseDetail.CChangeStartDate) {\r\n      this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate)\r\n    }\r\n    if (this.houseDetail.CChangeEndDate) {\r\n      this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate)\r\n    }\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '')\r\n  }\r\n\r\n  validationHouseHoldMain() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID)\r\n    this.valid.required('[棟別]', this.houseHoldMain.CBuildingName)\r\n    this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10)\r\n    this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100)\r\n    this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100)\r\n  }\r\n\r\n\r\n  addHouseHoldMain(ref: any) {\r\n    this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID,\r\n      this.validationHouseHoldMain()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({\r\n      body: this.houseHoldMain\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n        }\r\n      }),\r\n      concatMap(() => this.getHouseList())\r\n    ).subscribe();\r\n  }  // 開啟報價單對話框\r\n  async openQuotation(dialog: any, item: any) {\r\n    this.currentHouse = item;\r\n    this.quotationItems = [];\r\n    this.totalAmount = 0;\r\n    this.currentQuotationId = 0; // 重置報價單ID\r\n    this.isQuotationEditable = true; // 預設可編輯\r\n    // 重置百分比費用設定（固定營業稅5%）\r\n    this.additionalFeeName = '營業稅';\r\n    this.additionalFeePercentage = 5;\r\n    this.additionalFeeAmount = 0;\r\n    this.finalTotalAmount = 0;\r\n    this.enableAdditionalFee = true;\r\n\r\n    // 載入現有報價資料\r\n    try {\r\n      const response = await this.quotationService.getQuotationByHouseId(item.CID).toPromise();\r\n\r\n      if (response && response.StatusCode === 0 && response.Entries) {\r\n        // 保存當前的報價單ID\r\n        this.currentQuotationId = response.Entries.CQuotationVersionId || 0;\r\n        // 根據 cQuotationStatus 決定是否可編輯\r\n        if (response.Entries.CQuotationStatus === 2) { // 2: 已報價\r\n          this.isQuotationEditable = false;\r\n        } else {\r\n          this.isQuotationEditable = true;\r\n        }\r\n\r\n        // 載入額外費用設定（固定營業稅5%，不從後端載入）\r\n        this.enableAdditionalFee = true;\r\n        this.additionalFeeName = '營業稅';\r\n        this.additionalFeePercentage = 5;\r\n\r\n        // 檢查 Entries 是否有 Items 陣列\r\n        if (response.Entries.Items && Array.isArray(response.Entries.Items)) {\r\n          // 將 API 回傳的資料轉換為 QuotationItem 格式\r\n          this.quotationItems = response.Entries.Items.map((entry: any) => ({\r\n            cHouseID: response.Entries.CHouseID || item.CID,\r\n            cQuotationID: response.Entries.CQuotationID,\r\n            cItemName: entry.CItemName || '',\r\n            cUnit: entry.CUnit || '',\r\n            cUnitPrice: entry.CUnitPrice || 0,\r\n            cCount: entry.CCount || 1,\r\n            cStatus: entry.CStatus || 1,\r\n            CQuotationItemType: entry.CQuotationItemType && entry.CQuotationItemType > 0 ? entry.CQuotationItemType : CQuotationItemType.自定義,\r\n            cRemark: entry.CRemark || '',\r\n            cQuotationStatus: entry.CQuotationStatus\r\n          }));\r\n          this.calculateTotal();\r\n        } else {\r\n\r\n        }\r\n      } else {\r\n\r\n      }\r\n    } catch (error) {\r\n      console.error('載入報價資料失敗:', error);\r\n    }\r\n\r\n    this.dialogService.open(dialog, {\r\n      context: item,\r\n      closeOnBackdropClick: false\r\n    });\r\n  }\r\n\r\n  // 產生新報價單\r\n  createNewQuotation() {\r\n    this.currentQuotationId = 0;\r\n    this.quotationItems = [];\r\n    this.isQuotationEditable = true;\r\n    this.totalAmount = 0;\r\n    this.finalTotalAmount = 0;\r\n    this.additionalFeeAmount = 0;\r\n    this.enableAdditionalFee = true;\r\n\r\n    // 顯示成功訊息\r\n    this.message.showSucessMSG('已產生新報價單，可開始編輯');\r\n  }\r\n  // 新增自定義報價項目\r\n  addQuotationItem() {\r\n    this.quotationItems.push({\r\n      cHouseID: this.currentHouse?.CID || 0,\r\n      cItemName: '',\r\n      cUnit: '',\r\n      cUnitPrice: 0,\r\n      cCount: 1,\r\n      cStatus: 1,\r\n      CQuotationItemType: CQuotationItemType.自定義,\r\n      cRemark: ''\r\n    });\r\n  }\r\n  // 載入客變需求\r\n  async loadDefaultItems() {\r\n    try {\r\n      if (!this.currentHouse?.CID) {\r\n        this.message.showErrorMSG('請先選擇戶別');\r\n        return;\r\n      }\r\n\r\n      const request = {\r\n        CBuildCaseID: this.searchQuery?.CBuildCaseSelected?.cID || 0,\r\n        CHouseID: this.currentHouse.CID\r\n      };\r\n\r\n      const response = await this.quotationService.loadDefaultItems(request).toPromise();\r\n      if (response?.success && response.data) {\r\n        const defaultItems = response.data.map((x: any) => ({\r\n          cQuotationID: x.CQuotationID,\r\n          cHouseID: this.currentHouse?.CID,\r\n          cItemName: x.CItemName,\r\n          cUnit: x.CUnit || '',\r\n          cUnitPrice: x.CUnitPrice,\r\n          cCount: x.CCount,\r\n          cStatus: x.CStatus,\r\n          CQuotationItemType: CQuotationItemType.客變需求,\r\n          cRemark: x.CRemark\r\n        }));\r\n        this.quotationItems.push(...defaultItems);\r\n        this.calculateTotal();\r\n        this.message.showSucessMSG('載入客變需求成功');\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '載入客變需求失敗');\r\n      }\r\n    } catch (error) {\r\n      console.error('載入客變需求錯誤:', error);\r\n      this.message.showErrorMSG('載入客變需求失敗');\r\n    }\r\n  }\r\n\r\n  // 載入選樣資料\r\n  async loadRegularItems() {\r\n    try {\r\n      if (!this.currentHouse?.CID) {\r\n        this.message.showErrorMSG('請先選擇戶別');\r\n        return;\r\n      }\r\n\r\n      const request = {\r\n        CBuildCaseID: this.searchQuery?.CBuildCaseSelected?.cID || 0,\r\n        CHouseID: this.currentHouse.CID\r\n      };\r\n\r\n      const response = await this.quotationService.loadRegularItems(request).toPromise();\r\n      if (response?.success && response.data) {\r\n        const regularItems = response.data.map((x: any) => ({\r\n          cQuotationID: x.CQuotationID,\r\n          cHouseID: this.currentHouse?.CID,\r\n          cItemName: x.CItemName,\r\n          cUnit: x.CUnit || '',\r\n          cUnitPrice: x.CUnitPrice,\r\n          cCount: x.CCount,\r\n          cStatus: x.CStatus,\r\n          CQuotationItemType: CQuotationItemType.選樣, // 選樣資料\r\n          cRemark: x.CRemark || ''\r\n        }));\r\n        this.quotationItems.push(...regularItems);\r\n        this.calculateTotal();\r\n        this.message.showSucessMSG('載入選樣資料成功');\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '載入選樣資料失敗');\r\n      }\r\n    } catch (error) {\r\n      console.error('載入選樣資料錯誤:', error);\r\n      this.message.showErrorMSG('載入選樣資料失敗');\r\n    }\r\n  }\r\n\r\n  // 移除報價項目\r\n  removeQuotationItem(index: number) {\r\n    const item = this.quotationItems[index];\r\n    this.quotationItems.splice(index, 1);\r\n    this.calculateTotal();\r\n  }\r\n\r\n  // 計算總金額\r\n  calculateTotal() {\r\n    this.totalAmount = this.quotationItems.reduce((sum, item) => {\r\n      return sum + (item.cUnitPrice * item.cCount);\r\n    }, 0);\r\n    this.calculateFinalTotal();\r\n  }\r\n\r\n  // 計算百分比費用和最終總金額（固定營業稅5%）\r\n  calculateFinalTotal() {\r\n    // 固定計算營業稅5%\r\n    this.additionalFeeAmount = Math.round(this.totalAmount * 0.05);\r\n    this.finalTotalAmount = this.totalAmount + this.additionalFeeAmount;\r\n  }\r\n\r\n  // 格式化金額\r\n  formatCurrency(amount: number): string {\r\n    return new Intl.NumberFormat('zh-TW', {\r\n      style: 'currency',\r\n      currency: 'TWD',\r\n      minimumFractionDigits: 0\r\n    }).format(amount);\r\n  }\r\n\r\n\r\n\r\n  // 儲存報價單\r\n  async saveQuotation(ref: any) {\r\n    if (this.quotationItems.length === 0) {\r\n      this.message.showErrorMSG('請先新增報價項目');\r\n      return;\r\n    }\r\n\r\n    // 驗證必填欄位 (調整：允許單價和數量為負數)\r\n    const invalidItems = this.quotationItems.filter(item =>\r\n      !item.cItemName.trim()\r\n    );\r\n\r\n    if (invalidItems.length > 0) {\r\n      this.message.showErrorMSG('請確認所有項目名稱都已正確填寫');\r\n      return;\r\n    } try {\r\n      const request = {\r\n        houseId: this.currentHouse.CID,\r\n        items: this.quotationItems,\r\n        quotationId: this.currentQuotationId, // 傳遞當前的報價單ID\r\n        // 額外費用相關欄位\r\n        cShowOther: this.enableAdditionalFee, // 啟用額外費用\r\n        cOtherName: this.additionalFeeName,   // 額外費用名稱\r\n        cOtherPercent: this.additionalFeePercentage // 額外費用百分比\r\n      };\r\n\r\n      const response = await this.quotationService.saveQuotation(request).toPromise();\r\n      if (response?.success) {\r\n        this.message.showSucessMSG('報價單儲存成功');\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '儲存失敗');\r\n      }\r\n    } catch (error) {\r\n      this.message.showErrorMSG('報價單儲存失敗');\r\n    }\r\n  }\r\n\r\n  // 匯出報價單\r\n  async exportQuotation() {\r\n    try {\r\n      const blob: Blob | undefined = await this.quotationService.exportQuotation(this.currentHouse.CID).toPromise();\r\n      if (blob) {\r\n        const url = window.URL.createObjectURL(blob);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.download = `報價單_${this.currentHouse.CHouseHold}_${this.currentHouse.CFloor}樓.pdf`;\r\n        link.click();\r\n        window.URL.revokeObjectURL(url);\r\n      } else {\r\n        this.message.showErrorMSG('匯出報價單失敗：未收到檔案資料');\r\n      }\r\n    } catch (error) {\r\n      this.message.showErrorMSG('匯出報價單失敗');\r\n    }\r\n  }\r\n\r\n  // 列印報價單\r\n  printQuotation() {\r\n    if (this.quotationItems.length === 0) {\r\n      this.message.showErrorMSG('沒有可列印的報價項目');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // 建立列印內容\r\n      const printContent = this.generatePrintContent();\r\n\r\n      // 建立新的視窗進行列印\r\n      const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');\r\n      if (printWindow) {\r\n        printWindow.document.open();\r\n        printWindow.document.write(printContent);\r\n        printWindow.document.close();\r\n\r\n        // 等待內容載入完成後列印\r\n        printWindow.onload = function () {\r\n          setTimeout(() => {\r\n            printWindow.print();\r\n            // 列印後不自動關閉視窗，讓使用者可以預覽\r\n          }, 500);\r\n        };\r\n      } else {\r\n        this.message.showErrorMSG('無法開啟列印視窗，請檢查瀏覽器是否阻擋彈出視窗');\r\n      }\r\n    } catch (error) {\r\n      console.error('列印報價單錯誤:', error);\r\n      this.message.showErrorMSG('列印報價單時發生錯誤');\r\n    }\r\n  }\r\n\r\n  // 產生列印內容\r\n  private generatePrintContent(): string {\r\n    // 使用導入的模板\r\n    const template = QUOTATION_TEMPLATE;\r\n\r\n    // 準備數據\r\n    const currentDate = new Date().toLocaleDateString('zh-TW');\r\n    const buildCaseName = this.searchQuery.CBuildCaseSelected?.CBuildCaseName || '';\r\n\r\n    // 生成項目HTML\r\n    let itemsHtml = '';\r\n    this.quotationItems.forEach((item, index) => {\r\n      const subtotal = item.cUnitPrice * item.cCount;\r\n      const quotationType = this.getQuotationTypeText(item.CQuotationItemType);\r\n      const unit = item.cUnit || '';\r\n      itemsHtml += `\r\n          <tr>\r\n            <td class=\"text-center\">${index + 1}</td>\r\n            <td>${item.cItemName}</td>\r\n            <td class=\"text-right\">${this.formatCurrency(item.cUnitPrice)}</td>\r\n            <td class=\"text-center\">${unit}</td>\r\n            <td class=\"text-center\">${item.cCount}</td>\r\n            <td class=\"text-right\">${this.formatCurrency(subtotal)}</td>\r\n            <td class=\"text-center\">${quotationType}</td>\r\n          </tr>\r\n        `;\r\n    });\r\n\r\n    // 生成額外費用HTML\r\n    const additionalFeeHtml = this.enableAdditionalFee ? `\r\n        <div class=\"additional-fee\">\r\n          ${this.additionalFeeName} (${this.additionalFeePercentage}%)：${this.formatCurrency(this.additionalFeeAmount)}\r\n        </div>\r\n      ` : '';\r\n\r\n    // 替換模板中的占位符\r\n    const html = template\r\n      .replace(/{{buildCaseName}}/g, buildCaseName)\r\n      .replace(/{{houseHold}}/g, this.currentHouse?.CHouseHold || '')\r\n      .replace(/{{floor}}/g, this.currentHouse?.CFloor || '')\r\n      .replace(/{{customerName}}/g, this.currentHouse?.CCustomerName || '')\r\n      .replace(/{{printDate}}/g, currentDate)\r\n      .replace(/{{itemsHtml}}/g, itemsHtml)\r\n      .replace(/{{subtotalAmount}}/g, this.formatCurrency(this.totalAmount))\r\n      .replace(/{{additionalFeeHtml}}/g, additionalFeeHtml)\r\n      .replace(/{{totalAmount}}/g, this.formatCurrency(this.finalTotalAmount))\r\n      .replace(/{{printDateTime}}/g, new Date().toLocaleString('zh-TW'));\r\n\r\n    return html;\r\n  }\r\n\r\n\r\n  // 鎖定報價單\r\n  async lockQuotation(ref: any) {\r\n    if (this.quotationItems.length === 0) {\r\n      this.message.showErrorMSG('請先新增報價項目');\r\n      return;\r\n    }\r\n\r\n    if (!this.currentQuotationId) {\r\n      this.message.showErrorMSG('無效的報價單ID');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await this.quotationService.lockQuotation(this.currentQuotationId).toPromise();\r\n\r\n      if (response.success) {\r\n        this.message.showSucessMSG('報價單已成功鎖定');\r\n        console.log('報價單鎖定成功:', {\r\n          quotationId: this.currentQuotationId,\r\n          message: response.message\r\n        });\r\n      } else {\r\n        this.message.showErrorMSG(response.message || '報價單鎖定失敗');\r\n        console.error('報價單鎖定失敗:', response.message);\r\n      }\r\n\r\n      ref.close();\r\n    } catch (error) {\r\n      this.message.showErrorMSG('報價單鎖定失敗');\r\n      console.error('鎖定報價單錯誤:', error);\r\n    }\r\n  }\r\n\r\n  // 取得報價類型文字\r\n  getQuotationTypeText(quotationType: CQuotationItemType): string {\r\n    switch (quotationType) {\r\n      case CQuotationItemType.客變需求:\r\n        return '客變需求';\r\n      case CQuotationItemType.自定義:\r\n        return '自定義';\r\n      case CQuotationItemType.選樣:\r\n        return '選樣';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n\r\n  getQuotationStatusText(status: number): string {\r\n    switch (status) {\r\n      case EnumQuotationStatus.待報價:\r\n        return '待報價';\r\n      case EnumQuotationStatus.已報價:\r\n        return '已報價';\r\n      case EnumQuotationStatus.已簽回:\r\n        return '已簽回';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此管理各戶別內之相關資訊，包含基本資料、繳款狀況、上傳客變圖面、檢視客變結果等等。 </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"searchQuery.CBuildCaseSelected\" class=\"col-9\"\r\n            (selectedChange)=\"onSelectionChangeBuildCase()\">\r\n            <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cHouseType\" class=\"label col-3\">類型</label>\r\n          <nb-select [(ngModel)]=\"searchQuery.CHouseTypeSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of houseTypeOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"cFloorFrom\" class=\"label col-3\">樓\r\n          </label>\r\n          <nb-form-field class=\"ml-3\">\r\n            <input type=\"text\" id=\"CFrom\" nbInput class=\"w-full col-4\" [(ngModel)]=\"searchQuery.CFrom\">\r\n          </nb-form-field>\r\n          <label for=\"cFloorTo\" class=\"label col-1\">~\r\n          </label>\r\n          <nb-form-field class=\"mr-3\">\r\n            <input type=\"text\" id=\"CTo\" nbInput class=\"w-full\" [(ngModel)]=\"searchQuery.CTo\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <!-- <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">\r\n            棟別\r\n          </label>\r\n          <nb-select [(ngModel)]=\"searchQuery.CBuildingNameSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of buildingSelectedOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div> -->\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cHousehold\" class=\"label col-3\">\r\n            戶型\r\n          </label>\r\n          <nb-select placeholder=\"戶型\" [(ngModel)]=\"searchQuery.CHouseHoldSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of houseHoldOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cPayStatus\" class=\"label col-3\">\r\n            繳款狀態\r\n          </label>\r\n          <nb-select placeholder=\"繳款狀態\" [(ngModel)]=\"searchQuery.CPayStatusSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of payStatusOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cProgress\" class=\"label col-3\">\r\n            進度\r\n          </label>\r\n          <nb-select placeholder=\"進度\" [(ngModel)]=\"searchQuery.CProgressSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of progressOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cStatus\" class=\"label col-3\">\r\n            狀態\r\n          </label>\r\n          <nb-select placeholder=\"狀態\" [(ngModel)]=\"searchQuery.CIsEnableSeleted\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of cIsEnableOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cSignStatus\" class=\"label col-3\">\r\n            簽回狀態\r\n          </label>\r\n          <nb-select placeholder=\"簽回狀態\" [(ngModel)]=\"searchQuery.CSignStatusSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of signStatusOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cQuotationStatus\" class=\"label col-3\">\r\n            報價單狀態\r\n          </label>\r\n          <nb-select placeholder=\"報價單狀態\" [(ngModel)]=\"searchQuery.CQuotationStatusSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of quotationStatusOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <!-- 空白區域，讓查詢按鈕可以放到右下角 -->\r\n      </div>\r\n\r\n      <!-- 查詢按鈕移到這裡，放在搜尋條件的右下角 -->\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-2 mb-3\">\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"onSearch()\">\r\n            查詢 <i class=\"fas fa-search\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-3\">\r\n          <button class=\"btn btn-info mx-1 btn-sm\" *ngIf=\"isCreate\" (click)=\"openModel(dialogHouseholdMain)\">\r\n            批次新增戶別資料\r\n          </button>\r\n          <button class=\"btn btn-info mx-1 btn-sm\" (click)=\"onNavidateId('modify-floor-plan')\">\r\n            修改樓層戶型\r\n          </button>\r\n          <!-- <button class=\"btn btn-info mx-1 btn-sm\" (click)=\"onNavidateId('standard-house-plan')\">\r\n            3.設定戶型標準圖\r\n          </button> -->\r\n          <button class=\"btn btn-info mx-1 btn-sm\" (click)=\"exportHouse()\">\r\n            匯出戶別明細檔\r\n          </button>\r\n          <input type=\"file\" #fileInput style=\"display:none\" (change)=\"onFileSelected($event)\" accept=\".xlsx, .xls\" />\r\n          <button class=\"btn btn-info btn-sm\" (click)=\"triggerFileInput()\">\r\n            匯入更新戶別明細檔\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <!-- <th scope=\"col\" class=\"col-1\">棟別</th> -->\r\n            <th scope=\"col\" class=\"col-1\">戶型</th>\r\n            <th scope=\"col\" class=\"col-1\">樓層</th>\r\n            <th scope=\"col\" class=\"col-1\">戶別</th>\r\n            <th scope=\"col\" class=\"col-1\">類型</th>\r\n            <th scope=\"col\" class=\"col-1\">進度</th>\r\n            <th scope=\"col\" class=\"col-1\">繳款狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">簽回狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">報價單狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">狀態</th>\r\n            <th scope=\"col\" class=\"col-4\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of houseList ; let i = index\">\r\n            <!-- <td>{{ item.CBuildingName}}</td> -->\r\n            <td>{{ item.CHouseHold}}</td>\r\n            <td>{{ item.CFloor}}</td>\r\n            <td>\r\n              {{ item.CHouseType === 2 ? '銷售戶' : ''}}\r\n              {{item.CHouseType === 1 ? '地主戶' : ''}}\r\n            </td>\r\n            <td>{{ item.CIsChange === true ? '客變' : (item.CIsChange === false ? '標準' :'') }}</td>\r\n            <td>{{ item.CProgressName}}</td>\r\n            <td>\r\n              {{item.CPayStatus === 0 ? '未付款': ''}}\r\n              {{item.CPayStatus === 1 ? '已付款': ''}}\r\n              {{item.CPayStatus === 2 ? '無須付款': ''}}\r\n            </td>\r\n            <td>{{ (item.CSignStatus === 0 || item.CSignStatus == null) ? '未簽回' : '已簽回' }}</td>\r\n            <td>{{ getQuotationStatusText(item.CQuotationStatus) }}</td>\r\n            <td>{{ item.CIsEnable ? '啟用' : '停用'}}</td>\r\n            <td class=\"text-center w-32 px-0\">\r\n              <button *ngIf=\"isUpdate\" class=\"btn btn-outline-success btn-sm text-left m-[2px]\"\r\n                (click)=\"openModelDetail(dialogUpdateHousehold, item)\">\r\n                編輯\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\"\r\n                (click)=\"onNavidateBuildCaseIdHouseId('customer-change-picture', searchQuery.CBuildCaseSelected.cID, item.CID )\">\r\n                洽談紀錄\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\"\r\n                (click)=\"onNavidateBuildCaseIdHouseId('sample-selection-result', searchQuery.CBuildCaseSelected.cID, item.CID )\">\r\n                客變確認圖說\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\"\r\n                (click)=\"onNavidateBuildCaseIdHouseId('finaldochouse_management', searchQuery.CBuildCaseSelected.cID, item.CID )\">\r\n                簽署文件歷程\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\" (click)=\"resetSecureKey(item)\">\r\n                重置密碼\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\" (click)=\"openQuotation(dialogQuotation, item)\">\r\n                報價單\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialogUpdateHousehold let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <!-- <nb-card-header>\r\n    </nb-card-header> -->\r\n    <nb-card-body class=\"px-4\" *ngIf=\"houseDetail\">\r\n      <div class=\"form-group\">\r\n        <label for=\"cBuildCaseId\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          建案名稱\r\n        </label>\r\n        <nb-select placeholder=\"建案名稱\" [(ngModel)]=\"detailSelected.CBuildCaseSelected\" class=\"w-full\" disabled=\"true\">\r\n          <nb-option *ngFor=\"let status of userBuildCaseOptions\" [value]=\"status\">\r\n            {{ status.CBuildCaseName }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cHousehold\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          戶型名稱\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"戶型名稱\" [(ngModel)]=\"houseDetail.CHousehold\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cFloor\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          樓層\r\n        </label>\r\n        <input type=\"number\" class=\"w-full\" nbInput placeholder=\"樓層\" [(ngModel)]=\"houseDetail.CFloor\" min=\"1\" max=\"100\"\r\n          disabled=\"true\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cCustomerName\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          客戶姓名\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"客戶姓名\" [(ngModel)]=\"houseDetail.CCustomerName\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cNationalId\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          身分證字號\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"身分證字號\" [(ngModel)]=\"houseDetail.CNationalId\"\r\n          maxlength=\"20\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cMail\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          電子郵件\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"電子郵件\" [(ngModel)]=\"houseDetail.CMail\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"cPhone\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          聯絡電話\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"聯絡電話\" [(ngModel)]=\"houseDetail.CPhone\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cHouseType\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          戶別類型\r\n        </label>\r\n        <nb-select placeholder=\"戶別類型\" [(ngModel)]=\"detailSelected.CHouseTypeSelected\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of options.houseTypeOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n\r\n      <div class=\"form-group\" *ngIf=\"isChangePayStatus\">\r\n        <label for=\"cPayStatus\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          付款狀態\r\n        </label>\r\n        <nb-select placeholder=\"付款狀態\" [(ngModel)]=\"detailSelected.CPayStatusSelected\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of options.payStatusOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n      <div class=\"form-group\" *ngIf=\"isChangeProgress\">\r\n        <label for=\"cProgress\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          進度\r\n        </label>\r\n        <nb-select placeholder=\"進度\" [(ngModel)]=\"detailSelected.CProgressSelected\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of options.progressOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cIsChange\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          是否客變\r\n        </label>\r\n        <nb-checkbox status=\"basic\" [(checked)]=\"houseDetail.CIsChange\">是\r\n        </nb-checkbox>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cIsEnable\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          是否啟用\r\n        </label>\r\n        <nb-checkbox status=\"basic\" [(checked)]=\"houseDetail.CIsEnable\">是\r\n        </nb-checkbox>\r\n      </div>\r\n\r\n      <div class=\"form-group flex flex-row\">\r\n        <label for=\"cIsEnable\" class=\"mr-4 content-center\" style=\"min-width:75px\" baseLabel>\r\n          客變時段\r\n        </label>\r\n        <div class=\"max-w-xs flex flex-row\">\r\n          <nb-form-field class=\"w-1/2\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" id=\"StartDate\" placeholder=\"yyyy-mm-dd\" [nbDatepicker]=\"StartDate\"\r\n              class=\"w-[42%] mr-2\" [(ngModel)]=\"houseDetail.changeStartDate\">\r\n            <nb-datepicker #StartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n          <nb-form-field class=\"w-1/2\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" id=\"EndDate\" placeholder=\"yyyy-mm-dd\" [nbDatepicker]=\"EndDate\"\r\n              class=\"w-[42%] ml-2\" [(ngModel)]=\"houseDetail.changeEndDate\">\r\n            <nb-datepicker #EndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-outline-secondary m-2 px-8\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary m-2 bg-[#169BD5] px-8\" *ngIf=\"isCreate\" (click)=\"onSubmitDetail(ref)\">送出</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialogHouseholdMain let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      戶別管理 》批次新增戶別資料\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group\">\r\n        <label for=\"cBuildingName\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          棟別\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"棟別\" [(ngModel)]=\"houseHoldMain.CBuildingName\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"CHouseHoldCount\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>當層最多戶數\r\n        </label>\r\n        <input type=\"number\" class=\"w-full\" nbInput placeholder=\"當層最多戶數\" [(ngModel)]=\"houseHoldMain.CHouseHoldCount\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"CFloor\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>本棠總樓層\r\n        </label>\r\n        <input type=\"number\" class=\"w-full\" nbInput placeholder=\"本棠總樓層\" [(ngModel)]=\"houseHoldMain.CFloor\" />\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-primary mr-4\" (click)=\"onClose(ref)\">{{ '關閉'}}</button>\r\n      <button class=\"btn btn-primary\" *ngIf=\"isCreate\" (click)=\"addHouseHoldMain(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 報價單對話框 -->\r\n<ng-template #dialogQuotation let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:1200px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      報價單 - {{ currentHouse?.CHouseHold }} ({{ currentHouse?.CFloor }}樓)\r\n    </nb-card-header>\r\n    <nb-card-body>\r\n      <!-- 只有報價單可編輯時才顯示操作按鈕 -->\r\n      <div *ngIf=\"isQuotationEditable\" class=\"mb-4 d-flex justify-content-between\">\r\n        <button class=\"btn btn-info btn-sm\" (click)=\"addQuotationItem()\">\r\n          + 新增自定義項目\r\n        </button>\r\n        <div>\r\n          <button class=\"btn btn-secondary btn-sm me-2\" (click)=\"loadDefaultItems()\">\r\n            載入客變需求\r\n          </button>\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"loadRegularItems()\">\r\n            載入選樣資料\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 報價單已鎖定時的提示 -->\r\n      <div *ngIf=\"!isQuotationEditable\" class=\"mb-4 alert alert-warning\">\r\n        <i class=\"fas fa-lock me-2\"></i>\r\n        <strong>報價單已鎖定</strong> - 此報價單已鎖定，無法進行修改。您可以列印此報價單或產生新的報價單。\r\n      </div>\r\n\r\n      <div class=\"table-responsive\">\r\n        <table class=\"table table-bordered\">\r\n          <thead>\r\n            <tr>\r\n              <th width=\"25%\">項目名稱</th>\r\n              <th width=\"15%\">單價 (元)</th>\r\n              <th width=\"8%\">單位</th>\r\n              <th width=\"8%\">數量</th>\r\n              <th width=\"18%\">小計 (元)</th>\r\n              <th width=\"10%\">類型</th>\r\n              <th width=\"10%\">操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let item of quotationItems; let i = index\">\r\n              <td>\r\n                <input type=\"text\" nbInput [(ngModel)]=\"item.cItemName\"\r\n                  [disabled]=\"!isQuotationEditable || item.CQuotationItemType === 1 || item.CQuotationItemType === 3\"\r\n                  class=\"w-full\"\r\n                  [class.bg-light]=\"!isQuotationEditable || item.CQuotationItemType === 1 || item.CQuotationItemType === 3\">\r\n              </td>\r\n              <td>\r\n                <input type=\"number\" nbInput [(ngModel)]=\"item.cUnitPrice\" (ngModelChange)=\"calculateTotal()\"\r\n                  class=\"w-full\" min=\"0\" step=\"0.01\" [disabled]=\"!isQuotationEditable || item.CQuotationItemType === 3\">\r\n              </td>\r\n              <td>\r\n                <input type=\"text\" nbInput [(ngModel)]=\"item.cUnit\"\r\n                  [disabled]=\"!isQuotationEditable || item.CQuotationItemType === 1 || item.CQuotationItemType === 3\"\r\n                  class=\"w-full\" placeholder=\"單位\"\r\n                  [class.bg-light]=\"!isQuotationEditable || item.CQuotationItemType === 1 || item.CQuotationItemType === 3\">\r\n              </td>\r\n              <td>\r\n                <input type=\"number\" nbInput [(ngModel)]=\"item.cCount\" (ngModelChange)=\"calculateTotal()\" class=\"w-full\"\r\n                  step=\"0.01\" [disabled]=\"!isQuotationEditable || item.CQuotationItemType === 3\">\r\n              </td>\r\n              <td class=\"text-right\">\r\n                {{ formatCurrency(item.cUnitPrice * item.cCount) }}\r\n              </td>\r\n              <td>\r\n                <span class=\"badge\" [class.badge-primary]=\"item.CQuotationItemType === 1\"\r\n                  [class.badge-info]=\"item.CQuotationItemType === 3\"\r\n                  [class.badge-secondary]=\"item.CQuotationItemType !== 1 && item.CQuotationItemType !== 3\">\r\n                  {{ getQuotationTypeText(item.CQuotationItemType) }}\r\n                </span>\r\n              </td>\r\n              <td>\r\n                <button *ngIf=\"isQuotationEditable\" class=\"btn btn-danger btn-sm\" (click)=\"removeQuotationItem(i)\">\r\n                  刪除\r\n                </button>\r\n                <span *ngIf=\"!isQuotationEditable\" class=\"text-muted\">\r\n                  <i class=\"fas fa-lock\"></i>\r\n                </span>\r\n              </td>\r\n            </tr>\r\n            <tr *ngIf=\"quotationItems.length === 0\">\r\n              <td colspan=\"7\" class=\"text-center text-muted py-4\">\r\n                請點擊「新增自定義項目」或「載入客變需求」開始建立報價單\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      <!-- 金額計算區塊 -->\r\n      <div class=\"mt-4\">\r\n        <div class=\"card border-0 shadow-sm\">\r\n          <div class=\"card-body p-4\">\r\n            <!-- 小計 -->\r\n            <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n              <span class=\"h6 mb-0 text-muted\">小計</span>\r\n              <span class=\"h5 mb-0 text-dark fw-bold\">{{ formatCurrency(totalAmount) }}</span>\r\n            </div>\r\n\r\n            <!-- 營業稅 -->\r\n            <div class=\"tax-section d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded\">\r\n              <div class=\"d-flex align-items-center\">\r\n                <div class=\"tax-icon-wrapper me-3\">\r\n                  <i class=\"fas fa-receipt text-info\"></i>\r\n                </div>\r\n                <div>\r\n                  <span class=\"fw-medium text-dark\">營業稅</span>\r\n                  <span class=\"tax-percentage ms-1 badge bg-info text-white\">5%</span>\r\n                  <div class=\"small text-muted mt-1\">\r\n                    <i class=\"fas fa-info-circle me-1\"></i>\r\n                    固定為小計金額的5%\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"text-end\">\r\n                <div class=\"tax-amount h6 mb-0 text-info fw-bold\">{{ formatCurrency(additionalFeeAmount) }}</div>\r\n                <div class=\"small text-muted\">含稅金額</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 分隔線 -->\r\n            <hr class=\"my-3\">\r\n\r\n            <!-- 總金額 -->\r\n            <div class=\"d-flex justify-content-between align-items-center\">\r\n              <span class=\"h5 mb-0 text-dark fw-bold\">總金額</span>\r\n              <span class=\"h4 mb-0 text-primary fw-bold\">{{ formatCurrency(finalTotalAmount) }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-between\">\r\n      <div>\r\n        <button class=\"btn btn-outline-info btn-sm me-2\" (click)=\"printQuotation()\"\r\n          [disabled]=\"quotationItems.length === 0\" title=\"列印報價單\">\r\n          <i class=\"fas fa-print me-1\"></i> 列印報價單\r\n        </button>\r\n        <!-- 報價單已鎖定時才顯示 -->\r\n        <button *ngIf=\"!isQuotationEditable\" class=\"btn btn-outline-success btn-sm me-2\" (click)=\"createNewQuotation()\"\r\n          title=\"產生新報價單\">\r\n          <i class=\"fas fa-plus me-1\"></i> 產生新報價單\r\n        </button>\r\n        <!-- <button class=\"btn btn-outline-info btn-sm\" (click)=\"exportQuotation()\"\r\n          [disabled]=\"quotationItems.length === 0\">\r\n          匯出報價單\r\n        </button> -->\r\n      </div>\r\n      <div>\r\n        <button class=\"btn btn-outline-secondary m-2\" (click)=\"onClose(ref)\">取消</button>\r\n        <!-- 只有在報價單可編輯時才顯示鎖定和儲存按鈕 -->\r\n        <button *ngIf=\"isQuotationEditable\" class=\"btn btn-warning m-2\" (click)=\"lockQuotation(ref)\"\r\n          [disabled]=\"quotationItems.length === 0\">\r\n          鎖定報價單\r\n        </button>\r\n        <button *ngIf=\"isQuotationEditable\" class=\"btn btn-primary m-2\" (click)=\"saveQuotation(ref)\"\r\n          [disabled]=\"quotationItems.length === 0\">\r\n          儲存報價單\r\n        </button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n", "import { NgModule } from \"@angular/core\";\r\nimport { CommonModule } from \"@angular/common\";\r\nimport { ThemeModule } from \"src/app/@theme/theme.module\";\r\nimport { NbMenuModule } from \"@nebular/theme\";\r\nimport { SharedModule } from \"../components/shared.module\";\r\nimport { CustomerChangePictureComponent } from \"./customer-change-picture/customer-change-picture.component\";\r\nimport { ModifyFloorPlanComponent } from \"./modify-floor-plan/modify-floor-plan.component\";\r\nimport { ModifyHouseholdComponent } from \"./modify-household/modify-household.component\";\r\nimport { ModifyHouseTypeComponent } from \"./modify-house-type/modify-house-type.component\";\r\nimport { StandardHousePlanComponent } from \"./standard-house-plan/standard-house-plan.component\";\r\nimport { CalendarModule } from \"primeng/calendar\";\r\nimport { HouseholdRoutingModule } from \"./household-management-routing.module\";\r\nimport { SampleSelectionResultComponent } from \"./sample-selection-result/sample-selection-result.component\";\r\nimport { SpecialChangeSourcePipe } from \"../../@theme/pipes/specialChangeSource.pipe\";\r\n\r\n@NgModule({\r\n  declarations: [\r\n    CustomerChangePictureComponent,\r\n    SampleSelectionResultComponent,\r\n    ModifyFloorPlanComponent,\r\n    ModifyHouseholdComponent,\r\n    ModifyHouseTypeComponent,\r\n    StandardHousePlanComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    HouseholdRoutingModule,\r\n    SharedModule,\r\n    ThemeModule,\r\n    CalendarModule,\r\n    NbMenuModule.forRoot(),\r\n    SpecialChangeSourcePipe\r\n  ],\r\n})\r\nexport class HouseholdManagementModule { }\r\n", "import { Component, OnInit } from '@angular/core';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { HouseService } from 'src/services/api/services';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\n// import { GetHouseListRes } from 'src/services/api/models';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { EEvent, EventService } from 'src/app/shared/services/event.service';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\nexport interface GetHouseListRes {\r\n  CBuildingName?: string | null;\r\n  CFloor?: number | null;\r\n  CHouseHold?: string | null;\r\n  CHouseType?: number | null;\r\n  CID?: number;\r\n  CIsChange?: boolean;\r\n  CIsEnable?: boolean | null;\r\n  CIsSelected?: boolean | null;\r\n  CPayStatus?: number;\r\n  CProgress?: number;\r\n  CSignStatus?: number | null;\r\n  CProgressName?: string | null;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-modify-floor-plan',\r\n  templateUrl: './modify-floor-plan.component.html',\r\n  styleUrls: ['./modify-floor-plan.component.scss'],\r\n})\r\n\r\nexport class ModifyFloorPlanComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _houseService: HouseService,\r\n    private route: ActivatedRoute,\r\n    private location: Location,\r\n    private message: MessageService,\r\n    private router: Router,\r\n    private _eventService: EventService\r\n\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  buildCaseId: number\r\n  buildingSelectedOptions: any[] = [{ value: '', label: '全部' }]\r\n\r\n  getListBuilding() {\r\n    this._houseService.apiHouseGetListBuildingPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.buildCaseId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.buildingSelectedOptions = [{\r\n          value: '', label: '全部'\r\n        }, ...res.Entries.map(e => {\r\n          return { value: e, label: e }\r\n        })]\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  houseList: any\r\n\r\n\r\n  clear() {\r\n    this.searchQuery = {\r\n      CFrom: 1,\r\n      CTo: 100,\r\n      CBuildingNameSelected: this.buildingSelectedOptions[0]\r\n    }\r\n  }\r\n\r\n  groupByFloor(customerData: GetHouseListRes[]): GetHouseListRes[][] {\r\n    const groupedData: GetHouseListRes[][] = [];\r\n    // Get all unique floor numbers (handling potential nulls)\r\n    const uniqueFloors = Array.from(new Set(\r\n      customerData.map(customer => customer.CFloor).filter(floor => floor !== null) as number[]\r\n    ));\r\n    // Create an empty array for each unique floor\r\n    for (const floor of uniqueFloors) {\r\n      groupedData.push([]);\r\n    }\r\n    // Place each customer in the correct floor array\r\n    for (const customer of customerData) {\r\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor as number); // Find the index of the customer's floor in the uniqueFloors array\r\n      if (floorIndex !== -1) {\r\n        groupedData[floorIndex].push({ ...customer, CIsSelected: customer.CIsEnable === false ? true : false });\r\n      } // Add customer to the corresponding array in groupedData\r\n    }\r\n    return groupedData;\r\n  }\r\n\r\n  isHouseList = false\r\n\r\n  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {\r\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\r\n  }\r\n\r\n  getHouseList() {\r\n    this.isHouseList = false\r\n    if (this.buildCaseId) {\r\n      this._houseService.apiHouseGetHouseListPost$Json({\r\n        body: {\r\n          CBuildCaseID: this.buildCaseId,\r\n          CBuildingName: this.searchQuery.CBuildingNameSelected.value || null,\r\n          CFloor: {\r\n            CFrom: this.searchQuery.CFrom,\r\n            CTo: this.searchQuery.CTo,\r\n          },\r\n          CIsPagi: false\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            const rest = this.sortByFloorDescending(res.Entries)\r\n            this.houseList = this.groupByFloor(rest)\r\n            this.isHouseList = true\r\n          }\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n\r\n  onSubmit() {\r\n    let bodyParam = this.houseList.flat().map((item: any) => {\r\n      return {\r\n        CIsEnable: !item.CIsSelected,\r\n        CHouseID: item.CID,\r\n      };\r\n    });\r\n\r\n    this._houseService.apiHouseEditListHousePost$Json({\r\n      body: {\r\n        mode: 1,\r\n        Args: bodyParam\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        console.log(res);\r\n        this.getHouseList()\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  isCheckAllRowChecked(row: any[]): boolean {\r\n    return row.every((item: { CIsSelected: any; }) => item.CIsSelected);\r\n  }\r\n\r\n  isCheckAllColumnChecked(index: number): boolean {\r\n    if (this.isHouseList) {\r\n\r\n      if (index < 0 || index >= this.houseList[0].length) {\r\n        throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\r\n      }\r\n      for (const floorData of this.houseList) {\r\n        if (index >= floorData.length || !floorData[index].CIsSelected) {\r\n          return false; // Found a customer with CIsSelected not true (or missing)\r\n        }\r\n      }\r\n      return true; // All customers at the given index have CIsSelected as true\r\n    }\r\n    return false\r\n  }\r\n\r\n  enableAllAtIndex(checked: boolean, index: number): void {\r\n    if (index < 0) {\r\n      throw new Error(\"Invalid index. Index must be a non-negative number.\");\r\n    }\r\n    for (const floorData of this.houseList) {\r\n      if (index < floorData.length) { // Check if index is valid for this floor\r\n        floorData[index].CIsSelected = checked;\r\n      }\r\n    }\r\n  }\r\n\r\n  enableAllRow(checked: boolean, row: any[]) {\r\n    for (const item of row) {\r\n      item.CIsSelected = checked;\r\n    }\r\n  }\r\n\r\n\r\n  searchQuery: any\r\n\r\n  override ngOnInit(): void {\r\n    this.searchQuery = {\r\n      CBuildingNameSelected: this.buildingSelectedOptions[0],\r\n      CFrom: 1,\r\n      CTo: 100\r\n    }\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        this.getListBuilding()\r\n        this.getHouseList()\r\n      }\r\n    });\r\n\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n  }\r\n\r\n\r\n  onOpen(ref: any) {\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  onNavigateWithId(type: any) {\r\n    this.router.navigate([`/pages/household-management/${type}`, this.buildCaseId])\r\n  }\r\n\r\n}\r\n", "<!-- 2.1.3 -->\r\n<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\"></h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-4\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">棟別</label>\r\n          <nb-select placeholder=\"狀態\" [(ngModel)]=\"searchQuery.CBuildingNameSelected\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let building of buildingSelectedOptions\" [value]=\"building\">\r\n              {{ building.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n\r\n      </div>\r\n      <div class=\"col-md-5\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"cFloorFrom\" class=\"label col-3\">樓\r\n          </label>\r\n          <nb-form-field class=\"ml-3\">\r\n            <input type=\"number\" id=\"search\" nbInput class=\"w-full col-4\" [(ngModel)]=\"searchQuery.CFrom\">\r\n          </nb-form-field>\r\n          <label for=\"cFloorTo\" class=\"label col-1\">~\r\n          </label>\r\n          <nb-form-field class=\"mr-3\">\r\n            <input type=\"number\" id=\"search\" nbInput class=\"w-full\" [(ngModel)]=\"searchQuery.CTo\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-3\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-secondary mx-2\" (click)=\"clear()\">\r\n            清除\r\n          </button>\r\n          <button class=\"btn btn-secondary\" (click)=\"getHouseList()\">\r\n            查詢 <i class=\"fas fa-search\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-primary\" (click)=\"onNavigateWithId('modify-floor-plan')\">\r\n            1.調整戶型組成\r\n          </button>\r\n          <button class=\"btn btn-primary mx-2\" (click)=\"onNavigateWithId('modify-household')\">\r\n            2.修改戶型名稱\r\n          </button>\r\n          <button class=\"btn btn-primary\" (click)=\"onNavigateWithId('modify-house-type')\">\r\n            3.設定地主戶\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"table-responsive mt-4\" *ngIf=\"isHouseList\">\r\n      <table class=\"table table-bordered\" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr *ngIf=\"houseList.length\">\r\n            <th></th>\r\n            <th *ngFor=\"let house of houseList[0]; let idx = index;\">\r\n              <div class=\"w-max\">\r\n                <nb-checkbox status=\"basic\" (checkedChange)=\"enableAllAtIndex($event, idx)\"\r\n                  [checked]=\"isCheckAllColumnChecked(idx)\">\r\n                  <span class=\"font-medium\">全選無此戶型 </span>\r\n                </nb-checkbox>\r\n              </div>\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let row of houseList\">\r\n            <td>\r\n              <div class=\"w-max\">\r\n                <p>&nbsp;</p>\r\n                <nb-checkbox status=\"basic\" [checked]=\"isCheckAllRowChecked(row)\"\r\n                  (checkedChange)=\"enableAllRow($event,row)\">\r\n                  <span class=\"font-medium\">全選無此戶型</span>\r\n                </nb-checkbox>\r\n              </div>\r\n            </td>\r\n            <td *ngFor=\"let house of row\">\r\n              <div class=\"w-max\">\r\n                <p class=\"font-bold\">{{ house.CHouseHold || 'null' }} - {{ house.CFloor }}</p>\r\n                <nb-checkbox status=\"basic\" [(checked)]=\"house.CIsSelected\">\r\n                  <span class=\"font-medium\">無此戶型</span>\r\n                </nb-checkbox>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <div class=\"inline\">\r\n      <div class=\"d-flex justify-content-center w-full\">\r\n        <button class=\"btn btn-primary\" (click)=\"goBack()\">\r\n          返回上一頁\r\n        </button>\r\n        <button class=\"btn btn-primary mx-2\" (click)=\"getHouseList()\">\r\n          取消\r\n        </button>\r\n        <button class=\"btn btn-primary\" (click)=\"onSubmit()\">\r\n          儲存\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>", "import { Component, OnInit } from '@angular/core';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { HouseService } from 'src/services/api/services';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { GetHouseListRes } from 'src/services/api/models';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\nexport interface GetHouseListResCus {\r\n  CBuildingName?: string | null;\r\n  CFloor?: number | null;\r\n  CHouseHold?: string | null;\r\n  CHouseType?: number | null;\r\n  CHouseTypeBool?: boolean | null;\r\n  CID?: number;\r\n  CIsChange?: boolean;\r\n  CIsEnable?: boolean | null;\r\n  CPayStatus?: number;\r\n  CProgress?: number;\r\n  CProgressName?: string | null;\r\n  CSignStatus?: number | null;\r\n}\r\n\r\n\r\n@Component({\r\n  selector: 'ngx-modify-house-type',\r\n  templateUrl: './modify-house-type.component.html',\r\n  styleUrls: ['./modify-house-type.component.scss'],\r\n})\r\n\r\nexport class ModifyHouseTypeComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _houseService: HouseService,\r\n    private route: ActivatedRoute,\r\n    private location: Location,\r\n    private message: MessageService,\r\n    private router: Router\r\n\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  buildCaseId: number\r\n  buildingSelectedOptions: any[] = [{ value: '', label: '全部' }]\r\n\r\n  getListBuilding() {\r\n    this._houseService.apiHouseGetListBuildingPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.buildCaseId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.buildingSelectedOptions = [{\r\n          value: '', label: '全部'\r\n        }, ...res.Entries.map(e => {\r\n          return { value: e, label: e }\r\n        })]\r\n      }\r\n    })\r\n  }\r\n\r\n  houseList: any\r\n\r\n  groupByFloor(customerData: GetHouseListRes[]): GetHouseListResCus[][] {\r\n    const groupedData: GetHouseListResCus[][] = [];\r\n    const uniqueFloors = Array.from(new Set(\r\n      customerData.map(customer => customer.CFloor).filter(floor => floor !== null) as number[]\r\n    ));\r\n    for (const floor of uniqueFloors) {\r\n      groupedData.push([]);\r\n    }\r\n    for (const customer of customerData) {\r\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor as number); // Find the index of the customer's floor in the uniqueFloors array\r\n      if (floorIndex !== -1) {\r\n        let custemp: GetHouseListResCus = { ...customer }\r\n        if (customer.CIsEnable) {\r\n          custemp = { ...custemp, CHouseTypeBool: (customer.CHouseType && customer.CHouseType == 1) ? true : false }\r\n        }\r\n        groupedData[floorIndex].push(custemp);\r\n      }\r\n    }\r\n    return groupedData;\r\n  }\r\n\r\n  isHouseList = false\r\n\r\n  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {\r\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\r\n  }\r\n\r\n  getHouseList() {\r\n    this.isHouseList = false\r\n    if (this.buildCaseId) {\r\n      this._houseService.apiHouseGetHouseListPost$Json({\r\n        body: {\r\n          CBuildCaseID: this.buildCaseId,\r\n          CBuildingName: this.searchQuery.CBuildingNameSelected.value || null,\r\n          CFloor: {\r\n            CFrom: this.searchQuery.CFrom,\r\n            CTo: this.searchQuery.CTo,\r\n          },\r\n          CIsPagi: false\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          const rest = this.sortByFloorDescending(res.Entries)\r\n          this.houseList = this.groupByFloor(rest)\r\n          this.isHouseList = true\r\n          console.log('this.houseList', this.houseList);\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n\r\n  goBack() { this.location.back() }\r\n\r\n  onSubmit() {\r\n    let bodyParam = this.houseList.flat().map((item: any) => {\r\n      return {\r\n        CHouseType: item.CHouseTypeBool === true ? 1 : 2,\r\n        CHouseID: item.CID,\r\n        CIsEnable: item.CIsEnable\r\n      };\r\n    });\r\n\r\n    this._houseService.apiHouseEditListHousePost$Json({\r\n      body: {\r\n        mode: 3,\r\n        Args: bodyParam.filter((e: any) => e.CIsEnable)\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getHouseList()\r\n      }\r\n    })\r\n  }\r\n\r\n  isCheckAllRowChecked(row: any[]): boolean {\r\n    let count = 0\r\n    for (let i = 0; i < row.length; i++) {\r\n      const item = row[i];\r\n      if (!item.CHouseTypeBool && item.CIsEnable) {\r\n        return false;\r\n      }\r\n      if (!item.CIsEnable) {\r\n        count = count + 1\r\n      }\r\n    }\r\n    if (count === row.length) {\r\n      return false //If all row are disabled, they will not be checked.\r\n    }\r\n    return true;\r\n  }\r\n\r\n  isCheckAllColumnChecked(index: number): boolean {\r\n    if (this.isHouseList) {\r\n      if (index < 0 || index >= this.houseList[0].length) {\r\n        throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\r\n      }\r\n      let count = 0\r\n\r\n      for (const floorData of this.houseList) {\r\n        if (floorData[index].CIsEnable) {\r\n\r\n          if (index >= floorData.length || !floorData[index].CHouseTypeBool) {\r\n            return false; // Found a customer with CHouseTypeBool not true (or missing)\r\n          }\r\n        } else {\r\n          count = count + 1\r\n        }\r\n        if (count === this.houseList.length) {\r\n          return false //If all columns are disabled, they will not be checked.\r\n        }\r\n      }\r\n      return true; // All customers at the given index have CIsEnable as true\r\n    }\r\n    return false\r\n  }\r\n\r\n  enableAllAtIndex(checked: boolean, index: number): void {\r\n    if (index < 0) {\r\n      throw new Error(\"Invalid index. Index must be a non-negative number.\");\r\n    }\r\n    for (const floorData of this.houseList) {\r\n      if (index < floorData.length) { // Check if index is valid for this floor\r\n        if (floorData[index].CIsEnable) {\r\n          floorData[index].CHouseTypeBool = checked;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  enableAllRow(checked: boolean, row: any[]) {\r\n    for (const item of row) {\r\n      if (item.CIsEnable) {\r\n        item.CHouseTypeBool = checked;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  searchQuery: any\r\n\r\n  override ngOnInit(): void {\r\n    this.searchQuery = {\r\n      CBuildingNameSelected: this.buildingSelectedOptions[0],\r\n      CFrom: 1,\r\n      CTo: 100\r\n    }\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        this.getListBuilding()\r\n        this.getHouseList()\r\n      }\r\n    });\r\n\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n  }\r\n\r\n  clear() {\r\n    this.searchQuery = {\r\n      CFrom: 1,\r\n      CTo: 100,\r\n      CBuildingNameSelected: this.buildingSelectedOptions[0]\r\n    }\r\n  }\r\n\r\n  onOpen(ref: any) {\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  onNavigateWithId(type: any) {\r\n    this.router.navigate([`/pages/household-management/${type}`, this.buildCaseId])\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\"></h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-4\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">棟別</label>\r\n          <nb-select placeholder=\"狀態\" [(ngModel)]=\"searchQuery.CBuildingNameSelected\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let building of buildingSelectedOptions\" [value]=\"building\">\r\n              {{ building.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n\r\n      </div>\r\n      <div class=\"col-md-5\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"cFloorFrom\" class=\"label col-3\">樓\r\n          </label>\r\n          <nb-form-field class=\"ml-3\">\r\n            <input type=\"number\" id=\"search\" nbInput class=\"w-full col-4\" [(ngModel)]=\"searchQuery.CFrom\">\r\n          </nb-form-field>\r\n          <label for=\"cFloorTo\" class=\"label col-1\">~\r\n          </label>\r\n          <nb-form-field class=\"mr-3\">\r\n            <input type=\"number\" id=\"search\" nbInput class=\"w-full\" [(ngModel)]=\"searchQuery.CTo\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-3\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-secondary mx-2\" (click)=\"clear()\">\r\n            清除\r\n          </button>\r\n          <button class=\"btn btn-secondary\" (click)=\"getHouseList()\">\r\n            查詢 <i class=\"fas fa-search\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-primary\" (click)=\"onNavigateWithId('modify-floor-plan')\">\r\n            1.調整戶型組成\r\n          </button>\r\n          <button class=\"btn btn-primary mx-2\" (click)=\"onNavigateWithId('modify-household')\">\r\n            2.修改戶型名稱\r\n          </button>\r\n          <button class=\"btn btn-primary\" (click)=\"onNavigateWithId('modify-house-type')\">\r\n            3.設定地主戶\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"table-responsive mt-4\" *ngIf=\"isHouseList\">\r\n      <table class=\"table table-bordered\" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr *ngIf=\"houseList.length\" class=\"text-center\">\r\n            <th class=\"px-1\"></th>\r\n            <th *ngFor=\"let house of houseList[0]; let idx = index;\" class=\" px-1\">\r\n              <div class=\"w-max\">\r\n                <nb-checkbox status=\"basic\" (checkedChange)=\"enableAllAtIndex($event, idx)\"\r\n                  [checked]=\"isCheckAllColumnChecked(idx)\">\r\n                  <span class=\"font-medium\">全選為地主戶 </span>\r\n                </nb-checkbox>\r\n              </div>\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let row of houseList\" class=\"text-center\">\r\n            <td class=\"px-1\">\r\n              <div class=\"w-max\">\r\n                <p>&nbsp;</p>\r\n                <nb-checkbox status=\"basic\" [checked]=\"isCheckAllRowChecked(row)\"\r\n                  (checkedChange)=\"enableAllRow($event,row)\">\r\n                  <span class=\"font-medium\">全選為地主戶 </span>\r\n                </nb-checkbox>\r\n              </div>\r\n            </td>\r\n            <td *ngFor=\"let house of row\" [ngClass]=\"!house.CIsEnable ? 'bg-slate-400' : ''\">\r\n              <div class=\"w-max\">\r\n                <p class=\"font-bold\">{{ house.CHouseHold || 'null' }} - {{ house.CFloor }}</p>\r\n                <nb-checkbox *ngIf=\"house.CIsEnable\" status=\"basic\" [(checked)]=\"house.CHouseTypeBool\">\r\n                  <span class=\"font-medium\">\r\n                    是地主戶\r\n                  </span>\r\n                </nb-checkbox>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <div class=\"inline\">\r\n      <div class=\"d-flex justify-content-center w-full\">\r\n        <button class=\"btn btn-primary\" (click)=\"goBack()\">\r\n          返回上一頁\r\n        </button>\r\n        <button class=\"btn btn-primary mx-2\" (click)=\"getHouseList()\">\r\n          取消\r\n        </button>\r\n        <button class=\"btn btn-primary\" (click)=\"onSubmit()\">\r\n          儲存\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>", "import { Component, OnInit } from '@angular/core';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { HouseService } from 'src/services/api/services';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { GetHouseListRes } from 'src/services/api/models';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\ninterface HouseListWithEdit extends GetHouseListRes {\r\n  isEdit?: boolean | null;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-modify-household',\r\n  templateUrl: './modify-household.component.html',\r\n  styleUrls: ['./modify-household.component.scss'],\r\n})\r\n\r\nexport class ModifyHouseholdComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _houseService: HouseService,\r\n    private route: ActivatedRoute,\r\n    private location: Location,\r\n    private message: MessageService,\r\n    private router: Router\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  buildCaseId: number\r\n  buildingSelectedOptions: any[] = [{ value: '', label: '全部' }]\r\n\r\n  getListBuilding() {\r\n    this._houseService.apiHouseGetListBuildingPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.buildCaseId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.buildingSelectedOptions = [{\r\n          value: '', label: '全部'\r\n        }, ...res.Entries.map(e => {\r\n          return { value: e, label: e }\r\n        })]\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  houseList: HouseListWithEdit[][]\r\n\r\n  houseListItem: any\r\n\r\n\r\n  groupByFloor(customerData: GetHouseListRes[]): GetHouseListRes[][] {\r\n    const groupedData: GetHouseListRes[][] = [];\r\n    // Get all unique floor numbers (handling potential nulls)\r\n    const uniqueFloors = Array.from(new Set(\r\n      customerData.map(customer => customer.CFloor).filter(floor => floor !== null) as number[]\r\n    ));\r\n    // Create an empty array for each unique floor\r\n    for (const floor of uniqueFloors) {\r\n      groupedData.push([]);\r\n    }\r\n    // Place each customer in the correct floor array\r\n    for (const customer of customerData) {\r\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor as number); // Find the index of the customer's floor in the uniqueFloors array\r\n      if (floorIndex !== -1) {\r\n        groupedData[floorIndex].push(customer);\r\n      } // Add customer to the corresponding array in groupedData\r\n    }\r\n    return groupedData;\r\n  }\r\n\r\n  isHouseList = false\r\n\r\n  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {\r\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\r\n  }\r\n\r\n  getHouseList() {\r\n    this.isHouseList = false\r\n    if (this.buildCaseId) {\r\n      this._houseService.apiHouseGetHouseListPost$Json({\r\n        body: {\r\n          CBuildCaseID: this.buildCaseId,\r\n          CBuildingName: this.searchQuery.CBuildingNameSelected.value || null,\r\n          CFloor: {\r\n            CFrom: this.searchQuery.CFrom,\r\n            CTo: this.searchQuery.CTo,\r\n          },\r\n          CIsPagi: false\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          const rest = this.sortByFloorDescending(res.Entries)\r\n          this.houseList = this.groupByFloor(rest)\r\n          this.houseListItem = this.houseList[0].map((item: GetHouseListRes) => {\r\n            return {\r\n              CHouseHold: \"\",\r\n              isEdit: false\r\n            }\r\n          })\r\n          this.houseList.forEach((element: any) => {\r\n            if (element.CIsEnable) {\r\n              element['isEdit'] = false\r\n            }\r\n          });\r\n          this.isHouseList = true\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  goBack() { this.location.back() }\r\n\r\n  onSubmit() {\r\n    let bodyParam = this.houseList.flat().map((item: any) => {\r\n      return {\r\n        CIsEnable: item.CIsEnable,\r\n        CHouseID: item.CID,\r\n        CHouseHold: item.CHouseHold\r\n      };\r\n    });\r\n\r\n    this._houseService.apiHouseEditListHousePost$Json({\r\n      body: {\r\n        mode: 2,\r\n        Args: bodyParam.filter((e: any) => e.CIsEnable)\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getHouseList()\r\n      }\r\n    })\r\n  }\r\n  currentCHouseHold: any\r\n\r\n  onEditItem(house: any) {\r\n    house.isEdit = !house.isEdit\r\n    this.currentCHouseHold = house.CHouseHold\r\n  }\r\n\r\n  closeEditItem(house: any) {\r\n    house.isEdit = !house.isEdit\r\n    if (this.currentCHouseHold) {\r\n      house.CHouseHold = this.currentCHouseHold\r\n    }\r\n  }\r\n\r\n  onUpdateItem(item: any) {\r\n    this._houseService.apiHouseEditListHousePost$Json({\r\n      body: {\r\n        mode: 2,\r\n        Args: [{\r\n          CIsEnable: item.CIsEnable,\r\n          CHouseID: item.CID,\r\n          CHouseHold: item.CHouseHold\r\n        }]\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getHouseList()\r\n      }\r\n    })\r\n  }\r\n\r\n  clearForm(house: any) {\r\n    house.isEdit = !house.isEdit\r\n    house.CHouseHold = ''\r\n  }\r\n\r\n  onUpdateAllCol(item: any, index: number) {\r\n    if (item.CHouseHold === '') return\r\n    let param: any[] = []\r\n    this.houseList.forEach((element: any) => {\r\n      if (element[index].CIsEnable) {\r\n        param.push({\r\n          CHouseHold: item.CHouseHold,\r\n          CHouseID: element[index].CID,\r\n          CIsEnable: element[index].CIsEnable\r\n        })\r\n      }\r\n    });\r\n\r\n    this._houseService.apiHouseEditListHousePost$Json({\r\n      body: {\r\n        mode: 2,\r\n        Args: param\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getHouseList()\r\n      }\r\n    })\r\n  }\r\n\r\n  searchQuery: any\r\n\r\n  override ngOnInit(): void {\r\n    this.searchQuery = {\r\n      CBuildingNameSelected: this.buildingSelectedOptions[0],\r\n      CFrom: 1,\r\n      CTo: 100\r\n    }\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        this.getListBuilding()\r\n        this.getHouseList()\r\n      }\r\n    });\r\n\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n  }\r\n\r\n\r\n  onOpen(ref: any) {\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  clear() {\r\n    this.searchQuery = {\r\n      CFrom: 1,\r\n      CTo: 100,\r\n      CBuildingNameSelected: this.buildingSelectedOptions[0]\r\n    }\r\n  }\r\n\r\n  onNavigateWithId(type: any) {\r\n    this.router.navigate([`/pages/household-management/${type}`, this.buildCaseId])\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\"></h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-4\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">棟別</label>\r\n          <nb-select placeholder=\"狀態\" [(ngModel)]=\"searchQuery.CBuildingNameSelected\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let building of buildingSelectedOptions\" [value]=\"building\">\r\n              {{ building.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n\r\n      </div>\r\n      <div class=\"col-md-5\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"cFloorFrom\" class=\"label col-3\">樓\r\n          </label>\r\n          <nb-form-field class=\"ml-3\">\r\n            <input type=\"number\" id=\"search\" nbInput class=\"w-full col-4\" [(ngModel)]=\"searchQuery.CFrom\">\r\n          </nb-form-field>\r\n          <label for=\"cFloorTo\" class=\"label col-1\">~\r\n          </label>\r\n          <nb-form-field class=\"mr-3\">\r\n            <input type=\"number\" id=\"search\" nbInput class=\"w-full\" [(ngModel)]=\"searchQuery.CTo\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-3\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-secondary mx-2\" (click)=\"clear()\">\r\n            清除\r\n          </button>\r\n          <button class=\"btn btn-secondary\" (click)=\"getHouseList()\">\r\n            查詢 <i class=\"fas fa-search\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-primary\" (click)=\"onNavigateWithId('modify-floor-plan')\">\r\n            1.調整戶型組成\r\n          </button>\r\n          <button class=\"btn btn-primary mx-2\" (click)=\"onNavigateWithId('modify-household')\">\r\n            2.修改戶型名稱\r\n          </button>\r\n          <button class=\"btn btn-primary\" (click)=\"onNavigateWithId('modify-house-type')\">\r\n            3.設定地主戶\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"table-responsive mt-4\" *ngIf=\"isHouseList\">\r\n      <table class=\"table table-bordered\" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr *ngIf=\"houseList.length\" class=\"text-center\">\r\n            <th><span class=\"block w-8\">樓層</span></th>\r\n            <th *ngFor=\"let house of houseListItem; let idx = index;\">\r\n              <div *ngIf=\"house.isEdit === true\" class=\"font-bold float-left w-32\">\r\n                <input type=\"text\" id=\"CHouseHold\" nbInput class=\"w-full\" [(ngModel)]=\"house.CHouseHold\"><br>\r\n                <span class=\"font-normal text-blue-400 underline inline-block w-[40%] hover:underline cursor-pointer\"\r\n                  (click)=\"clearForm(house)\">\r\n                  取消\r\n                </span>\r\n                <span class=\"font-normal text-blue-400 underline inline-block w-[60%] hover:underline cursor-pointer\"\r\n                  [ngClass]=\"{'opacity-50 cursor-not-allowed': house.CHouseHold === ''}\"\r\n                  (click)=\"onUpdateAllCol(house, idx)\">\r\n                  批次儲存\r\n                </span>\r\n              </div>\r\n              <div *ngIf=\"house.isEdit !== true\" class=\"font-bold w-max\">\r\n                <span class=\"font-normal text-blue-400 hover:underline underline cursor-pointer block min-w-3\"\r\n                  (click)=\"house.isEdit = !house.isEdit\">批次修改名稱 </span>\r\n              </div>\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let row of houseList\" class=\"text-center\">\r\n            <td *ngIf=\"row.length\">\r\n              <p>{{row[0].CFloor}}</p>\r\n            </td>\r\n            <td *ngFor=\"let house of row\" [ngClass]=\"!house.CIsEnable ? 'bg-slate-400' : ''\">\r\n              <p *ngIf=\"!house.isEdit\" class=\"font-bold\">{{ house.CHouseHold || 'null' }}</p>\r\n              <div *ngIf=\"house.CIsEnable && house.isEdit === true\" class=\"font-bold float-left w-32\">\r\n                <input type=\"text\" id=\"CHouseHold\" nbInput class=\"w-full\" [(ngModel)]=\"house.CHouseHold\"><br>\r\n                <span class=\"font-normal text-blue-400 underline inline-block w-[50%] hover:underline cursor-pointer\"\r\n                  (click)=\"closeEditItem(house)\">\r\n                  取消\r\n                </span>\r\n                <span class=\"font-normal text-blue-400 underline inline-block w-[50%] hover:underline cursor-pointer\"\r\n                  [ngClass]=\"{'opacity-50 cursor-not-allowed': house.CHouseHold === ''}\" (click)=\"onUpdateItem(house)\">\r\n                  儲存\r\n                </span>\r\n              </div>\r\n              <div *ngIf=\"house.CIsEnable  && house.isEdit !== true\" class=\"font-bold\">\r\n                <span class=\"font-normal text-blue-400 hover:underline underline cursor-pointer\"\r\n                  (click)=\"onEditItem(house)\">修改名稱 </span>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <div class=\"inline\">\r\n      <div class=\"d-flex justify-content-center w-full\">\r\n        <button class=\"btn btn-primary\" (click)=\"goBack()\">\r\n          返回上一頁\r\n        </button>\r\n        <button class=\"btn btn-primary mx-2\" (click)=\"getHouseList()\">\r\n          取消\r\n        </button>\r\n        <button class=\"btn btn-primary\" (click)=\"onSubmit()\">\r\n          儲存\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>", "import { Component, OnInit } from '@angular/core';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { FinalDocumentService, HouseService } from 'src/services/api/services';\r\nimport { CreateFinalDocArgs, GetListFinalDocRes, TblHouse } from 'src/services/api/models';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { Location } from '@angular/common';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\nimport * as moment from 'moment';\r\n\r\n@Component({\r\n  selector: 'ngx-sample-selection-result',\r\n  templateUrl: './sample-selection-result.component.html',\r\n  styleUrls: ['./sample-selection-result.component.scss'],\r\n})\r\n\r\nexport class SampleSelectionResultComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private valid: ValidationHelper,\r\n    private _finalDocumentService: FinalDocumentService,\r\n    private message: MessageService,\r\n    private route: ActivatedRoute,\r\n    private location: Location,\r\n    private _eventService: EventService,\r\n    private _houseService: HouseService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  houseID: any\r\n  buildCaseId: number\r\n\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        this.buildCaseId = +(params.get('id1') ?? 0);\r\n        this.houseID = +(params.get('id2') ?? 0);\r\n        if(this.houseID) {\r\n          this.getHouseById()\r\n        }\r\n        this.getListFinalDoc()\r\n      }\r\n    });\r\n  }\r\n\r\n  houseByID: TblHouse\r\n\r\n  getHouseById() {\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({\r\n      body: { CHouseID: this.houseID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseByID = res.Entries\r\n      }\r\n    })\r\n  }\r\n\r\n  documentStatusOptions = ['待審核', '已駁回', '待客戶簽回', '客戶已簽回']\r\n\r\n  openPdfInNewTab(data?: any) {\r\n    if (data && data.CFile) window.open(data.CFile, '_blank');\r\n  }\r\n\r\n\r\n  listFinalDoc: GetListFinalDocRes[]\r\n\r\n\r\n  getListFinalDoc() {\r\n    this._finalDocumentService.apiFinalDocumentGetListFinalDocPost$Json({\r\n      body: {\r\n        CHouseID: this.houseID,\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize,\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.listFinalDoc = res.Entries! ?? []\r\n        this.totalRecords = res.TotalItems;\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  listSpecialChangeAvailable: any[]\r\n  getListSpecialChangeAvailable() {\r\n    this._finalDocumentService.apiFinalDocumentGetListSpecialChangeAvailablePost$Json({\r\n      body: {\r\n        CHouseID: this.houseID,\r\n      }\r\n    }).subscribe(res => {\r\n      this.listSpecialChangeAvailable = []\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialChangeAvailable = res.Entries! ?? []\r\n        if (res.Entries.length) {\r\n          this.listSpecialChangeAvailable = res.Entries.map((e: any) => {\r\n            return { ...e, isChecked: false }\r\n          })\r\n        }\r\n      }\r\n    })\r\n  }\r\n  finalDoc: CreateFinalDocArgs\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[文件名稱]', this.finalDoc.CDocumentName)\r\n    this.valid.required('[送審資訊]', this.finalDoc.CApproveRemark)\r\n    this.valid.required('[系統操作說明]', this.finalDoc.CNote)\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n\r\n  getCheckedCIDs(changeArray: any[]) {\r\n    if (changeArray && changeArray.length) {\r\n      return changeArray.filter(change => change.isChecked).map(change => change.CID);\r\n    } return []\r\n  }\r\n\r\n  onCreateFinalDoc(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    const param = {\r\n      ...this.finalDoc,\r\n      CSpecialChange: this.getCheckedCIDs(this.listSpecialChangeAvailable)\r\n    }\r\n    this._finalDocumentService.apiFinalDocumentCreateFinalDocPost$Json({\r\n      body: param\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.getListFinalDoc();\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    })\r\n  }\r\n\r\n  isChecked = true\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListFinalDoc();\r\n  }\r\n\r\n\r\n  addNew(ref: any) {\r\n    this.finalDoc = {\r\n      CHouseID: this.houseID,\r\n      CDocumentName: '',\r\n      CApproveRemark: '',\r\n      CNote:\"\"\r\n    }\r\n    this.getListSpecialChangeAvailable()\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onOpenModel(ref: any) {\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  formatDate(date:string){\r\n    return moment(date).format('YYYY/MM/DD HH:mm');\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <!-- <ngx-breadcrumb></ngx-breadcrumb> -->\r\n    <div style=\"font-size: 32px;\">戶別管理 / 客變確認圖說</div>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此檢視該戶別客戶對於選樣結果之簽認文件，並可選擇要將哪些客變圖面整合為一份圖面請客戶簽回確認。\r\n\r\n      如果該位客戶有多份簽回檔案，於客戶端僅會顯示最新的一份文件。</h1>\r\n\r\n    <div class=\"d-flex flex-wrap\">\r\n\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-info\" (click)=\"addNew(dialogConfirmImage)\">\r\n            新增確認客變圖</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\" class=\"text-center\">\r\n            <th scope=\"col\" class=\"col-1\">類型</th>\r\n            <th scope=\"col\" class=\"col-1\">文件名稱</th>\r\n            <th scope=\"col\" class=\"col-1\">簽回日期 </th>\r\n            <th scope=\"col\" class=\"col-1\">狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of listFinalDoc ; let i = index\" class=\"text-center\">\r\n            <td>{{ item.CIsChange === true ? '客變' : (item.CIsChange === false ? '選樣' :'') }}</td>\r\n            <td>{{ item.CDocumentName}}</td>\r\n            <td>{{ item.CSignDate ? (item.CSignDate | dateFormat) : ''}}</td>\r\n            <td>{{ item.CDocumentStatus! | getDocumentStatus }}</td>\r\n            <td class=\"text-center w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" [disabled]=\"!item.CFile\"\r\n                (click)=\"openPdfInNewTab(item)\">檢視</button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n  <nb-card-footer>\r\n    <div class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm\" (click)=\"goBack()\">\r\n        返回上一頁\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n\r\n<ng-template #dialogConfirmImage let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:1000px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      戶別管理 > 客變確認圖說 > {{houseByID.CHousehold}} {{houseByID.CFloor}}F\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <h4>\r\n        請確認要將哪些圖面整合為一份文件供客戶簽名確認。\r\n      </h4>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"CDocumentName\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          文件名稱\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"文件名稱\" [(ngModel)]=\"finalDoc.CDocumentName\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"isChecked\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          選樣結果\r\n        </label>\r\n        <nb-checkbox status=\"basic\" [(checked)]=\"isChecked\" disabled>選樣結果\r\n        </nb-checkbox>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"客變圖\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          客變圖\r\n        </label>\r\n        <h4>僅能勾選已通過審核之圖面。</h4>\r\n\r\n      </div>\r\n\r\n      <table style=\"min-width: 600px;\" class=\"table border table-striped\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th>來源</th>\r\n            <th>討論日期</th>\r\n            <th>圖面名稱</th>\r\n            <th>上傳日期</th>\r\n            <th>通過日期</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let drawing of listSpecialChangeAvailable\">\r\n\r\n            <td>\r\n              <nb-checkbox status=\"basic\" [(checked)]=\"drawing.isChecked\">\r\n              </nb-checkbox>\r\n            </td>\r\n            <td>{{ drawing.CSource| specialChangeSource }}</td>\r\n            <td>{{ formatDate(drawing.CChangeDate) }}</td>\r\n            <td>{{ drawing.CDrawingName }}</td>\r\n            <td>{{ formatDate(drawing.CCreateDT) }}</td>\r\n            <td>{{ formatDate(drawing.CApproveDate) }}</td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"remark\" baseLabel class=\"required-field align-self-start col-3\">送審資訊\r\n          <p style=\"color: red\">內部審核人員查看</p>\r\n        </label>\r\n\r\n        <textarea name=\"remark\" id=\"remark\" rows=\"5\" nbInput style=\"resize: none; max-width: none\" class=\"w-full\"\r\n          [(ngModel)]=\"finalDoc.CApproveRemark\">\r\n        </textarea>\r\n      </div>\r\n\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"CNote\" baseLabel class=\"required-field align-self-start col-3\">摘要註記\r\n          <p style=\"color: red\">客戶於文件中查看</p>\r\n        </label>\r\n        <textarea name=\"CNote\" id=\"CNote\" rows=\"5\" nbInput style=\"resize: none; max-width: none\" class=\"w-full\"\r\n          [(ngModel)]=\"finalDoc.CNote\">\r\n        </textarea>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-outline-secondary m-2\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-success m-2\" (click)=\"onCreateFinalDoc(ref)\">確認送出審核</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>", "import { Component, OnInit } from '@angular/core';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { HouseService } from 'src/services/api/services';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { EditHouseRegularPicture, GetListHouseRegularPicRes } from 'src/services/api/models';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-standard-house-plan',\r\n  templateUrl: './standard-house-plan.component.html',\r\n  styleUrls: ['./standard-house-plan.component.scss'],\r\n})\r\n\r\nexport class StandardHousePlanComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _houseService: HouseService,\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n\r\n  selectedBuilding: any\r\n  buildingSelectedOptions: any[] = [\r\n    {\r\n      value: '', label: '全部'\r\n    }\r\n  ]\r\n\r\n  getListBuilding() {\r\n    this._houseService.apiHouseGetListBuildingPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.buildCaseId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.buildingSelectedOptions = [{\r\n          value: '', label: '全部'\r\n        }, ...res.Entries.map(e => {\r\n          return { value: e, label: e }\r\n        })]\r\n        this.selectedBuilding = this.buildingSelectedOptions[0]\r\n        this.getListHouseRegularPic()\r\n      }\r\n    })\r\n  }\r\n\r\n  listHouseRegularPic: GetListHouseRegularPicRes[]\r\n\r\n\r\n  getListHouseRegularPic() {\r\n    let param = {\r\n      CBuildCaseID: this.buildCaseId,\r\n      CBuildingName: this.selectedBuilding.value,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    }\r\n    if (!this.selectedBuilding.value) {\r\n      delete param.CBuildingName\r\n    }\r\n    this._houseService.apiHouseGetListHouseRegularPicPost$Json({\r\n      body: param\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.listHouseRegularPic = res.Entries! ?? []\r\n        this.totalRecords = res.TotalItems!\r\n      }\r\n    })\r\n  }\r\n\r\n  checkAll(checked: boolean, houseRegularPic: any) {\r\n    if (houseRegularPic.CHouse && houseRegularPic.CHouse.length > 0) {\r\n      houseRegularPic.CHouse.forEach((item: { CIsSelect: boolean; }) => (item.CIsSelect = checked));\r\n    }\r\n    if (checked) {\r\n      this.listHouseRegularPic.forEach((element, index) => {\r\n        if (element.CRegularPictureID !== houseRegularPic.CRegularPictureID) {\r\n          if (element.CHouse && Array.isArray(element.CHouse)) {\r\n            element.CHouse.forEach((item, o) => {\r\n              item.CIsSelect = false\r\n            })\r\n          }\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  checkItem(checked: boolean, idx: any, i: any) {\r\n    if (checked) {\r\n      this.listHouseRegularPic.forEach((element, index) => {\r\n        if (index !== idx) {\r\n          if (element.CHouse && Array.isArray(element.CHouse)) {\r\n            element.CHouse.forEach((item, o) => {\r\n              if (item.CHouseID === i.CHouseID) {\r\n                item.CIsSelect = false\r\n              }\r\n            });\r\n          }\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  isAllChecked(houseRegularPic: any): boolean {\r\n    return houseRegularPic.CHouse.every((item: { CIsSelect: any; }) => item.CIsSelect);\r\n  }\r\n\r\n\r\n  extractSelectedHouses(data: any[]): EditHouseRegularPicture[] {\r\n    const result: EditHouseRegularPicture[] = [];\r\n    for (const item of data) {\r\n      for (const house of item.CHouse) {\r\n        if (house.CIsSelect) {\r\n          result.push({\r\n            CHouseID: house.CHouseID,\r\n            CRegularPictureID: item.CRegularPictureID\r\n          });\r\n        }\r\n      }\r\n    }\r\n    return result;\r\n  }\r\n\r\n  submitEditHouseRegularPic(ref: any) {\r\n    let bodyHouseRegularPic = this.extractSelectedHouses(this.listHouseRegularPic)\r\n    this._houseService.apiHouseEditHouseRegularPicPost$Json( { body: {CHousePic : bodyHouseRegularPic}\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  onDeleteHouseRegularPic(houseRegularPic: any) {\r\n    if (window.confirm(`確定要刪除【項目${houseRegularPic.CFileName}】?`)) {\r\n    this._houseService.apiHouseDeleteRegularPicturePost$Json({body : { CRegularPictureID: houseRegularPic.CRegularPictureID}}).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getListHouseRegularPic()\r\n      }\r\n    })\r\n  }\r\n  }\r\n\r\n  clear() {\r\n    this.selectedBuilding = this.buildingSelectedOptions[0]\r\n  }\r\n\r\n\r\n  buildCaseId: number\r\n\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        this.getListBuilding()\r\n      }\r\n    });\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListHouseRegularPic()\r\n  }\r\n\r\n\r\n  onOpen(ref: any) {\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n}\r\n", "<!-- 2.1.6 -->\r\n<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\"></h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">棟別</label>\r\n          <nb-select placeholder=\"狀態\" [(ngModel)]=\"selectedBuilding\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let building of buildingSelectedOptions\" [value]=\"building\">\r\n              {{ building.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-secondary\" (click)=\"clear()\">\r\n            清除\r\n          </button>\r\n          <button class=\"btn btn-secondary mx-2\" (click)=\"getListHouseRegularPic()\">\r\n            查詢\r\n          </button>\r\n          <button class=\"btn btn-info\" (click)=\"onOpen(dialog)\">\r\n            棟別\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1\">檔案</th>\r\n            <th scope=\"col\" class=\"col-1\">適用戶型 </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of listHouseRegularPic ; let i = index\">\r\n            <td>{{ item.CFileName}}</td>\r\n            <td>\r\n            <span *ngFor=\"let i of item.CHouse\">\r\n              {{i.CHouseHold}} 、\r\n\r\n            </span>\r\n          </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:700px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      戶別管理》設定戶型標準圖》修改\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"d-flex justify-end\">\r\n        <button class=\"btn btn-primary mx-2\" *ngIf=\"listHouseRegularPic.length\" (click)=\"submitEditHouseRegularPic(ref)\">上傳檔案</button>\r\n      </div>\r\n      <div class=\"bg-white p-4 rounded shadow m-2\" \r\n        *ngFor=\"let houseRegularPic of listHouseRegularPic; let idx = index\">\r\n        <div class=\"mb-2\">\r\n          <label for=\"standard-drawing\" class=\"block text-gray-700 font-bold mb-2\">\r\n            {{houseRegularPic.CFileName}}\r\n          </label>\r\n        </div>\r\n\r\n        <div>\r\n          <label for=\"applicable-models\" class=\"block text-gray-700 font-bold mb-2\">\r\n            適用戶型\r\n          </label>\r\n\r\n          <label class=\"inline-flex items-center mr-4\">\r\n            <span class=\"mr-2\">適用戶型 </span>\r\n            <nb-checkbox status=\"basic\" (checkedChange)=\"checkAll($event, houseRegularPic)\" [checked]=\"isAllChecked(houseRegularPic)\">全選\r\n            </nb-checkbox>\r\n          </label>\r\n          <div class=\"flex flex-wrap\">\r\n            <label class=\"inline-flex items-center mr-4\" *ngFor=\"let i of houseRegularPic.CHouse\">\r\n              <nb-checkbox status=\"basic\" [(checked)]=\"i.CIsSelect\" (checkedChange)=\"checkItem($event, idx, i)\">\r\n              </nb-checkbox>\r\n              <span class=\"ml-2\">{{i.CHouseHold || 'null'}}</span>\r\n            </label>\r\n            <div class=\"w-full text-right\">\r\n              <button type=\"button\" class=\"btn btn-outline-dark py-2 px-4 btn-sm ml-6 \" (click)=\"onDeleteHouseRegularPic(houseRegularPic)\">\r\n                刪除\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-primary btn-sm mx-2\" (click)=\"onClose(ref)\">返回上一頁</button>\r\n      <button class=\"btn btn-primary btn-sm mx-2\" (click)=\"onClose(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>", "import { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map, switchMap } from 'rxjs/operators';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { QuotationItem, QuotationRequest, QuotationResponse, CQuotationItemType } from '../models/quotation.model';\r\nimport { QuotationService as ApiQuotationService } from '../../services/api/services/quotation.service';\r\nimport {\r\n  GetListQuotationRequest,\r\n  GetQuotationByIdRequest,\r\n  SaveDataQuotation,\r\n  QuotationItemModel,\r\n  GetListByHouseIdRequest,\r\n  LoadDefaultItemsRequest\r\n} from '../../services/api/models';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class QuotationService {\r\n  private readonly apiUrl = '/api/Quotation';\r\n\r\n  constructor(\r\n    private apiQuotationService: ApiQuotationService,\r\n    private http: HttpClient\r\n  ) { }\r\n  // 取得報價單列表\r\n  getQuotationList(request: GetListQuotationRequest): Observable<any> {\r\n    return this.apiQuotationService.apiQuotationGetListPost$Json({ body: request });\r\n  }\r\n  // 取得單筆報價單資料\r\n  getQuotationData(quotationId: number): Observable<any> {\r\n    const request: GetQuotationByIdRequest = { cQuotationID: quotationId };\r\n    return this.apiQuotationService.apiQuotationGetDataPost$Json({ body: request });\r\n  }  // 取得戶別的報價項目\r\n  getQuotationByHouseId(houseId: number): Observable<any> {\r\n    const request: GetListByHouseIdRequest = { cHouseID: houseId };\r\n    return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({ body: request }).pipe(\r\n      map(response => {\r\n        // 將 API 響應的小寫屬性名轉換為前端期望的大寫屬性名\r\n        if (response) {\r\n          return {\r\n            StatusCode: response.StatusCode,\r\n            Message: response.Message,\r\n            TotalItems: response.TotalItems,\r\n            Entries: response.Entries // 保持原始結構，因為 entries 是一個物件而不是陣列\r\n          };\r\n        }\r\n        return response;\r\n      })\r\n    );\r\n  }\r\n  // 儲存報價單 (支援單一項目)\r\n  saveQuotationItem(quotation: SaveDataQuotation): Observable<any> {\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: quotation });\r\n  }  // 儲存報價單 (支援批量保存多個項目) - 使用單一請求\r\n  saveQuotation(request: {\r\n    houseId: number;\r\n    items: QuotationItem[];\r\n    quotationId?: number;\r\n    cShowOther?: boolean;\r\n    cOtherName?: string;\r\n    cOtherPercent?: number;\r\n  }): Observable<QuotationResponse> {\r\n    // 將 QuotationItem 轉換為 QuotationItemModel 格式\r\n    const quotationItems: QuotationItemModel[] = request.items.map(item => {\r\n      const quotationType = item.CQuotationItemType && item.CQuotationItemType > 0 ? item.CQuotationItemType : CQuotationItemType.自定義;\r\n\r\n\r\n      return {\r\n        CItemName: item.cItemName,\r\n        CUnit: item.cUnit || '',\r\n        CUnitPrice: item.cUnitPrice,\r\n        CCount: item.cCount,\r\n        CStatus: item.cStatus || 1,\r\n        CQuotationItemType: quotationType,\r\n        CRemark: item.cRemark || ''\r\n      };\r\n    });\r\n\r\n    // 建立 SaveDataQuotation 請求，並直接添加額外費用欄位\r\n    const saveRequest: any = {\r\n      CHouseID: request.houseId,\r\n      CQuotationID: request.quotationId || 0, // 使用傳入的 quotationId，如果沒有則為 0（新建報價單）\r\n      Items: quotationItems,\r\n      // 額外費用相關欄位 - 直接添加到請求中\r\n      CShowOther: request.cShowOther || false,\r\n      COtherName: request.cOtherName || '',\r\n      COtherPercent: request.cOtherPercent || 0\r\n    };\r\n\r\n    // 調試用 - 印出最終送出的請求資料\r\n    console.log('QuotationService saveRequest:', saveRequest);\r\n\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: saveRequest }).pipe(\r\n      map(response => ({\r\n        success: response?.StatusCode === 0,\r\n        message: response?.StatusCode === 0 ? '報價單保存成功' : '報價單保存失敗',\r\n        data: request.items\r\n      } as QuotationResponse))\r\n    );\r\n  }\r\n\r\n  // 取得預設報價項目 (保持原有方法以兼容現有代碼)\r\n  getDefaultQuotationItems(): Observable<QuotationResponse> {\r\n    // 使用 GetList 方法獲取預設項目\r\n    const request: GetListQuotationRequest = {\r\n      pageIndex: 0,\r\n      pageSize: 100,\r\n      // 其他預設參數可能需要根據實際 API 需求調整\r\n    };\r\n    return this.apiQuotationService.apiQuotationGetListPost$Json({ body: request }).pipe(\r\n      map(response => {\r\n        // 轉換 API 響應格式以保持兼容性\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 statusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: response.Entries || []\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }  // 載入預設報價項目 (LoadDefaultItems API)\r\n  loadDefaultItems(request: LoadDefaultItemsRequest): Observable<QuotationResponse> {\r\n    // 使用注入的 HttpClient 直接發送請求，但使用 apiQuotationService 的 rootUrl\r\n    return this.http.post<any>(\r\n      `${this.apiQuotationService.rootUrl}/api/Quotation/LoadDefaultItems`,\r\n      request\r\n    ).pipe(\r\n      map(response => {\r\n        // 轉換 API 響應格式以保持兼容性\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: response.Entries || []\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }  // 載入常規報價項目 (LoadRegularItems API)\r\n  loadRegularItems(request: LoadDefaultItemsRequest): Observable<QuotationResponse> {\r\n    // 使用注入的 HttpClient 直接發送請求，但使用 apiQuotationService 的 rootUrl\r\n    return this.http.post<any>(\r\n      `${this.apiQuotationService.rootUrl}/api/Quotation/LoadRegularItems`,\r\n      request\r\n    ).pipe(\r\n      map(response => {\r\n        // 轉換 API 響應格式以保持兼容性\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: response.Entries || []\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }\r\n\r\n  // 更新報價項目 (使用 SaveData 方法)\r\n  updateQuotationItem(quotationId: number, item: QuotationItem): Observable<QuotationResponse> {\r\n    const saveData: SaveDataQuotation = {\r\n      CHouseID: item.cHouseID,\r\n      CQuotationID: quotationId,\r\n      Items: [{\r\n        CItemName: item.cItemName,\r\n        CUnitPrice: item.cUnitPrice,\r\n        CCount: item.cCount,\r\n        CStatus: item.cStatus || 1,\r\n        CQuotationItemType: item.CQuotationItemType || CQuotationItemType.自定義\r\n      }]\r\n    };\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: saveData }).pipe(\r\n      map(response => {\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: [item] // 包裝為陣列以符合 QuotationResponse 格式\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }\r\n\r\n  // 匯出報價單 (需要另外實作，因為 swagger 中沒有此 API)\r\n  exportQuotation(houseId: number): Observable<Blob> {\r\n    // 這個方法可能需要使用其他 API 或保持原有實作\r\n    // 暫時拋出錯誤提示需要實作\r\n    throw new Error('Export quotation functionality needs to be implemented separately');\r\n  }\r\n\r\n  // 鎖定報價單\r\n  lockQuotation(quotationId: number): Observable<any> {\r\n    return this.http.post<any>(\r\n      `${this.apiQuotationService.rootUrl}/api/Quotation/LockQuotation`,\r\n      quotationId\r\n    ).pipe(\r\n      map(response => {\r\n        return {\r\n          success: response.StatusCode === 0,\r\n          message: response.Message || '',\r\n          data: response.Entries || null\r\n        };\r\n      })\r\n    );\r\n  }\r\n}\r\n", "export enum EnumHouseProgress {\r\n  尚未開始 = 0,\r\n  已閱讀操作說明 = 1,\r\n  選樣完成 = 2,\r\n  簽署完成 = 3\r\n}\r\n", "export enum EnumPayStatus {\r\n  未付款 = 0, //unpaid\r\n  已付款 = 1,  //paid\r\n  無須付款 = 2, //No payment required\r\n}\r\n", "export enum EnumQuotationStatus {\r\n  待報價 = 1,\r\n  已報價 = 2,\r\n  已簽回 = 3,\r\n}\r\n", "export enum EnumSignStatus {\r\n  已簽回 = 1, //signed back\r\n  未簽回 = 2  //not signed back\r\n}\r\n", "export const QUOTATION_TEMPLATE = `<!DOCTYPE html>\r\n<html>\r\n\r\n<head>\r\n  <meta charset=\"utf-8\">\r\n  <title>報價單列印模板</title>\r\n  <style>\r\n    body {\r\n      font-family: 'Microsoft JhengHei', '微軟正黑體', <PERSON><PERSON>, sans-serif;\r\n      margin: 20px;\r\n      font-size: 14px;\r\n      line-height: 1.6;\r\n    }\r\n\r\n    .header {\r\n      text-align: center;\r\n      margin-bottom: 30px;\r\n    }\r\n\r\n    .header h1 {\r\n      margin: 0;\r\n      font-size: 24px;\r\n      color: #333;\r\n      font-weight: bold;\r\n    }\r\n\r\n    .info-section {\r\n      margin-bottom: 20px;\r\n      border-bottom: 1px solid #ddd;\r\n      padding-bottom: 15px;\r\n    }\r\n\r\n    .info-row {\r\n      display: flex;\r\n      margin-bottom: 8px;\r\n    }\r\n\r\n    .info-label {\r\n      font-weight: bold;\r\n      width: 100px;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    .info-value {\r\n      flex: 1;\r\n    }\r\n\r\n    table {\r\n      width: 100%;\r\n      border-collapse: collapse;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    th {\r\n      background-color: #27ae60;\r\n      color: white;\r\n      border: 1px solid #ddd;\r\n      padding: 10px 8px;\r\n      text-align: center;\r\n      font-weight: bold;\r\n    }\r\n\r\n    td {\r\n      border: 1px solid #ddd;\r\n      padding: 8px;\r\n    }\r\n\r\n    .text-center {\r\n      text-align: center;\r\n    }\r\n\r\n    .text-right {\r\n      text-align: right;\r\n    }\r\n\r\n    .total-section {\r\n      text-align: right;\r\n      margin-top: 20px;\r\n      padding-top: 15px;\r\n      border-top: 2px solid #27ae60;\r\n    }\r\n\r\n    .subtotal {\r\n      font-size: 14px;\r\n      margin-bottom: 5px;\r\n      color: #666;\r\n    }\r\n\r\n    .additional-fee {\r\n      font-size: 14px;\r\n      margin-bottom: 10px;\r\n      color: #666;\r\n    }\r\n\r\n    .total-amount {\r\n      font-size: 18px;\r\n      font-weight: bold;\r\n      color: #27ae60;\r\n      border-top: 1px solid #ddd;\r\n      padding-top: 10px;\r\n    }\r\n\r\n    .footer {\r\n      margin-top: 40px;\r\n      text-align: center;\r\n      font-size: 12px;\r\n      color: #666;\r\n    }\r\n\r\n    .signature-section {\r\n      margin-top: 40px;\r\n      page-break-inside: avoid;\r\n    }\r\n\r\n    .signature-box {\r\n      width: 300px;\r\n      margin: 0 auto;\r\n      text-align: center;\r\n    }\r\n\r\n    .signature-label {\r\n      font-weight: bold;\r\n      margin-bottom: 40px;\r\n      font-size: 16px;\r\n    }\r\n\r\n    .signature-line {\r\n      border-bottom: 2px solid #000;\r\n      height: 60px;\r\n      margin-bottom: 10px;\r\n      position: relative;\r\n    }\r\n\r\n    .signature-date {\r\n      font-size: 14px;\r\n      margin-top: 15px;\r\n    }\r\n\r\n    .signature-notes {\r\n      margin-top: 30px;\r\n      padding: 15px;\r\n      background-color: #f9f9f9;\r\n      border-left: 4px solid #27ae60;\r\n    }\r\n\r\n    .signature-notes p {\r\n      margin: 0 0 10px 0;\r\n      font-weight: bold;\r\n    }\r\n\r\n    .signature-notes ul {\r\n      margin: 0;\r\n      padding-left: 20px;\r\n    }\r\n\r\n    .signature-notes li {\r\n      margin-bottom: 5px;\r\n      line-height: 1.4;\r\n    }\r\n\r\n    @media print {\r\n      body {\r\n        margin: 0;\r\n      }\r\n\r\n      .header {\r\n        page-break-inside: avoid;\r\n      }\r\n\r\n      .signature-section {\r\n        page-break-inside: avoid;\r\n      }\r\n    }\r\n  </style>\r\n</head>\r\n\r\n<body>\r\n  <div class=\"header\">\r\n    <h1>報價單</h1>\r\n  </div>\r\n\r\n  <div class=\"info-section\">\r\n    <div class=\"info-row\">\r\n      <span class=\"info-label\">建案名稱：</span>\r\n      <span class=\"info-value\">{{buildCaseName}}</span>\r\n    </div>\r\n    <div class=\"info-row\">\r\n      <span class=\"info-label\">戶別：</span>\r\n      <span class=\"info-value\">{{houseHold}}</span>\r\n    </div>\r\n    <div class=\"info-row\">\r\n      <span class=\"info-label\">樓層：</span>\r\n      <span class=\"info-value\">{{floor}}樓</span>\r\n    </div>\r\n    <div class=\"info-row\">\r\n      <span class=\"info-label\">列印日期：</span>\r\n      <span class=\"info-value\">{{printDate}}</span>\r\n    </div>\r\n  </div>\r\n\r\n  <table>\r\n    <thead>\r\n      <tr>\r\n        <th width=\"8%\">序號</th>\r\n        <th width=\"30%\">項目名稱</th>\r\n        <th width=\"12%\">單價 (元)</th>\r\n        <th width=\"8%\">單位</th>\r\n        <th width=\"8%\">數量</th>\r\n        <th width=\"15%\">小計 (元)</th>\r\n        <th width=\"12%\">類型</th>\r\n      </tr>\r\n    </thead>\r\n    <tbody>\r\n      {{itemsHtml}}\r\n    </tbody>\r\n  </table>\r\n\r\n  <div class=\"total-section\">\r\n    <div class=\"subtotal\">\r\n      小計：{{subtotalAmount}}\r\n    </div>\r\n    {{additionalFeeHtml}}\r\n    <div class=\"total-amount\">\r\n      總金額：{{totalAmount}}\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"signature-section\">\r\n    <div class=\"signature-box\">\r\n      <div class=\"signature-label\">客戶簽名：</div>\r\n      <div class=\"signature-line\"></div>\r\n      <div class=\"signature-date\">日期：_____年_____月_____日</div>\r\n    </div>\r\n\r\n    <div class=\"signature-notes\">\r\n      <p><strong>注意事項：</strong></p>\r\n      <ul>\r\n        <li>此報價單有效期限為30天，逾期需重新報價</li>\r\n        <li>報價內容若有異動，請重新確認</li>\r\n        <li>簽名確認後即視為同意此報價內容</li>\r\n      </ul>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"footer\">\r\n    此報價單由系統自動產生，列印時間：{{printDateTime}}\r\n  </div>\r\n</body>\r\n\r\n</html>`;\r\n"], "names": ["SpecialChangeSourcePipe", "transform", "value", "pure", "standalone", "CQuotationItemType", "moment", "BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵlistener", "CustomerChangePictureComponent_button_9_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "dialogUploadDrawing_r4", "ɵɵreference", "ɵɵresetView", "addNew", "ɵɵtext", "ɵɵelementEnd", "CustomerChangePictureComponent_tr_27_button_13_Template_button_click_0_listener", "_r5", "item_r6", "$implicit", "onEdit", "ɵɵtemplate", "CustomerChangePictureComponent_tr_27_button_13_Template", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "CSource", "formatDate", "CChangeDate", "CDrawingName", "CCreateDT", "CIsApprove", "ɵɵproperty", "isUpdate", "CustomerChangePictureComponent_ng_template_34_button_16_Template_button_click_0_listener", "_r8", "inputFile_r9", "click", "ɵɵelement", "file_r11", "data", "ɵɵsanitizeUrl", "CustomerChangePictureComponent_ng_template_34_div_21_div_1_img_1_Template", "CustomerChangePictureComponent_ng_template_34_div_21_div_1_span_2_Template", "CustomerChangePictureComponent_ng_template_34_div_21_div_1_Template_span_click_5_listener", "i_r12", "_r10", "index", "removeFile", "isImage", "CFileType", "CFileName", "CustomerChangePictureComponent_ng_template_34_div_21_div_1_Template", "imageUrlList", "CustomerChangePictureComponent_ng_template_34_div_22_div_1_img_1_Template_img_click_0_listener", "_r13", "file_r14", "openNewTab", "CFile", "CustomerChangePictureComponent_ng_template_34_div_22_div_1_span_2_Template_span_click_0_listener", "_r15", "CustomerChangePictureComponent_ng_template_34_div_22_div_1_img_1_Template", "CustomerChangePictureComponent_ng_template_34_div_22_div_1_span_2_Template", "isPDFString", "CustomerChangePictureComponent_ng_template_34_div_22_div_1_Template", "SpecialChange", "CFileRes", "CustomerChangePictureComponent_ng_template_34_button_30_Template_button_click_0_listener", "_r17", "ref_r16", "dialogRef", "onSaveSpecialChange", "ɵɵtwoWayListener", "CustomerChangePictureComponent_ng_template_34_Template_p_calendar_ngModelChange_8_listener", "$event", "_r7", "ɵɵtwoWayBindingSet", "formSpecialChange", "CustomerChangePictureComponent_ng_template_34_Template_input_ngModelChange_12_listener", "CustomerChangePictureComponent_ng_template_34_button_16_Template", "CustomerChangePictureComponent_ng_template_34_Template_input_change_17_listener", "detectFiles", "CustomerChangePictureComponent_ng_template_34_div_21_Template", "CustomerChangePictureComponent_ng_template_34_div_22_Template", "CustomerChangePictureComponent_ng_template_34_Template_textarea_ngModelChange_26_listener", "CApproveRemark", "CustomerChangePictureComponent_ng_template_34_Template_button_click_28_listener", "onClose", "CustomerChangePictureComponent_ng_template_34_button_30_Template", "ɵɵtextInterpolate2", "house", "CHousehold", "CFloor", "ɵɵtwoWayProperty", "isEdit", "CustomerChangePictureComponent", "constructor", "_allow", "dialogService", "valid", "_specialChangeService", "_houseService", "route", "message", "location", "_eventService", "statusOptions", "key", "label", "pageFirst", "pageSize", "pageIndex", "totalRecords", "listPictures", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "id", "buildCaseId", "idParam2", "id2", "houseId", "getListSpecialChange", "getHouseById", "openPdfInNewTab", "window", "open", "apiSpecialChangeGetListSpecialChangePost$Json", "body", "CHouseId", "PageIndex", "PageSize", "res", "TotalItems", "Entries", "StatusCode", "listSpecialChange", "apiHouseGetHouseByIdPost$Json", "CHouseID", "houseTitle", "getSpecialChangeById", "ref", "CSpecialChangeID", "apiSpecialChangeGetSpecialChangeByIdPost$Json", "CBuildCaseID", "SpecialChangeFiles", "Date", "validation", "errorMessages", "length", "showErrorMSGs", "apiSpecialChangeSaveSpecialChangePost$Json", "formatParam", "showSucessMSG", "close", "pageChanged", "newPage", "specialChange", "clear", "required", "format", "deleteDataFields", "array", "item", "result", "removeBase64Prefix", "base64String", "prefixIndex", "indexOf", "substring", "event", "files", "target", "allowedTypes", "fileRegex", "i", "file", "test", "type", "showErrorMSG", "includes", "reader", "FileReader", "onload", "e", "fileType", "startsWith", "push", "CFileBlood", "name", "console", "log", "fileInput", "nativeElement", "readAsDataURL", "str", "toLowerCase", "endsWith", "isPdf", "extension", "splice", "removeImage", "pictureId", "filter", "x", "uploadImage", "renameFile", "blob", "slice", "size", "newFile", "File", "goBack", "action", "payload", "back", "url", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "ValidationHelper", "i4", "SpecialChangeService", "HouseService", "i5", "ActivatedRoute", "i6", "MessageService", "i7", "Location", "i8", "EventService", "selectors", "viewQuery", "CustomerChangePictureComponent_Query", "rf", "ctx", "CustomerChangePictureComponent_button_9_Template", "CustomerChangePictureComponent_tr_27_Template", "CustomerChangePictureComponent_Template_ngb_pagination_pageChange_29_listener", "_r1", "CustomerChangePictureComponent_Template_button_click_32_listener", "CustomerChangePictureComponent_ng_template_34_Template", "ɵɵtemplateRefExtractor", "ɵɵtextInterpolate1", "isCreate", "NbCardModule", "NbCheckboxModule", "NbInputModule", "NbOptionModule", "NbSelectModule", "FormsModule", "<PERSON><PERSON><PERSON>", "NgIf", "PaginationComponent", "FullCalendarModule", "interactionPlugin", "dayGridPlugin", "timeGridPlugin", "listPlugin", "bootstrapPlugin", "CalendarModule", "FinaldochouseManagementComponent_tr_36_Template_button_click_6_listener", "data_r4", "_r3", "ctx_r4", "CDocumentName", "CSignDate", "CFileAfter", "CFileBefore", "FinaldochouseManagementComponent_ng_template_42_div_13_Template_button_click_3_listener", "clearFile", "fileName", "FinaldochouseManagementComponent_ng_template_42_Template_input_change_9_listener", "_r6", "onFileSelected", "FinaldochouseManagementComponent_ng_template_42_div_13_Template", "FinaldochouseManagementComponent_ng_template_42_Template_input_ngModelChange_17_listener", "FinaldochouseManagementComponent_ng_template_42_Template_textarea_ngModelChange_23_listener", "FinaldochouseManagementComponent_ng_template_42_Template_textarea_ngModelChange_30_listener", "CNote", "FinaldochouseManagementComponent_ng_template_42_Template_button_click_33_listener", "ref_r8", "FinaldochouseManagementComponent_ng_template_42_Template_button_click_35_listener", "onCreateFinalDoc", "houseByID", "FinaldochouseManagementComponent", "enum<PERSON>elper", "finalDocumentService", "pettern", "router", "destroyref", "calendarOptions", "plugins", "locale", "headerToolbar", "left", "center", "right", "getListFinalDocRequest", "uploadFinaldocRequest", "listFinalDoc", "maxDate", "currentHouseID", "getList", "CSign", "apiFinalDocumentGetListFinalDocByHousePost$Json", "pipe", "apiFinalDocumentUploadFinalDocPost$Json", "Message", "convertToBlob", "mimeType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Blob", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FinalDocumentService", "PetternHelper", "Router", "DestroyRef", "i9", "i10", "FinaldochouseManagementComponent_Query", "FinaldochouseManagementComponent_Template_p_calendar_ngModelChange_10_listener", "CDateStart", "FinaldochouseManagementComponent_Template_p_calendar_ngModelChange_13_listener", "CDateEnd", "FinaldochouseManagementComponent_Template_button_click_15_listener", "FinaldochouseManagementComponent_Template_button_click_21_listener", "dialogUploadFinaldoc_r2", "FinaldochouseManagementComponent_tr_36_Template", "FinaldochouseManagementComponent_Template_ngx_pagination_PageChange_37_listener", "FinaldochouseManagementComponent_Template_button_click_40_listener", "FinaldochouseManagementComponent_ng_template_42_Template", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "i11", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i12", "Calendar", "styles", "RouterModule", "HouseholdManagementComponent", "SampleSelectionResultComponent", "ModifyFloorPlanComponent", "ModifyHouseholdComponent", "ModifyHouseTypeComponent", "StandardHousePlanComponent", "routes", "path", "component", "HouseholdRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "exports", "SharedModule", "CommonModule", "QUOTATION_TEMPLATE", "NbDatepickerModule", "concatMap", "tap", "NbDateFnsDateModule", "EnumHouseProgress", "EnumHouseType", "EnumPayStatus", "EnumSignStatus", "EnumQuotationStatus", "LocalStorageService", "STORAGE_KEY", "case_r2", "CBuildCaseName", "case_r3", "case_r4", "case_r5", "case_r6", "case_r7", "case_r8", "case_r9", "HouseholdManagementComponent_button_74_Template_button_click_0_listener", "ctx_r10", "dialogHouseholdMain_r12", "openModel", "HouseholdManagementComponent_tr_108_button_20_Template_button_click_0_listener", "_r14", "item_r15", "dialogUpdateHousehold_r16", "openModelDetail", "HouseholdManagementComponent_tr_108_button_20_Template", "HouseholdManagementComponent_tr_108_Template_button_click_21_listener", "onNavidateBuildCaseIdHouseId", "searchQuery", "CBuildCaseSelected", "cID", "CID", "HouseholdManagementComponent_tr_108_Template_button_click_23_listener", "HouseholdManagementComponent_tr_108_Template_button_click_25_listener", "HouseholdManagementComponent_tr_108_Template_button_click_27_listener", "resetSecureKey", "HouseholdManagementComponent_tr_108_Template_button_click_29_listener", "dialogQuotation_r17", "openQuotation", "CHouseHold", "CHouseType", "CIsChange", "CProgressName", "ɵɵtextInterpolate3", "CPayStatus", "CSignStatus", "getQuotationStatusText", "CQuotationStatus", "CIsEnable", "status_r20", "status_r21", "status_r23", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_Template_nb_select_ngModelChange_3_listener", "_r22", "detailSelected", "CPayStatusSelected", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_nb_option_4_Template", "options", "payStatusOptions", "status_r25", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_Template_nb_select_ngModelChange_3_listener", "_r24", "CProgressSelected", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_nb_option_4_Template", "progressOptions", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_select_ngModelChange_4_listener", "_r19", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_nb_option_5_Template", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_9_listener", "houseDetail", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_13_listener", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_17_listener", "CCustomerName", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_21_listener", "CNationalId", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_25_listener", "CMail", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_29_listener", "CPhone", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_select_ngModelChange_33_listener", "CHouseTypeSelected", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_nb_option_34_Template", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_Template", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_Template", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_checkbox_checkedChange_40_listener", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_checkbox_checkedChange_45_listener", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_53_listener", "changeStartDate", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_58_listener", "changeEndDate", "userBuildCaseOptions", "houseTypeOptions", "isChangePayStatus", "isChangeProgress", "StartDate_r26", "EndDate_r27", "HouseholdManagementComponent_ng_template_111_button_5_Template_button_click_0_listener", "_r29", "ref_r28", "onSubmitDetail", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template", "HouseholdManagementComponent_ng_template_111_Template_button_click_3_listener", "_r18", "HouseholdManagementComponent_ng_template_111_button_5_Template", "HouseholdManagementComponent_ng_template_113_button_19_Template_button_click_0_listener", "_r32", "ref_r31", "addHouseHoldMain", "HouseholdManagementComponent_ng_template_113_Template_input_ngModelChange_7_listener", "_r30", "houseHoldMain", "CBuildingName", "HouseholdManagementComponent_ng_template_113_Template_input_ngModelChange_11_listener", "CHouseHoldCount", "HouseholdManagementComponent_ng_template_113_Template_input_ngModelChange_15_listener", "HouseholdManagementComponent_ng_template_113_Template_button_click_17_listener", "HouseholdManagementComponent_ng_template_113_button_19_Template", "HouseholdManagementComponent_ng_template_115_div_4_Template_button_click_1_listener", "_r34", "addQuotationItem", "HouseholdManagementComponent_ng_template_115_div_4_Template_button_click_4_listener", "loadDefaultItems", "HouseholdManagementComponent_ng_template_115_div_4_Template_button_click_6_listener", "loadRegularItems", "HouseholdManagementComponent_ng_template_115_tr_25_button_15_Template_button_click_0_listener", "_r37", "i_r38", "removeQuotationItem", "HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_2_listener", "item_r36", "_r35", "cItemName", "HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_4_listener", "cUnitPrice", "calculateTotal", "HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_6_listener", "cUnit", "HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_8_listener", "cCount", "HouseholdManagementComponent_ng_template_115_tr_25_button_15_Template", "HouseholdManagementComponent_ng_template_115_tr_25_span_16_Template", "ɵɵclassProp", "isQuotationEditable", "formatCurrency", "getQuotationTypeText", "HouseholdManagementComponent_ng_template_115_button_63_Template_button_click_0_listener", "_r39", "createNewQuotation", "HouseholdManagementComponent_ng_template_115_button_67_Template_button_click_0_listener", "_r41", "ref_r40", "lockQuotation", "quotationItems", "HouseholdManagementComponent_ng_template_115_button_68_Template_button_click_0_listener", "_r42", "saveQuotation", "HouseholdManagementComponent_ng_template_115_div_4_Template", "HouseholdManagementComponent_ng_template_115_div_5_Template", "HouseholdManagementComponent_ng_template_115_tr_25_Template", "HouseholdManagementComponent_ng_template_115_tr_26_Template", "HouseholdManagementComponent_ng_template_115_Template_button_click_60_listener", "_r33", "printQuotation", "HouseholdManagementComponent_ng_template_115_button_63_Template", "HouseholdManagementComponent_ng_template_115_Template_button_click_65_listener", "HouseholdManagementComponent_ng_template_115_button_67_Template", "HouseholdManagementComponent_ng_template_115_button_68_Template", "currentHouse", "totalAmount", "additionalFeeAmount", "finalTotalAmount", "_houseHoldMainService", "_buildCaseService", "_ultilityService", "quotationService", "tempBuildCaseID", "cIsEnableOptions", "buildCaseOptions", "houseHoldOptions", "signStatusOptions", "quotationStatusOptions", "getEnumOptions", "initDetail", "CNationalID", "CProgress", "additionalFeeName", "additionalFeePercentage", "enableAdditionalFee", "currentQuotationId", "selectedFile", "buildingSelectedOptions", "receive", "GetSessionStorage", "HOUSE_SEARCH", "previous_search", "JSON", "parse", "CHouseHoldSelected", "find", "CSignStatusSelected", "CQuotationStatusSelected", "CIsEnableSeleted", "CFrom", "CTo", "getListBuildCase", "onSearch", "sessionSave", "AddSessionStorage", "stringify", "getHouseList", "exportHouse", "apiHouseExportHousePost$Json", "downloadExcelFile", "triggerFileInput", "input", "importExcel", "formData", "FormData", "append", "apiHouseImportHousePost$Json", "getListHouseHold", "apiHouseGetListHouseHoldPost$Json", "map", "findIndex", "formatQuery", "bodyRequest", "sortByFloorDescending", "arr", "sort", "a", "b", "apiHouseGetHouseListPost$Json", "houseList", "onSelectionChangeBuildCase", "apiBuildCaseGetAllBuildCaseForSelectPost$Json", "CIsPagi", "CStatus", "setTimeout", "CChangeStartDate", "CChangeEndDate", "CBuildCaseId", "findItemInArray", "editHouseArgsParam", "CId", "apiHouseEditHousePost$Json", "onSubmit", "bodyReq", "onNavidateId", "idURL", "navigate", "confirm", "apiHouseResetHouseSecureKeyPost$Json", "isStringMaxLength", "pattern", "MailPettern", "isPhoneNumber", "checkStartBeforeEnd", "validationHouseHoldMain", "isNaturalNumberInRange", "apiHouseHoldMainAddHouseHoldMainPost$Json", "dialog", "_this", "_asyncToGenerator", "response", "getQuotationByHouseId", "to<PERSON>romise", "CQuotationVersionId", "Items", "Array", "isArray", "entry", "cHouseID", "cQuotationID", "CQuotationID", "CItemName", "CUnit", "CUnitPrice", "CCount", "cStatus", "自定義", "cRemark", "CRemark", "cQuotationStatus", "error", "context", "closeOnBackdropClick", "_this2", "request", "success", "defaultItems", "客變需求", "_this3", "regularItems", "選樣", "reduce", "sum", "calculateFinalTotal", "Math", "round", "amount", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "_this4", "invalidItems", "trim", "items", "quotationId", "cShowOther", "cOtherName", "cOtherPercent", "exportQuotation", "_this5", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "revokeObjectURL", "printContent", "generatePrintContent", "printWindow", "write", "print", "template", "currentDate", "toLocaleDateString", "buildCaseName", "itemsHtml", "for<PERSON>ach", "subtotal", "quotationType", "unit", "additionalFeeHtml", "html", "replace", "toLocaleString", "_this6", "status", "待報價", "已報價", "已簽回", "HouseHoldMainService", "BuildCaseService", "UtilityService", "QuotationService", "HouseholdManagementComponent_Query", "HouseholdManagementComponent_Template_nb_select_ngModelChange_11_listener", "HouseholdManagementComponent_Template_nb_select_selected<PERSON><PERSON>e_11_listener", "HouseholdManagementComponent_nb_option_12_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_17_listener", "HouseholdManagementComponent_nb_option_18_Template", "HouseholdManagementComponent_Template_input_ngModelChange_24_listener", "HouseholdManagementComponent_Template_input_ngModelChange_28_listener", "HouseholdManagementComponent_Template_nb_select_ngModelChange_34_listener", "HouseholdManagementComponent_nb_option_35_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_40_listener", "HouseholdManagementComponent_nb_option_41_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_46_listener", "HouseholdManagementComponent_nb_option_47_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_52_listener", "HouseholdManagementComponent_nb_option_53_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_58_listener", "HouseholdManagementComponent_nb_option_59_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_64_listener", "HouseholdManagementComponent_nb_option_65_Template", "HouseholdManagementComponent_Template_button_click_69_listener", "HouseholdManagementComponent_button_74_Template", "HouseholdManagementComponent_Template_button_click_75_listener", "HouseholdManagementComponent_Template_button_click_77_listener", "HouseholdManagementComponent_Template_input_change_79_listener", "HouseholdManagementComponent_Template_button_click_81_listener", "HouseholdManagementComponent_tr_108_Template", "HouseholdManagementComponent_Template_ngb_pagination_pageChange_110_listener", "HouseholdManagementComponent_ng_template_111_Template", "HouseholdManagementComponent_ng_template_113_Template", "HouseholdManagementComponent_ng_template_115_Template", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i13", "NumberValueAccessor", "MaxLengthValidator", "MinValidator", "MaxValidator", "NbCheckboxComponent", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "NbPrefixDirective", "NbIconComponent", "NbDatepickerDirective", "NbDatepickerComponent", "i14", "NgbPagination", "i15", "BreadcrumbComponent", "i16", "BaseLabelDirective", "ThemeModule", "NbMenuModule", "HouseholdManagementModule", "forRoot", "declarations", "building_r1", "ModifyFloorPlanComponent_div_37_tr_3_th_2_Template_nb_checkbox_checkedChange_2_listener", "idx_r3", "ctx_r3", "enableAllAtIndex", "isCheckAllColumnChecked", "ModifyFloorPlanComponent_div_37_tr_3_th_2_Template", "ModifyFloorPlanComponent_div_37_tr_5_td_8_Template_nb_checkbox_checkedChange_4_listener", "house_r8", "CIsSelected", "ModifyFloorPlanComponent_div_37_tr_5_Template_nb_checkbox_checkedChange_5_listener", "row_r6", "enableAllRow", "ModifyFloorPlanComponent_div_37_tr_5_td_8_Template", "isCheckAllRowChecked", "ModifyFloorPlanComponent_div_37_tr_3_Template", "ModifyFloorPlanComponent_div_37_tr_5_Template", "isHouseList", "getListBuilding", "apiHouseGetListBuildingPost$Json", "CBuildingNameSelected", "groupByFloor", "customerData", "groupedData", "uniqueFloors", "from", "Set", "customer", "floor", "floorIndex", "rest", "bodyParam", "flat", "apiHouseEditListHousePost$Json", "mode", "<PERSON><PERSON><PERSON>", "row", "every", "Error", "floorData", "checked", "onOpen", "onNavigateWithId", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "ModifyFloorPlanComponent_Template", "ModifyFloorPlanComponent_Template_nb_select_ngModelChange_10_listener", "ModifyFloorPlanComponent_nb_option_11_Template", "ModifyFloorPlanComponent_Template_input_ngModelChange_17_listener", "ModifyFloorPlanComponent_Template_input_ngModelChange_21_listener", "ModifyFloorPlanComponent_Template_button_click_24_listener", "ModifyFloorPlanComponent_Template_button_click_26_listener", "ModifyFloorPlanComponent_Template_button_click_31_listener", "ModifyFloorPlanComponent_Template_button_click_33_listener", "ModifyFloorPlanComponent_Template_button_click_35_listener", "ModifyFloorPlanComponent_div_37_Template", "ModifyFloorPlanComponent_Template_button_click_41_listener", "ModifyFloorPlanComponent_Template_button_click_43_listener", "ModifyFloorPlanComponent_Template_button_click_45_listener", "ModifyHouseTypeComponent_div_37_tr_3_th_2_Template_nb_checkbox_checkedChange_2_listener", "ModifyHouseTypeComponent_div_37_tr_3_th_2_Template", "ModifyHouseTypeComponent_div_37_tr_5_td_8_nb_checkbox_4_Template_nb_checkbox_checkedChange_0_listener", "CHouseTypeBool", "ModifyHouseTypeComponent_div_37_tr_5_td_8_nb_checkbox_4_Template", "ModifyHouseTypeComponent_div_37_tr_5_Template_nb_checkbox_checkedChange_5_listener", "ModifyHouseTypeComponent_div_37_tr_5_td_8_Template", "ModifyHouseTypeComponent_div_37_tr_3_Template", "ModifyHouseTypeComponent_div_37_tr_5_Template", "custemp", "count", "ModifyHouseTypeComponent_Template", "ModifyHouseTypeComponent_Template_nb_select_ngModelChange_10_listener", "ModifyHouseTypeComponent_nb_option_11_Template", "ModifyHouseTypeComponent_Template_input_ngModelChange_17_listener", "ModifyHouseTypeComponent_Template_input_ngModelChange_21_listener", "ModifyHouseTypeComponent_Template_button_click_24_listener", "ModifyHouseTypeComponent_Template_button_click_26_listener", "ModifyHouseTypeComponent_Template_button_click_31_listener", "ModifyHouseTypeComponent_Template_button_click_33_listener", "ModifyHouseTypeComponent_Template_button_click_35_listener", "ModifyHouseTypeComponent_div_37_Template", "ModifyHouseTypeComponent_Template_button_click_41_listener", "ModifyHouseTypeComponent_Template_button_click_43_listener", "ModifyHouseTypeComponent_Template_button_click_45_listener", "ModifyHouseholdComponent_div_37_tr_3_th_4_div_1_Template_input_ngModelChange_1_listener", "house_r3", "ModifyHouseholdComponent_div_37_tr_3_th_4_div_1_Template_span_click_3_listener", "clearForm", "ModifyHouseholdComponent_div_37_tr_3_th_4_div_1_Template_span_click_5_listener", "idx_r6", "onUpdateAllCol", "ɵɵpureFunction1", "_c0", "ModifyHouseholdComponent_div_37_tr_3_th_4_div_2_Template_span_click_1_listener", "ModifyHouseholdComponent_div_37_tr_3_th_4_div_1_Template", "ModifyHouseholdComponent_div_37_tr_3_th_4_div_2_Template", "ModifyHouseholdComponent_div_37_tr_3_th_4_Template", "houseListItem", "row_r8", "house_r9", "ModifyHouseholdComponent_div_37_tr_5_td_2_div_2_Template_input_ngModelChange_1_listener", "ModifyHouseholdComponent_div_37_tr_5_td_2_div_2_Template_span_click_3_listener", "closeEditItem", "ModifyHouseholdComponent_div_37_tr_5_td_2_div_2_Template_span_click_5_listener", "onUpdateItem", "ModifyHouseholdComponent_div_37_tr_5_td_2_div_3_Template_span_click_1_listener", "_r11", "onEditItem", "ModifyHouseholdComponent_div_37_tr_5_td_2_p_1_Template", "ModifyHouseholdComponent_div_37_tr_5_td_2_div_2_Template", "ModifyHouseholdComponent_div_37_tr_5_td_2_div_3_Template", "ModifyHouseholdComponent_div_37_tr_5_td_1_Template", "ModifyHouseholdComponent_div_37_tr_5_td_2_Template", "ModifyHouseholdComponent_div_37_tr_3_Template", "ModifyHouseholdComponent_div_37_tr_5_Template", "element", "currentCHouseHold", "param", "ModifyHouseholdComponent_Template", "ModifyHouseholdComponent_Template_nb_select_ngModelChange_10_listener", "ModifyHouseholdComponent_nb_option_11_Template", "ModifyHouseholdComponent_Template_input_ngModelChange_17_listener", "ModifyHouseholdComponent_Template_input_ngModelChange_21_listener", "ModifyHouseholdComponent_Template_button_click_24_listener", "ModifyHouseholdComponent_Template_button_click_26_listener", "ModifyHouseholdComponent_Template_button_click_31_listener", "ModifyHouseholdComponent_Template_button_click_33_listener", "ModifyHouseholdComponent_Template_button_click_35_listener", "ModifyHouseholdComponent_div_37_Template", "ModifyHouseholdComponent_Template_button_click_41_listener", "ModifyHouseholdComponent_Template_button_click_43_listener", "ModifyHouseholdComponent_Template_button_click_45_listener", "SampleSelectionResultComponent_tr_27_Template_button_click_12_listener", "item_r4", "CDocumentStatus", "SampleSelectionResultComponent_ng_template_34_tr_34_Template_nb_checkbox_checkedChange_2_listener", "drawing_r8", "isChecked", "CApproveDate", "SampleSelectionResultComponent_ng_template_34_Template_input_ngModelChange_9_listener", "finalDoc", "SampleSelectionResultComponent_ng_template_34_Template_nb_checkbox_checkedChange_13_listener", "SampleSelectionResultComponent_ng_template_34_tr_34_Template", "SampleSelectionResultComponent_ng_template_34_Template_textarea_ngModelChange_40_listener", "SampleSelectionResultComponent_ng_template_34_Template_textarea_ngModelChange_47_listener", "SampleSelectionResultComponent_ng_template_34_Template_button_click_50_listener", "ref_r9", "SampleSelectionResultComponent_ng_template_34_Template_button_click_52_listener", "listSpecialChangeAvailable", "_finalDocumentService", "documentStatusOptions", "houseID", "getListFinalDoc", "apiFinalDocumentGetListFinalDocPost$Json", "getListSpecialChangeAvailable", "apiFinalDocumentGetListSpecialChangeAvailablePost$Json", "getCheckedCIDs", "changeArray", "change", "CSpecialChange", "apiFinalDocumentCreateFinalDocPost$Json", "onOpenModel", "date", "SampleSelectionResultComponent_Template", "SampleSelectionResultComponent_Template_button_click_10_listener", "dialogConfirmImage_r2", "SampleSelectionResultComponent_tr_27_Template", "SampleSelectionResultComponent_Template_ngb_pagination_pageChange_29_listener", "SampleSelectionResultComponent_Template_button_click_32_listener", "SampleSelectionResultComponent_ng_template_34_Template", "building_r2", "i_r4", "StandardHousePlanComponent_tr_29_span_4_Template", "item_r5", "CHouse", "StandardHousePlanComponent_ng_template_32_button_5_Template_button_click_0_listener", "ctx_r8", "submitEditHouseRegularPic", "StandardHousePlanComponent_ng_template_32_div_6_label_13_Template_nb_checkbox_checkedChange_1_listener", "i_r13", "_r12", "CIsSelect", "idx_r14", "checkItem", "StandardHousePlanComponent_ng_template_32_div_6_Template_nb_checkbox_checkedChange_10_listener", "houseRegularPic_r11", "checkAll", "StandardHousePlanComponent_ng_template_32_div_6_label_13_Template", "StandardHousePlanComponent_ng_template_32_div_6_Template_button_click_15_listener", "onDeleteHouseRegularPic", "isAllChecked", "StandardHousePlanComponent_ng_template_32_button_5_Template", "StandardHousePlanComponent_ng_template_32_div_6_Template", "StandardHousePlanComponent_ng_template_32_Template_button_click_8_listener", "StandardHousePlanComponent_ng_template_32_Template_button_click_10_listener", "listHouseRegularPic", "selectedBuilding", "getListHouseRegularPic", "apiHouseGetListHouseRegularPicPost$Json", "houseRegularPic", "CRegularPictureID", "o", "idx", "extractSelectedHouses", "bodyHouseRegularPic", "apiHouseEditHouseRegularPicPost$Json", "CHousePic", "apiHouseDeleteRegularPicturePost$Json", "StandardHousePlanComponent_Template", "StandardHousePlanComponent_Template_nb_select_ngModelChange_10_listener", "StandardHousePlanComponent_nb_option_11_Template", "StandardHousePlanComponent_Template_button_click_14_listener", "StandardHousePlanComponent_Template_button_click_16_listener", "StandardHousePlanComponent_Template_button_click_18_listener", "dialog_r3", "StandardHousePlanComponent_tr_29_Template", "StandardHousePlanComponent_Template_ngb_pagination_pageChange_31_listener", "StandardHousePlanComponent_ng_template_32_Template", "apiQuotationService", "http", "apiUrl", "getQuotationList", "apiQuotationGetListPost$Json", "getQuotationData", "apiQuotationGetDataPost$Json", "apiQuotationGetListByHouseIdPost$Json", "saveQuotationItem", "quotation", "apiQuotationSaveDataPost$Json", "saveRequest", "CShowOther", "COtherName", "COtherPercent", "getDefaultQuotationItems", "post", "rootUrl", "updateQuotationItem", "saveData", "ɵɵinject", "HttpClient", "factory", "ɵfac", "providedIn"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}