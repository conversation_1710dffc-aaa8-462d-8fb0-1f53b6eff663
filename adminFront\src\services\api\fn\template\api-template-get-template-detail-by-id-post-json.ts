/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { GetTemplateDetailByIdArgs } from '../../models/get-template-detail-by-id-args';
import { TemplateDetailItemListResponseBase } from '../../models/template-detail-item-list-response-base';

export interface ApiTemplateGetTemplateDetailByIdPost$Json$Params {
      body?: GetTemplateDetailByIdArgs
}

export function apiTemplateGetTemplateDetailByIdPost$Json(http: HttpClient, rootUrl: string, params?: ApiTemplateGetTemplateDetailByIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<TemplateDetailItemListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiTemplateGetTemplateDetailByIdPost$Json.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'text/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<TemplateDetailItemListResponseBase>;
    })
  );
}

apiTemplateGetTemplateDetailByIdPost$Json.PATH = '/api/Template/GetTemplateDetailById';
