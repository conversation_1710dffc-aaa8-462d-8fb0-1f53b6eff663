# Template Viewer 組件使用說明

## 更新內容

已將 `ModuleType` 改為 `CTemplateType`，其中：
- `1` = 客變需求
- 此值從父元件傳入且不可異動

## 使用方式

### 在父元件中使用

```typescript
// 父元件 TypeScript
export class ParentComponent {
  templateData = [
    { CRequirementID: 1, CRequirement: '廚房改裝', CGroupName: '室內裝修' },
    { CRequirementID: 2, CRequirement: '浴室翻新', CGroupName: '室內裝修' },
    // ... 更多資料
  ];

  onTemplateSelected(template: any) {
    console.log('選中的模板:', template);
  }

  onTemplateViewerClose() {
    // 關閉模板查看器的邏輯
  }
}
```

```html
<!-- 父元件 HTML -->
<app-template-viewer
  [availableData]="templateData"
  [templateType]="1"
  [showOnlyAddForm]="false"
  (selectTemplate)="onTemplateSelected($event)"
  (close)="onTemplateViewerClose()">
</app-template-viewer>
```

## 參數說明

| 參數 | 類型 | 說明 | 預設值 |
|------|------|------|--------|
| `availableData` | `any[]` | 父元件傳入的可選資料 | `[]` |
| `templateType` | `number` | 模板類型 (1=客變需求) | `1` |
| `showOnlyAddForm` | `boolean` | 是否只顯示新增模板表單 | `false` |

## 事件說明

| 事件 | 參數 | 說明 |
|------|------|------|
| `selectTemplate` | `Template` | 當選擇模板時觸發 |
| `close` | `void` | 當關閉組件時觸發 |

## 資料結構

### Template
```typescript
interface Template {
  TemplateID?: number;
  TemplateName: string;
  Description?: string;
}
```

### TemplateDetail
```typescript
interface TemplateDetail {
  TemplateDetailID?: number;
  TemplateID: number;
  RefID: number; // 關聯主檔ID
  CTemplateType: number; // 模板類型，1=客變需求
  FieldName: string;
  FieldValue: string;
}
```

## API 對應

組件已完成以下 API 串接：

1. **POST /api/Template/GetTemplateList** - 獲取模板列表 ✅ 已串接
2. **POST /api/Template/SaveTemplate** - 保存模板 ✅ 已串接
3. **POST /api/Template/DeleteTemplate** - 刪除模板 ✅ 已串接
4. **POST /api/Template/GetTemplateById** - 根據ID獲取模板 (待實作)

## API 使用說明

### SaveTemplate API 請求格式
```typescript
{
  CTemplateId: null,           // 新增時為 null
  CTemplateName: "模板名稱",    // 模板名稱
  CTemplateType: 1,            // 1=客變需求
  CStatus: 1,                  // 啟用狀態
  Details: [                   // 模板詳情陣列
    {
      CTemplateDetailId: null, // 新增時為 null
      CReleateId: 123,         // 關聯主檔ID
      CReleateName: "項目名稱"  // 關聯名稱
    }
  ]
}
```

### GetTemplateList API 請求格式
```typescript
{
  CTemplateType: 1,            // 1=客變需求
  PageIndex: 1,                // 頁碼
  PageSize: 100,               // 每頁筆數
  CTemplateName: null          // 模板名稱篩選 (可選)
}
```

### DeleteTemplate API 請求格式
```typescript
{
  CTemplateId: 123             // 要刪除的模板ID
}
```

## 注意事項

1. `templateType` 參數固定為 `1` (客變需求)，從父元件傳入且不可異動
2. 組件內部已完成所有 API 串接，包含錯誤處理和成功回饋
3. 模板詳情結構已調整為使用 `CTemplateType` 而非 `ModuleType`
4. 所有 API 調用都包含完整的錯誤處理和用戶提示
5. 當 `showOnlyAddForm=true` 時，組件不會載入模板列表，避免不必要的 API 調用
6. 新增模板成功後，只有在非純新增模式下才會重新載入模板列表
