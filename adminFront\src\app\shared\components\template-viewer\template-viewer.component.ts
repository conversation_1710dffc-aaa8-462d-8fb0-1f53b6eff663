import { Component, EventEmitter, Input, Output, OnInit, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NbCardModule, NbButtonModule } from '@nebular/theme';
import { TemplateService } from 'src/services/api/services/template.service';
import { SaveTemplateArgs, SaveTemplateDetailArgs, TemplateGetListArgs, TemplateGetListResponse, GetTemplateByIdArgs } from 'src/services/api/models';

@Component({
  selector: 'app-template-viewer',
  templateUrl: './template-viewer.component.html',
  styleUrls: ['./template-viewer.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule],
})
export class TemplateViewerComponent implements OnInit, OnChanges {
  @Input() availableData: any[] = []; // 父元件傳入的可選資料 (包含關聯主檔ID和名稱)
  @Input() templateType: number = 1; // 模板類型，1=客變需求，從父元件傳入不可異動
  @Input() showOnlyAddForm: boolean = false; // 是否只顯示新增模板表單
  @Output() selectTemplate = new EventEmitter<Template>();
  @Output() close = new EventEmitter<void>(); // 關閉事件

  // 公開Math對象供模板使用
  Math = Math;

  // 內部管理的模板資料
  templates: Template[] = [];
  templateDetails: TemplateDetail[] = [];
  selectedTemplate: Template | null = null;

  // 查詢功能
  searchKeyword = '';
  filteredTemplates: Template[] = [];

  // 分頁功能 - 模板列表
  templatePagination = {
    currentPage: 1,
    pageSize: 10,
    totalItems: 0,
    totalPages: 0
  };
  paginatedTemplates: Template[] = [];

  // 分頁功能 - 模板詳情
  detailPagination = {
    currentPage: 1,
    pageSize: 5,
    totalItems: 0,
    totalPages: 0
  };
  paginatedDetails: TemplateDetail[] = [];

  // 新增模板表單
  showAddForm = false;
  newTemplate = {
    name: '',
    description: '',
    selectedItems: [] as any[]
  };

  constructor(private templateService: TemplateService) { }

  ngOnInit() {
    // 只有在非純新增模式下才載入模板列表
    if (!this.showOnlyAddForm) {
      this.loadTemplates();
      this.updateFilteredTemplates();
    }

    // 如果只顯示新增表單，自動打開新增模板表單
    if (this.showOnlyAddForm) {
      this.onAddTemplate();
    }
  }

  ngOnChanges() {
    this.updateFilteredTemplates();
  }

  // 載入模板列表 - 使用真實的 API 調用
  loadTemplates() {
    // 準備 API 請求參數
    const getTemplateListArgs: TemplateGetListArgs = {
      CTemplateType: this.templateType, // 1=客變需求
      PageIndex: 1, // 暫時載入第一頁
      PageSize: 100, // 暫時載入較多資料
      CTemplateName: null // 不篩選名稱
    };

    console.log('GetTemplateList API 調用:', getTemplateListArgs);

    // 調用 GetTemplateList API
    this.templateService.apiTemplateGetTemplateListPost$Json({
      body: getTemplateListArgs
    }).subscribe({
      next: (response) => {
        if (response.StatusCode === 0 && response.Entries) {
          // API 調用成功
          console.log('模板列表載入成功:', response);

          // 轉換 API 回應為內部格式
          this.templates = response.Entries.map(item => ({
            TemplateID: item.CTemplateId,
            TemplateName: item.CTemplateName || '',
            Description: `建立時間: ${item.CCreateDt ? new Date(item.CCreateDt).toLocaleDateString() : '未知'}`
          }));

          // 更新過濾列表
          this.updateFilteredTemplates();

          // TODO: 如果需要載入模板詳情，可以在這裡調用 GetTemplateById
          this.templateDetails = [];
        } else {
          // API 返回錯誤
          console.error('模板列表載入失敗:', response.Message);
          this.templates = [];
          this.templateDetails = [];
          this.updateFilteredTemplates();
        }
      },
      error: (error) => {
        // HTTP 請求錯誤
        console.error('GetTemplateList API 調用失敗:', error);
        this.templates = [];
        this.templateDetails = [];
        this.updateFilteredTemplates();
      }
    });
  }

  // 更新過濾後的模板列表
  updateFilteredTemplates() {
    if (!this.searchKeyword.trim()) {
      this.filteredTemplates = [...this.templates];
    } else {
      const keyword = this.searchKeyword.toLowerCase();
      this.filteredTemplates = this.templates.filter(template =>
        template.TemplateName.toLowerCase().includes(keyword) ||
        (template.Description && template.Description.toLowerCase().includes(keyword))
      );
    }
    this.updateTemplatePagination();
  }

  // 更新模板分頁
  updateTemplatePagination() {
    this.templatePagination.totalItems = this.filteredTemplates.length;
    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);

    // 確保當前頁面不超過總頁數
    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {
      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);
    }

    this.updatePaginatedTemplates();
  }

  // 更新分頁後的模板列表
  updatePaginatedTemplates() {
    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;
    const endIndex = startIndex + this.templatePagination.pageSize;
    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);
  }

  // 模板分頁導航
  goToTemplatePage(page: number) {
    if (page >= 1 && page <= this.templatePagination.totalPages) {
      this.templatePagination.currentPage = page;
      this.updatePaginatedTemplates();
    }
  }

  // 獲取模板分頁頁碼數組
  getTemplatePageNumbers(): number[] {
    const pages: number[] = [];
    const totalPages = this.templatePagination.totalPages;
    const currentPage = this.templatePagination.currentPage;

    // 顯示當前頁面前後各2頁
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  // 搜尋模板
  onSearch() {
    this.updateFilteredTemplates();
  }

  // 清除搜尋
  clearSearch() {
    this.searchKeyword = '';
    this.updateFilteredTemplates();
  }



  // 顯示新增模板表單
  onAddTemplate() {
    this.showAddForm = true;
    this.newTemplate = {
      name: '',
      description: '',
      selectedItems: []
    };
    // 如果不是只顯示新增表單模式，才重置選擇狀態
    // 在只顯示新增表單模式下，保持父元件傳入的選中狀態
    if (!this.showOnlyAddForm) {
      this.availableData.forEach(item => item.selected = false);
    }
  }

  // 取消新增模板
  cancelAddTemplate() {
    this.showAddForm = false;
    this.newTemplate = {
      name: '',
      description: '',
      selectedItems: []
    };
  }

  // 儲存新模板
  saveNewTemplate() {
    if (!this.newTemplate.name.trim()) {
      alert('請輸入模板名稱');
      return;
    }

    const selectedItems = this.availableData.filter(item => item.selected);
    if (selectedItems.length === 0) {
      alert('請至少選擇一個項目');
      return;
    }

    // TODO: 替換為實際的API調用
    this.createTemplate(this.newTemplate.name, this.newTemplate.description, selectedItems);
  }

  // 創建模板 - 使用真實的 API 調用
  createTemplate(name: string, description: string, selectedItems: any[]) {
    // 準備 API 請求資料
    const saveTemplateArgs: SaveTemplateArgs = {
      CTemplateId: null, // 新增時為 null
      CTemplateName: name.trim(),
      CTemplateType: this.templateType, // 1=客變需求
      CStatus: 1, // 啟用狀態
      Details: selectedItems.map(item => ({
        CTemplateDetailId: null, // 新增時為 null
        CReleateId: this.getRefId(item), // 關聯主檔ID
        CReleateName: this.getFieldName(item) // 關聯名稱
      } as SaveTemplateDetailArgs))
    };

    console.log('SaveTemplate API 調用:', saveTemplateArgs);

    // 調用 SaveTemplate API
    this.templateService.apiTemplateSaveTemplatePost$Json({
      body: saveTemplateArgs
    }).subscribe({
      next: (response) => {
        if (response.StatusCode === 0) {
          // API 調用成功
          console.log('模板創建成功:', response);

          // 只有在非純新增模式下才重新載入模板列表
          if (!this.showOnlyAddForm) {
            this.loadTemplates();
          }

          // 關閉表單
          this.showAddForm = false;
          alert(`模板 "${name}" 已成功創建！包含 ${selectedItems.length} 個項目`);

          // 如果是純新增模式，發出關閉事件通知父組件關閉 dialog
          if (this.showOnlyAddForm) {
            this.close.emit();
          }
        } else {
          // API 返回錯誤
          console.error('模板創建失敗:', response.Message);
          alert(`模板創建失敗：${response.Message || '未知錯誤'}`);
        }
      },
      error: (error) => {
        // HTTP 請求錯誤
        console.error('SaveTemplate API 調用失敗:', error);
        alert('模板創建失敗，請檢查網路連線或聯繫系統管理員');
      }
    });
  }

  // 獲取關聯主檔ID的輔助方法
  private getRefId(item: any): number {
    return item.CRequirementID || item.ID || item.id || 0;
  }

  // 獲取欄位名稱的輔助方法
  private getFieldName(_item?: any): string {
    // 根據模板類型決定欄位名稱
    switch (this.templateType) {
      case 1: // 客變需求
        return 'CRequirement';
      default:
        return 'name';
    }
  }

  // 獲取欄位值的輔助方法
  private getFieldValue(item: any): string {
    return item.CRequirement || item.name || item.title || '';
  }

  // 查看模板
  onSelectTemplate(template: Template) {
    this.selectedTemplate = template;
    this.selectTemplate.emit(template);
    this.updateDetailPagination();
  }

  // 更新詳情分頁
  updateDetailPagination() {
    const details = this.currentTemplateDetails;
    this.detailPagination.totalItems = details.length;
    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);
    this.detailPagination.currentPage = 1; // 重置到第一頁
    this.updatePaginatedDetails();
  }

  // 更新分頁後的詳情列表
  updatePaginatedDetails() {
    const details = this.currentTemplateDetails;
    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;
    const endIndex = startIndex + this.detailPagination.pageSize;
    this.paginatedDetails = details.slice(startIndex, endIndex);
  }

  // 詳情分頁導航
  goToDetailPage(page: number) {
    if (page >= 1 && page <= this.detailPagination.totalPages) {
      this.detailPagination.currentPage = page;
      this.updatePaginatedDetails();
    }
  }

  // 獲取詳情分頁頁碼數組
  getDetailPageNumbers(): number[] {
    const pages: number[] = [];
    const totalPages = this.detailPagination.totalPages;
    const currentPage = this.detailPagination.currentPage;

    // 顯示當前頁面前後各2頁
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  // 關閉模板查看器
  onClose() {
    this.close.emit();
  }

  // 刪除模板
  onDeleteTemplate(templateID: number) {
    if (confirm('確定刪除此模板？')) {
      // TODO: 替換為實際的API調用
      this.deleteTemplateById(templateID);
    }
  }

  // 刪除模板 - 使用真實的 API 調用
  deleteTemplateById(templateID: number) {
    // 準備 API 請求參數
    const deleteTemplateArgs: GetTemplateByIdArgs = {
      CTemplateId: templateID
    };

    console.log('DeleteTemplate API 調用:', deleteTemplateArgs);

    // 調用 DeleteTemplate API
    this.templateService.apiTemplateDeleteTemplatePost$Json({
      body: deleteTemplateArgs
    }).subscribe({
      next: (response) => {
        if (response.StatusCode === 0) {
          // API 調用成功
          console.log('模板刪除成功:', response);

          // 重新載入模板列表
          this.loadTemplates();

          // 如果當前查看的模板被刪除，關閉詳情
          if (this.selectedTemplate?.TemplateID === templateID) {
            this.selectedTemplate = null;
          }

          alert('模板已刪除');
        } else {
          // API 返回錯誤
          console.error('模板刪除失敗:', response.Message);
          alert(`模板刪除失敗：${response.Message || '未知錯誤'}`);
        }
      },
      error: (error) => {
        // HTTP 請求錯誤
        console.error('DeleteTemplate API 調用失敗:', error);
        alert('模板刪除失敗，請檢查網路連線或聯繫系統管理員');
      }
    });
  }

  /*
   * API 設計說明：
   *
   * 1. 載入模板列表 API:
   *    GET /api/Template/GetTemplateList
   *    請求體: { CTemplateType: number, PageIndex: number, PageSize: number }
   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }
   *
   * 2. 創建模板 API:
   *    POST /api/Template/SaveTemplate
   *    請求體: {
   *      CTemplateName: string,
   *      CTemplateType: number,  // 1=客變需求
   *      CStatus: number,
   *      Details: [{
   *        CReleateId: number,        // 關聯主檔ID
   *        CReleateName: string       // 關聯名稱
   *      }]
   *    }
   *
   * 3. 刪除模板 API:
   *    POST /api/Template/DeleteTemplate
   *    請求體: { CTemplateId: number }
   *
   * 資料庫設計重點：
   * - template 表：存放模板基本資訊 (CTemplateId, CTemplateName, CTemplateType, CStatus)
   * - templatedetail 表：存放模板詳情 (CTemplateDetailId, CTemplateId, CReleateId, CReleateName)
   * - CTemplateType 欄位用於區分不同模板類型 (1=客變需求)
   * - CReleateId 存放關聯主檔的ID，CReleateName 存放關聯名稱
   */

  // 關閉模板詳情
  closeTemplateDetail() {
    this.selectedTemplate = null;
  }

  // 取得當前選中模板的詳情
  get currentTemplateDetails(): TemplateDetail[] {
    if (!this.selectedTemplate) {
      return [];
    }
    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);
  }

  // TrackBy函數用於優化ngFor性能
  trackByTemplateId(index: number, template: Template): number {
    return template.TemplateID || index;
  }
}

// DB 對應型別
export interface Template {
  TemplateID?: number;
  TemplateName: string;
  Description?: string;
}
export interface TemplateDetail {
  TemplateDetailID?: number;
  TemplateID: number;
  RefID: number; // 關聯主檔ID
  CTemplateType: number; // 模板類型，1=客變需求
  FieldName: string;
  FieldValue: string;
}
