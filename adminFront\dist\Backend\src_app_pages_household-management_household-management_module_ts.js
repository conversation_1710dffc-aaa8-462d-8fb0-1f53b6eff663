"use strict";
(self["webpackChunkngx_admin_master_v16"] = self["webpackChunkngx_admin_master_v16"] || []).push([["src_app_pages_household-management_household-management_module_ts"],{

/***/ 41098:
/*!**********************************************************!*\
  !*** ./src/app/@theme/pipes/specialChangeSource.pipe.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SpecialChangeSourcePipe: () => (/* binding */ SpecialChangeSourcePipe)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 37580);

class SpecialChangeSourcePipe {
  transform(value) {
    switch (value) {
      case 1:
        return '後台';
      case 2:
        return '前台';
      default:
        return '';
    }
  }
  static {
    this.ɵfac = function SpecialChangeSourcePipe_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || SpecialChangeSourcePipe)();
    };
  }
  static {
    this.ɵpipe = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefinePipe"]({
      name: "specialChangeSource",
      type: SpecialChangeSourcePipe,
      pure: true,
      standalone: true
    });
  }
}

/***/ }),

/***/ 21324:
/*!*******************************************!*\
  !*** ./src/app/models/quotation.model.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CQuotationItemType: () => (/* binding */ CQuotationItemType)
/* harmony export */ });
var CQuotationItemType;
(function (CQuotationItemType) {
  /** 客變需求 */
  CQuotationItemType[CQuotationItemType["\u5BA2\u8B8A\u9700\u6C42"] = 1] = "\u5BA2\u8B8A\u9700\u6C42";
  /** 自定義 */
  CQuotationItemType[CQuotationItemType["\u81EA\u5B9A\u7FA9"] = 2] = "\u81EA\u5B9A\u7FA9";
  /** 選樣 */
  CQuotationItemType[CQuotationItemType["\u9078\u6A23"] = 3] = "\u9078\u6A23";
})(CQuotationItemType || (CQuotationItemType = {}));

/***/ }),

/***/ 26411:
/*!*********************************************************************************************************!*\
  !*** ./src/app/pages/household-management/customer-change-picture/customer-change-picture.component.ts ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CustomerChangePictureComponent: () => (/* binding */ CustomerChangePictureComponent)
/* harmony export */ });
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! moment */ 39545);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _components_base_baseComponent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../components/base/baseComponent */ 6250);
/* harmony import */ var src_app_shared_services_event_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/shared/services/event.service */ 35482);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_shared_helper_allowHelper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/shared/helper/allowHelper */ 39290);
/* harmony import */ var _nebular_theme__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @nebular/theme */ 69375);
/* harmony import */ var src_app_shared_helper_validationHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/app/shared/helper/validationHelper */ 3824);
/* harmony import */ var src_services_api_services__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/services/api/services */ 45654);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var src_app_shared_services_message_service__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/app/shared/services/message.service */ 71029);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _ng_bootstrap_ng_bootstrap__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @ng-bootstrap/ng-bootstrap */ 48418);
/* harmony import */ var _theme_directives_label_directive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../@theme/directives/label.directive */ 48584);
/* harmony import */ var primeng_calendar__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! primeng/calendar */ 41314);
/* harmony import */ var _theme_pipes_specialChangeSource_pipe__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../@theme/pipes/specialChangeSource.pipe */ 41098);

















const _c0 = ["fileInput"];
function CustomerChangePictureComponent_button_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "button", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵlistener"]("click", function CustomerChangePictureComponent_button_9_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r2);
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]();
      const dialogUploadDrawing_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵreference"](35);
      return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"](ctx_r2.addNew(dialogUploadDrawing_r4));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](1, " \u4E0A\u50B3\u5716\u9762");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
  }
}
function CustomerChangePictureComponent_tr_27_button_13_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "button", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵlistener"]("click", function CustomerChangePictureComponent_tr_27_button_13_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r5);
      const item_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]().$implicit;
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]();
      const dialogUploadDrawing_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵreference"](35);
      return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"](ctx_r2.onEdit(dialogUploadDrawing_r4, item_r6));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](1, "\u6AA2\u8996");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
  }
}
function CustomerChangePictureComponent_tr_27_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "tr", 18)(1, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵpipe"](3, "specialChangeSource");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](4, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](6, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](8, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](10, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](11);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](12, "td", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](13, CustomerChangePictureComponent_tr_27_button_13_Template, 2, 0, "button", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const item_r6 = ctx.$implicit;
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵpipeBind1"](3, 6, item_r6.CSource));
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtextInterpolate"](ctx_r2.formatDate(item_r6.CChangeDate));
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtextInterpolate"](item_r6.CDrawingName);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtextInterpolate"](ctx_r2.formatDate(item_r6.CCreateDT));
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtextInterpolate"](item_r6.CIsApprove == null ? "\u5F85\u5BE9\u6838" : item_r6.CIsApprove ? "\u901A\u904E" : "\u99C1\u56DE");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", ctx_r2.isUpdate);
  }
}
function CustomerChangePictureComponent_ng_template_34_button_16_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "button", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵlistener"]("click", function CustomerChangePictureComponent_ng_template_34_button_16_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r8);
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]();
      const inputFile_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵreference"](18);
      return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"](inputFile_r9.click());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](1, "\u9078\u64C7\u6A94\u6848");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
  }
}
function CustomerChangePictureComponent_ng_template_34_div_21_div_1_img_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelement"](0, "img", 46);
  }
  if (rf & 2) {
    const file_r11 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("src", file_r11.data, _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵsanitizeUrl"]);
  }
}
function CustomerChangePictureComponent_ng_template_34_div_21_div_1_span_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "span", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](1, "PDF");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
  }
}
function CustomerChangePictureComponent_ng_template_34_div_21_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "div", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](1, CustomerChangePictureComponent_ng_template_34_div_21_div_1_img_1_Template, 1, 1, "img", 41)(2, CustomerChangePictureComponent_ng_template_34_div_21_div_1_span_2_Template, 2, 0, "span", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](3, "p", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](5, "span", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵlistener"]("click", function CustomerChangePictureComponent_ng_template_34_div_21_div_1_Template_span_click_5_listener() {
      const i_r12 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r10).index;
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"](ctx_r2.removeFile(i_r12));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelement"](6, "i", 45);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const file_r11 = ctx.$implicit;
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", ctx_r2.isImage(file_r11.CFileType));
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", !ctx_r2.isImage(file_r11.CFileType));
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtextInterpolate"](file_r11.CFileName);
  }
}
function CustomerChangePictureComponent_ng_template_34_div_21_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "div", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](1, CustomerChangePictureComponent_ng_template_34_div_21_div_1_Template, 7, 3, "div", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngForOf", ctx_r2.imageUrlList);
  }
}
function CustomerChangePictureComponent_ng_template_34_div_22_div_1_img_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r13 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "img", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵlistener"]("click", function CustomerChangePictureComponent_ng_template_34_div_22_div_1_img_1_Template_img_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r13);
      const file_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]().$implicit;
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"](ctx_r2.openNewTab(file_r14.CFile));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const file_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("src", file_r14.CFile, _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵsanitizeUrl"]);
  }
}
function CustomerChangePictureComponent_ng_template_34_div_22_div_1_span_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r15 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "span", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵlistener"]("click", function CustomerChangePictureComponent_ng_template_34_div_22_div_1_span_2_Template_span_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r15);
      const file_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]().$implicit;
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"](ctx_r2.openNewTab(file_r14.CFile));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](1, "PDF");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
  }
}
function CustomerChangePictureComponent_ng_template_34_div_22_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "div", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](1, CustomerChangePictureComponent_ng_template_34_div_22_div_1_img_1_Template, 1, 1, "img", 48)(2, CustomerChangePictureComponent_ng_template_34_div_22_div_1_span_2_Template, 2, 0, "span", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](3, "p", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const file_r14 = ctx.$implicit;
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", !ctx_r2.isPDFString(file_r14.CFile));
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", ctx_r2.isPDFString(file_r14.CFile));
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtextInterpolate"](file_r14.CFileName);
  }
}
function CustomerChangePictureComponent_ng_template_34_div_22_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "div", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](1, CustomerChangePictureComponent_ng_template_34_div_22_div_1_Template, 5, 3, "div", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngForOf", ctx_r2.SpecialChange.CFileRes);
  }
}
function CustomerChangePictureComponent_ng_template_34_button_30_Template(rf, ctx) {
  if (rf & 1) {
    const _r17 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "button", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵlistener"]("click", function CustomerChangePictureComponent_ng_template_34_button_30_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r17);
      const ref_r16 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]().dialogRef;
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"](ctx_r2.onSaveSpecialChange(ref_r16));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](1, "\u9001\u51FA\u5BE9\u6838");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
  }
}
function CustomerChangePictureComponent_ng_template_34_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "nb-card", 22)(1, "nb-card-header");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](3, "nb-card-body", 23)(4, "div", 24)(5, "label", 25, 1);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](7, " \u8A0E\u8AD6\u65E5\u671F ");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](8, "p-calendar", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtwoWayListener"]("ngModelChange", function CustomerChangePictureComponent_ng_template_34_Template_p_calendar_ngModelChange_8_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r7);
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtwoWayBindingSet"](ctx_r2.formSpecialChange.CChangeDate, $event) || (ctx_r2.formSpecialChange.CChangeDate = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](9, "div", 24)(10, "label", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](11, " \u5716\u9762\u540D\u7A31 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](12, "input", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtwoWayListener"]("ngModelChange", function CustomerChangePictureComponent_ng_template_34_Template_input_ngModelChange_12_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r7);
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtwoWayBindingSet"](ctx_r2.formSpecialChange.CDrawingName, $event) || (ctx_r2.formSpecialChange.CDrawingName = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](13, "div", 24)(14, "label", 29);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](15, " \u9078\u6A23\u7D50\u679C ");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](16, CustomerChangePictureComponent_ng_template_34_button_16_Template, 2, 0, "button", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](17, "input", 30, 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵlistener"]("change", function CustomerChangePictureComponent_ng_template_34_Template_input_change_17_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r7);
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"](ctx_r2.detectFiles($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](19, "div", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelement"](20, "label", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](21, CustomerChangePictureComponent_ng_template_34_div_21_Template, 2, 1, "div", 33)(22, CustomerChangePictureComponent_ng_template_34_div_22_Template, 2, 1, "div", 33);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](23, "div", 31)(24, "label", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](25, "\u5BE9\u6838\u8AAA\u660E");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](26, "textarea", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtwoWayListener"]("ngModelChange", function CustomerChangePictureComponent_ng_template_34_Template_textarea_ngModelChange_26_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r7);
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtwoWayBindingSet"](ctx_r2.formSpecialChange.CApproveRemark, $event) || (ctx_r2.formSpecialChange.CApproveRemark = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](27, "div", 14)(28, "button", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵlistener"]("click", function CustomerChangePictureComponent_ng_template_34_Template_button_click_28_listener() {
      const ref_r16 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r7).dialogRef;
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"](ctx_r2.onClose(ref_r16));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](29, "\u53D6\u6D88");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](30, CustomerChangePictureComponent_ng_template_34_button_30_Template, 2, 0, "button", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtextInterpolate2"](" \u6236\u5225\u7BA1\u7406 > \u6D3D\u8AC7\u7D00\u9304\u4E0A\u50B3 > ", ctx_r2.house.CHousehold, " \u00A0 ", ctx_r2.house.CFloor, "F ");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("appendTo", "CChangeDate")("iconDisplay", "input")("showIcon", true);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtwoWayProperty"]("ngModel", ctx_r2.formSpecialChange.CChangeDate);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("disabled", ctx_r2.isEdit)("showButtonBar", true);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtwoWayProperty"]("ngModel", ctx_r2.formSpecialChange.CDrawingName);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("disabled", ctx_r2.isEdit);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", !(ctx_r2.isEdit && ctx_r2.SpecialChange.CIsApprove === null));
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("disabled", ctx_r2.isEdit);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", !ctx_r2.isEdit);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", ctx_r2.isEdit);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("disabled", ctx_r2.isEdit);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtwoWayProperty"]("ngModel", ctx_r2.formSpecialChange.CApproveRemark);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", !ctx_r2.isEdit);
  }
}
class CustomerChangePictureComponent extends _components_base_baseComponent__WEBPACK_IMPORTED_MODULE_1__.BaseComponent {
  constructor(_allow, dialogService, valid, _specialChangeService, _houseService, route, message, location, _eventService) {
    super(_allow);
    this._allow = _allow;
    this.dialogService = dialogService;
    this.valid = valid;
    this._specialChangeService = _specialChangeService;
    this._houseService = _houseService;
    this.route = route;
    this.message = message;
    this.location = location;
    this._eventService = _eventService;
    this.imageUrlList = [];
    this.isEdit = false;
    this.statusOptions = [{
      value: 0,
      key: 'allow',
      label: '允許'
    }, {
      value: 1,
      key: 'not allowed',
      label: '不允許'
    }];
    this.pageFirst = 1;
    this.pageSize = 10;
    this.pageIndex = 1;
    this.totalRecords = 0;
    this.listPictures = [];
  }
  ngOnInit() {
    this.route.paramMap.subscribe(params => {
      if (params) {
        const idParam = params.get('id1');
        const id = idParam ? +idParam : 0;
        this.buildCaseId = id;
        const idParam2 = params.get('id2');
        const id2 = idParam2 ? +idParam2 : 0;
        this.houseId = id2;
        this.getListSpecialChange();
        this.getHouseById();
      }
    });
  }
  openPdfInNewTab(data) {
    if (data && data.CFileRes.CFile) window.open(data.CFileRes.CFile, '_blank');
  }
  getListSpecialChange() {
    this._specialChangeService.apiSpecialChangeGetListSpecialChangePost$Json({
      body: {
        CHouseId: this.houseId,
        PageIndex: this.pageIndex,
        PageSize: this.pageSize
      }
    }).subscribe(res => {
      if (res.TotalItems && res.Entries && res.StatusCode == 0) {
        this.listSpecialChange = res.Entries ?? [];
        this.totalRecords = res.TotalItems;
      }
    });
  }
  getHouseById() {
    this._houseService.apiHouseGetHouseByIdPost$Json({
      body: {
        CHouseID: this.houseId
      }
    }).subscribe(res => {
      if (res.TotalItems && res.Entries && res.StatusCode == 0) {
        this.house = res.Entries;
        this.houseTitle = `${this.house.CHousehold} ${this.house.CFloor}F`;
      }
    });
  }
  getSpecialChangeById(ref, CSpecialChangeID) {
    this._specialChangeService.apiSpecialChangeGetSpecialChangeByIdPost$Json({
      body: CSpecialChangeID
    }).subscribe(res => {
      if (res.TotalItems && res.Entries && res.StatusCode == 0) {
        this.SpecialChange = res.Entries;
        this.formSpecialChange = {
          CApproveRemark: this.SpecialChange.CApproveRemark,
          CBuildCaseID: this.buildCaseId,
          CDrawingName: this.SpecialChange.CDrawingName,
          CHouseID: this.houseId,
          SpecialChangeFiles: null
        };
        if (this.SpecialChange.CChangeDate) {
          this.formSpecialChange.CChangeDate = new Date(this.SpecialChange.CChangeDate);
        }
        this.dialogService.open(ref);
      }
    });
  }
  onSaveSpecialChange(ref) {
    this.validation();
    if (this.valid.errorMessages.length > 0) {
      this.message.showErrorMSGs(this.valid.errorMessages);
      return;
    }
    this._specialChangeService.apiSpecialChangeSaveSpecialChangePost$Json({
      body: this.formatParam()
    }).subscribe(res => {
      if (res.StatusCode === 0) {
        this.message.showSucessMSG("執行成功");
        this.getListSpecialChange();
        ref.close();
      }
    });
  }
  pageChanged(newPage) {
    this.pageIndex = newPage;
    this.getListSpecialChange();
  }
  addNew(ref) {
    this.imageUrlList = [];
    this.isEdit = false;
    this.formSpecialChange = {
      CApproveRemark: '',
      CBuildCaseID: this.buildCaseId,
      CChangeDate: '',
      CDrawingName: '',
      CHouseID: this.houseId,
      SpecialChangeFiles: null
    };
    this.dialogService.open(ref);
  }
  onEdit(ref, specialChange) {
    this.imageUrlList = [];
    this.isEdit = true;
    this.getSpecialChangeById(ref, specialChange.CSpecialChangeID);
  }
  validation() {
    this.valid.clear();
    this.valid.required('[討論日期]', this.formSpecialChange.CChangeDate);
    this.valid.required('[圖面名稱]', this.formSpecialChange.CDrawingName);
    this.valid.required('[審核說明]', this.formSpecialChange.CApproveRemark);
  }
  formatDate(CChangeDate) {
    if (CChangeDate) {
      return moment__WEBPACK_IMPORTED_MODULE_0__(CChangeDate).format('YYYY-MM-DD');
    }
    return '';
  }
  deleteDataFields(array) {
    for (const item of array) {
      delete item.data;
    }
    return array;
  }
  formatParam() {
    const result = {
      ...this.formSpecialChange,
      SpecialChangeFiles: this.imageUrlList
    };
    this.deleteDataFields(result.SpecialChangeFiles);
    if (this.formSpecialChange.CChangeDate) {
      result.CChangeDate = this.formatDate(this.formSpecialChange.CChangeDate);
    }
    return result;
  }
  onClose(ref) {
    ref.close();
  }
  removeBase64Prefix(base64String) {
    const prefixIndex = base64String.indexOf(",");
    if (prefixIndex !== -1) {
      return base64String.substring(prefixIndex + 1);
    }
    return base64String;
  }
  detectFiles(event) {
    const files = event.target.files;
    if (files && files.length > 0) {
      const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf'];
      const fileRegex = /pdf|jpg|jpeg|png/i;
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        if (!fileRegex.test(file.type)) {
          this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔');
        }
        if (allowedTypes.includes(file.type)) {
          const reader = new FileReader();
          reader.onload = e => {
            const fileType = file.type.startsWith('image/') ? 2 : 1;
            this.imageUrlList.push({
              data: e.target.result,
              CFileBlood: this.removeBase64Prefix(e.target.result),
              CFileName: file.name,
              CFileType: fileType
            });
            if (this.imageUrlList.length === files.length) {
              console.log('this.imageUrlList', this.imageUrlList);
              if (this.fileInput) {
                this.fileInput.nativeElement.value = null;
              }
            }
          };
          reader.readAsDataURL(file);
        }
      }
    }
  }
  isPDFString(str) {
    if (str) {
      return str.toLowerCase().endsWith(".pdf");
    }
    return false;
  }
  isImage(fileType) {
    return fileType === 2;
  }
  isPdf(extension) {
    return extension.toLowerCase() === 'pdf';
  }
  removeFile(index) {
    this.imageUrlList.splice(index, 1);
  }
  removeImage(pictureId) {
    this.listPictures = this.listPictures.filter(x => x.id != pictureId);
  }
  uploadImage(ref) {}
  renameFile(event, index) {
    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);
    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, {
      type: this.listPictures[index].CFile.type
    });
    this.listPictures[index].CFile = newFile;
  }
  goBack() {
    this._eventService.push({
      action: "GET_BUILDCASE" /* EEvent.GET_BUILDCASE */,
      payload: this.buildCaseId
    });
    this.location.back();
  }
  openNewTab(url) {
    if (url) window.open(url, "_blank");
  }
  static {
    this.ɵfac = function CustomerChangePictureComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || CustomerChangePictureComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](src_app_shared_helper_allowHelper__WEBPACK_IMPORTED_MODULE_3__.AllowHelper), _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](_nebular_theme__WEBPACK_IMPORTED_MODULE_10__.NbDialogService), _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](src_app_shared_helper_validationHelper__WEBPACK_IMPORTED_MODULE_4__.ValidationHelper), _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](src_services_api_services__WEBPACK_IMPORTED_MODULE_5__.SpecialChangeService), _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](src_services_api_services__WEBPACK_IMPORTED_MODULE_5__.HouseService), _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_11__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](src_app_shared_services_message_service__WEBPACK_IMPORTED_MODULE_6__.MessageService), _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_12__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](src_app_shared_services_event_service__WEBPACK_IMPORTED_MODULE_2__.EventService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdefineComponent"]({
      type: CustomerChangePictureComponent,
      selectors: [["ngx-customer-change-picture"]],
      viewQuery: function CustomerChangePictureComponent_Query(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵviewQuery"](_c0, 5);
        }
        if (rf & 2) {
          let _t;
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵloadQuery"]()) && (ctx.fileInput = _t.first);
        }
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵInheritDefinitionFeature"]],
      decls: 36,
      vars: 6,
      consts: [["dialogUploadDrawing", ""], ["CChangeDate", ""], ["inputFile", ""], ["accent", "success"], [1, "font-bold", "text-[#818181]"], [1, "d-flex", "flex-wrap"], [1, "col-md-12"], [1, "d-flex", "justify-content-end", "w-full"], ["class", "btn btn-info", 3, "click", 4, "ngIf"], [1, "table-responsive", "mt-4"], [1, "table", "table-striped", "border", 2, "min-width", "1000px", "background-color", "#f3f3f3"], [1, "text-center", 2, "background-color", "#27ae60", "color", "white"], ["scope", "col", 1, "col-1"], ["class", "text-center", 4, "ngFor", "ngForOf"], [1, "d-flex", "justify-content-center"], ["aria-label", "Pagination", 3, "pageChange", "page", "pageSize", "collectionSize"], [1, "btn", "btn-secondary", "btn-sm", 3, "click"], [1, "btn", "btn-info", 3, "click"], [1, "text-center"], [1, "text-center", "w-32"], ["class", "btn btn-outline-primary btn-sm m-1", 3, "click", 4, "ngIf"], [1, "btn", "btn-outline-primary", "btn-sm", "m-1", 3, "click"], [2, "min-width", "600px", "max-height", "95vh"], [1, "px-4"], [1, "form-group"], ["for", "CChangeDate", "baseLabel", "", 1, "required-field", "mr-4", 2, "min-width", "75px"], ["placeholder", "\u5E74/\u6708/\u65E5", "inputId", "icondisplay", "dateFormat", "yy/mm/dd", 1, "!w-[400px]", 3, "ngModelChange", "appendTo", "iconDisplay", "showIcon", "ngModel", "disabled", "showButtonBar"], ["for", "cDrawingName", "baseLabel", "", 1, "required-field", "mr-4", 2, "min-width", "75px"], ["type", "text", "nbInput", "", "placeholder", "\u5716\u9762\u540D\u7A31", 1, "w-full", 3, "ngModelChange", "ngModel", "disabled"], ["for", "cIsEnable", "baseLabel", "", 1, "mr-4", 2, "min-width", "75px"], ["type", "file", "id", "fileInput", "accept", "image/jpeg, image/jpg, application/pdf", "multiple", "", 1, "hidden", 3, "change", "disabled"], [1, "form-group", "d-flex", "align-items-center"], ["baseLabel", "", 1, "align-self-start", "mr-4", 2, "min-width", "75px"], ["class", "flex flex-wrap mt-2", 4, "ngIf"], ["for", "cApproveRemark", "baseLabel", "", 1, "required-field", "align-self-start", "mr-4", 2, "min-width", "75px"], ["name", "remark", "id", "cApproveRemark", "rows", "5", "nbInput", "", 1, "w-full", 2, "resize", "none", 3, "ngModelChange", "disabled", "ngModel"], [1, "btn", "btn-outline-secondary", "m-2", 3, "click"], ["class", "btn btn-success m-2", 3, "click", 4, "ngIf"], [1, "flex", "flex-wrap", "mt-2"], ["class", "relative w-24 h-24 mr-2 mb-2 border", 4, "ngFor", "ngForOf"], [1, "relative", "w-24", "h-24", "mr-2", "mb-2", "border"], ["class", "w-full h-full object-contain cursor-pointer", 3, "src", 4, "ngIf"], ["class", "absolute inset-0 flex items-center justify-center cursor-pointer", 4, "ngIf"], [1, "absolute", "-bottom-4", "left-0", "w-full", "text-xs", "truncate", "px-1", "text-center"], [1, "absolute", "top-0", "right-0", "cursor-pointer", "bg-white", "rounded-full", 3, "click"], [1, "fa", "fa-times", "text-red-600"], [1, "w-full", "h-full", "object-contain", "cursor-pointer", 3, "src"], [1, "absolute", "inset-0", "flex", "items-center", "justify-center", "cursor-pointer"], ["class", "w-full h-full object-contain cursor-pointer", 3, "src", "click", 4, "ngIf"], ["class", "absolute inset-0 flex items-center justify-center cursor-pointer", 3, "click", 4, "ngIf"], [1, "w-full", "h-full", "object-contain", "cursor-pointer", 3, "click", "src"], [1, "absolute", "inset-0", "flex", "items-center", "justify-center", "cursor-pointer", 3, "click"], [1, "btn", "btn-success", "m-2", 3, "click"]],
      template: function CustomerChangePictureComponent_Template(rf, ctx) {
        if (rf & 1) {
          const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵgetCurrentView"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "nb-card", 3)(1, "nb-card-header");
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](3, "nb-card-body")(4, "h1", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](5, "\u60A8\u53EF\u8207\u6B64\u4E0A\u50B3\u8207\u8A72\u6236\u5225\u5BA2\u6236\u8A0E\u8AD6\u7684\u5BA2\u6236\u5716\u9762\uFF0C\u5BE9\u6838\u901A\u904E\u5F8C\u5BA2\u6236\u5C31\u53EF\u4EE5\u5728\u524D\u53F0\u6AA2\u8996\u8A72\u5716\u9762\u3002");
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](6, "div", 5)(7, "div", 6)(8, "div", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](9, CustomerChangePictureComponent_button_9_Template, 2, 0, "button", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](10, "div", 9)(11, "table", 10)(12, "thead")(13, "tr", 11)(14, "th", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](15, "\u4F86\u6E90");
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](16, "th", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](17, "\u8A0E\u8AD6\u65E5\u671F");
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](18, "th", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](19, "\u5716\u9762\u540D\u7A31");
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](20, "th", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](21, "\u4E0A\u50B3\u65E5\u671F");
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](22, "th", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](23, "\u5BE9\u6838\u72C0\u614B");
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](24, "th", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](25, "\u64CD\u4F5C");
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](26, "tbody");
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](27, CustomerChangePictureComponent_tr_27_Template, 14, 8, "tr", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](28, "nb-card-footer", 14)(29, "ngb-pagination", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtwoWayListener"]("pageChange", function CustomerChangePictureComponent_Template_ngb_pagination_pageChange_29_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r1);
            _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtwoWayBindingSet"](ctx.pageIndex, $event) || (ctx.pageIndex = $event);
            return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"]($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵlistener"]("pageChange", function CustomerChangePictureComponent_Template_ngb_pagination_pageChange_29_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r1);
            return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"](ctx.pageChanged($event));
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](30, "nb-card-footer")(31, "div", 14)(32, "button", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵlistener"]("click", function CustomerChangePictureComponent_Template_button_click_32_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r1);
            return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"](ctx.goBack());
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](33, " \u8FD4\u56DE\u4E0A\u4E00\u9801 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](34, CustomerChangePictureComponent_ng_template_34_Template, 31, 17, "ng-template", null, 0, _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplateRefExtractor"]);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtextInterpolate1"](" \u6236\u5225\u7BA1\u7406 > \u6D3D\u8AC7\u7D00\u9304\u4E0A\u50B3 > ", ctx.houseTitle, " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", ctx.isCreate);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](18);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngForOf", ctx.listSpecialChange);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtwoWayProperty"]("page", ctx.pageIndex);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("pageSize", ctx.pageSize)("collectionSize", ctx.totalRecords);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_12__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_12__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_13__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_13__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_13__.NgModel, _nebular_theme__WEBPACK_IMPORTED_MODULE_10__.NbCardComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_10__.NbCardBodyComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_10__.NbCardFooterComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_10__.NbCardHeaderComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_10__.NbInputDirective, _ng_bootstrap_ng_bootstrap__WEBPACK_IMPORTED_MODULE_14__.NgbPagination, _theme_directives_label_directive__WEBPACK_IMPORTED_MODULE_7__.BaseLabelDirective, primeng_calendar__WEBPACK_IMPORTED_MODULE_15__.Calendar, _theme_pipes_specialChangeSource_pipe__WEBPACK_IMPORTED_MODULE_8__.SpecialChangeSourcePipe],
      styles: ["#icondisplay {\n  width: 318px;\n}\n\n  [id^=pn_id_] {\n  z-index: 10;\n}\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImN1c3RvbWVyLWNoYW5nZS1waWN0dXJlLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksWUFBQTtBQUNKOztBQUVBO0VBQ0ksV0FBQTtBQUNKIiwiZmlsZSI6ImN1c3RvbWVyLWNoYW5nZS1waWN0dXJlLmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiOjpuZy1kZWVwICNpY29uZGlzcGxheSB7XHJcbiAgICB3aWR0aDogMzE4cHg7XHJcbn1cclxuXHJcbjo6bmctZGVlcCBbaWRePVwicG5faWRfXCJdIHtcclxuICAgIHotaW5kZXg6IDEwO1xyXG59XHJcbiJdfQ== */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvY3VzdG9tZXItY2hhbmdlLXBpY3R1cmUvY3VzdG9tZXItY2hhbmdlLXBpY3R1cmUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSxZQUFBO0FBQ0o7O0FBRUE7RUFDSSxXQUFBO0FBQ0o7QUFDQSw0ZEFBNGQiLCJzb3VyY2VzQ29udGVudCI6WyI6Om5nLWRlZXAgI2ljb25kaXNwbGF5IHtcclxuICAgIHdpZHRoOiAzMThweDtcclxufVxyXG5cclxuOjpuZy1kZWVwIFtpZF49XCJwbl9pZF9cIl0ge1xyXG4gICAgei1pbmRleDogMTA7XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 16513:
/*!***********************************************************************************************************!*\
  !*** ./src/app/pages/household-management/finaldochouse-management/finaldochouse-management.component.ts ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FinaldochouseManagementComponent: () => (/* binding */ FinaldochouseManagementComponent)
/* harmony export */ });
/* harmony import */ var _nebular_theme__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @nebular/theme */ 69375);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _components_pagination_pagination_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../components/pagination/pagination.component */ 40534);
/* harmony import */ var _components_base_baseComponent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../components/base/baseComponent */ 6250);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! moment */ 39545);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _fullcalendar_angular__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @fullcalendar/angular */ 87997);
/* harmony import */ var _fullcalendar_interaction__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @fullcalendar/interaction */ 19426);
/* harmony import */ var _fullcalendar_daygrid__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @fullcalendar/daygrid */ 53279);
/* harmony import */ var _fullcalendar_timegrid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @fullcalendar/timegrid */ 76863);
/* harmony import */ var _fullcalendar_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @fullcalendar/list */ 64342);
/* harmony import */ var _fullcalendar_bootstrap__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @fullcalendar/bootstrap */ 30820);
/* harmony import */ var primeng_calendar__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! primeng/calendar */ 41314);
/* harmony import */ var src_app_shared_services_event_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/shared/services/event.service */ 35482);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_shared_helper_allowHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/app/shared/helper/allowHelper */ 39290);
/* harmony import */ var src_app_shared_helper_enumHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/app/shared/helper/enumHelper */ 85654);
/* harmony import */ var src_app_shared_services_message_service__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/app/shared/services/message.service */ 71029);
/* harmony import */ var src_app_shared_helper_validationHelper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/app/shared/helper/validationHelper */ 3824);
/* harmony import */ var src_services_api_services__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! src/services/api/services */ 45654);
/* harmony import */ var src_app_shared_helper_petternHelper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! src/app/shared/helper/petternHelper */ 2437);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @angular/router */ 95072);



























const _c0 = ["calendar"];
function FinaldochouseManagementComponent_tr_36_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](0, "tr", 26)(1, "td", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](3, "td", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](5, "td", 29)(6, "button", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵlistener"]("click", function FinaldochouseManagementComponent_tr_36_Template_button_click_6_listener() {
      const data_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r3).$implicit;
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"](ctx_r4.openPdfInNewTab(data_r4));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](7, "\u9023\u7D50");
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const data_r4 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtextInterpolate"](data_r4.CDocumentName);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtextInterpolate"](data_r4.CSignDate);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("disabled", !data_r4.CFileAfter && !data_r4.CFileBefore);
  }
}
function FinaldochouseManagementComponent_ng_template_42_div_13_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](0, "div", 52)(1, "span", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](3, "button", 54);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵlistener"]("click", function FinaldochouseManagementComponent_ng_template_42_div_13_Template_button_click_3_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r7);
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"](ctx_r4.clearFile());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](4, "i", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtextInterpolate"](ctx_r4.fileName);
  }
}
function FinaldochouseManagementComponent_ng_template_42_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](0, "nb-card", 31)(1, "nb-card-header");
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](3, "nb-card-body", 32)(4, "div", 33)(5, "div", 34)(6, "label", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](7, "\u6587\u4EF6 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](8, "div", 36)(9, "input", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵlistener"]("change", function FinaldochouseManagementComponent_ng_template_42_Template_input_change_9_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r6);
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"](ctx_r4.onFileSelected($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](10, "label", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](11, "i", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](12, " \u4E0A\u50B3 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtemplate"](13, FinaldochouseManagementComponent_ng_template_42_div_13_Template, 5, 1, "div", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](14, "div", 41)(15, "label", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](16, " \u6587\u4EF6\u540D\u7A31 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](17, "input", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtwoWayListener"]("ngModelChange", function FinaldochouseManagementComponent_ng_template_42_Template_input_ngModelChange_17_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r6);
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵnextContext"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtwoWayBindingSet"](ctx_r4.CDocumentName, $event) || (ctx_r4.CDocumentName = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](18, "div", 44)(19, "label", 45);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](20, "\u9001\u5BE9\u8CC7\u8A0A ");
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](21, "p", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](22, "\u5167\u90E8\u5BE9\u6838\u4EBA\u54E1\u67E5\u770B");
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](23, "textarea", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtwoWayListener"]("ngModelChange", function FinaldochouseManagementComponent_ng_template_42_Template_textarea_ngModelChange_23_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r6);
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵnextContext"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtwoWayBindingSet"](ctx_r4.CApproveRemark, $event) || (ctx_r4.CApproveRemark = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](24, "        ");
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](25, "div", 44)(26, "label", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](27, "\u6458\u8981\u8A3B\u8A18 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](28, "p", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](29, "\u5BA2\u6236\u65BC\u6587\u4EF6\u4E2D\u67E5\u770B");
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](30, "textarea", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtwoWayListener"]("ngModelChange", function FinaldochouseManagementComponent_ng_template_42_Template_textarea_ngModelChange_30_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r6);
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵnextContext"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtwoWayBindingSet"](ctx_r4.CNote, $event) || (ctx_r4.CNote = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](31, "        ");
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](32, "nb-card-footer", 24)(33, "button", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵlistener"]("click", function FinaldochouseManagementComponent_ng_template_42_Template_button_click_33_listener() {
      const ref_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r6).dialogRef;
      return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"](ref_r8.close());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](34, "\u53D6\u6D88");
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](35, "button", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵlistener"]("click", function FinaldochouseManagementComponent_ng_template_42_Template_button_click_35_listener() {
      const ref_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r6).dialogRef;
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"](ctx_r4.onCreateFinalDoc(ref_r8));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](36, "\u78BA\u8A8D\u9001\u51FA\u5BE9\u6838");
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtextInterpolate2"](" \u6236\u5225\u7BA1\u7406 > \u7C3D\u7F72\u6587\u4EF6\u6B77\u7A0B > ", ctx_r4.houseByID.CHousehold, " ", ctx_r4.houseByID.CFloor, "F ");
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](11);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("ngIf", ctx_r4.fileName);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtwoWayProperty"]("ngModel", ctx_r4.CDocumentName);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtwoWayProperty"]("ngModel", ctx_r4.CApproveRemark);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtwoWayProperty"]("ngModel", ctx_r4.CNote);
  }
}
class FinaldochouseManagementComponent extends _components_base_baseComponent__WEBPACK_IMPORTED_MODULE_1__.BaseComponent {
  constructor(_allow, enumHelper, dialogService, message, valid, finalDocumentService, pettern, router, route, destroyref, _eventService, location, _houseService) {
    super(_allow);
    this._allow = _allow;
    this.enumHelper = enumHelper;
    this.dialogService = dialogService;
    this.message = message;
    this.valid = valid;
    this.finalDocumentService = finalDocumentService;
    this.pettern = pettern;
    this.router = router;
    this.route = route;
    this.destroyref = destroyref;
    this._eventService = _eventService;
    this.location = location;
    this._houseService = _houseService;
    this.calendarOptions = {
      plugins: [_fullcalendar_interaction__WEBPACK_IMPORTED_MODULE_11__["default"], _fullcalendar_daygrid__WEBPACK_IMPORTED_MODULE_12__["default"], _fullcalendar_timegrid__WEBPACK_IMPORTED_MODULE_13__["default"], _fullcalendar_list__WEBPACK_IMPORTED_MODULE_14__["default"], _fullcalendar_timegrid__WEBPACK_IMPORTED_MODULE_13__["default"], _fullcalendar_bootstrap__WEBPACK_IMPORTED_MODULE_15__["default"]],
      locale: 'zh-tw',
      headerToolbar: {
        left: 'prev',
        center: 'title',
        right: 'next'
      }
    };
    this.file = null;
    // request
    this.getListFinalDocRequest = {};
    this.uploadFinaldocRequest = {};
    // response
    this.listFinalDoc = [];
    this.maxDate = new Date();
  }
  ngOnInit() {
    this.route.paramMap.subscribe(params => {
      if (params) {
        const idParam = params.get('id1');
        const id = idParam ? +idParam : 0;
        this.buildCaseId = id;
        const idParam2 = params.get('id2');
        const id2 = idParam2 ? +idParam2 : 0;
        this.currentHouseID = id2;
        this.getList();
        this.getHouseById();
      }
    });
  }
  addNew(ref) {
    this.CApproveRemark = null;
    this.CDocumentName = null;
    this.CNote = null;
    this.file = null;
    this.dialogService.open(ref);
  }
  openPdfInNewTab(data) {
    if (data) {
      if (data.CSignDate && data.CSign) {
        window.open(data.CFileAfter, '_blank');
      } else {
        window.open(data.CFileBefore, '_blank');
      }
    }
  }
  goBack() {
    this._eventService.push({
      action: "GET_BUILDCASE" /* EEvent.GET_BUILDCASE */,
      payload: this.buildCaseId
    });
    this.location.back();
  }
  onFileSelected(event) {
    const file = event.target.files[0];
    const fileRegex = /pdf/i;
    if (!fileRegex.test(file.type)) {
      this.message.showErrorMSG('文件格式不正確，僅允許 pdf 文件');
      return;
    }
    if (file) {
      const allowedTypes = ['application/pdf'];
      if (allowedTypes.includes(file.type)) {
        this.fileName = file.name;
        this.file = file;
      }
    }
  }
  clearFile() {
    if (this.file) {
      this.file = null;
      this.fileName = null;
    }
  }
  getHouseById() {
    this._houseService.apiHouseGetHouseByIdPost$Json({
      body: {
        CHouseID: this.currentHouseID
      }
    }).subscribe(res => {
      if (res.Entries && res.StatusCode == 0) {
        this.houseByID = res.Entries;
      }
    });
  }
  getList() {
    this.getListFinalDocRequest.PageSize = this.pageSize;
    this.getListFinalDocRequest.PageIndex = this.pageIndex;
    if (this.currentHouseID != 0) {
      this.getListFinalDocRequest.CHouseID = this.currentHouseID;
      this.finalDocumentService.apiFinalDocumentGetListFinalDocByHousePost$Json({
        body: this.getListFinalDocRequest
      }).pipe().subscribe(res => {
        if (res.StatusCode == 0) {
          if (res.Entries) {
            this.listFinalDoc = res.Entries;
            this.totalRecords = res.TotalItems;
            if (this.listFinalDoc) {
              for (let i = 0; i < this.listFinalDoc.length; i++) {
                if (this.listFinalDoc[i].CSignDate) this.listFinalDoc[i].CSignDate = moment__WEBPACK_IMPORTED_MODULE_2__(this.listFinalDoc[i].CSignDate).format("yyyy/MM/DD H:mm:ss");
              }
            }
          }
        }
      });
    }
  }
  onCreateFinalDoc(ref) {
    this.validation();
    if (this.valid.errorMessages.length > 0) {
      this.message.showErrorMSGs(this.valid.errorMessages);
      return;
    }
    this.finalDocumentService.apiFinalDocumentUploadFinalDocPost$Json({
      body: {
        CHouseID: this.currentHouseID,
        CBuildCaseID: this.buildCaseId,
        CDocumentName: this.CDocumentName,
        CApproveRemark: this.CApproveRemark,
        CNote: this.CNote,
        CFile: this.file
      }
    }).subscribe(res => {
      if (res.Entries && res.StatusCode == 0) {
        this.getList();
        this.message.showSucessMSG("執行成功");
        ref.close();
      } else {
        this.message.showErrorMSG(res.Message);
      }
    });
  }
  convertToBlob(data, mimeType = 'application/octet-stream') {
    if (data instanceof ArrayBuffer) {
      return new Blob([data], {
        type: mimeType
      });
    } else if (typeof data === 'string') {
      return new Blob([data], {
        type: mimeType
      });
    } else {
      return undefined;
    }
  }
  validation() {
    this.valid.clear();
    this.valid.required('[文件格式不正確]', this.file);
    this.valid.required('[文件名稱]', this.CDocumentName);
    this.valid.required('[送審資訊]', this.CApproveRemark);
    this.valid.required('[系統操作說明]', this.CNote);
  }
  static {
    this.ɵfac = function FinaldochouseManagementComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || FinaldochouseManagementComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](src_app_shared_helper_allowHelper__WEBPACK_IMPORTED_MODULE_4__.AllowHelper), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](src_app_shared_helper_enumHelper__WEBPACK_IMPORTED_MODULE_5__.EnumHelper), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](_nebular_theme__WEBPACK_IMPORTED_MODULE_16__.NbDialogService), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](src_app_shared_services_message_service__WEBPACK_IMPORTED_MODULE_6__.MessageService), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](src_app_shared_helper_validationHelper__WEBPACK_IMPORTED_MODULE_7__.ValidationHelper), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](src_services_api_services__WEBPACK_IMPORTED_MODULE_8__.FinalDocumentService), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](src_app_shared_helper_petternHelper__WEBPACK_IMPORTED_MODULE_9__.PetternHelper), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_17__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_17__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](_angular_core__WEBPACK_IMPORTED_MODULE_10__.DestroyRef), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](src_app_shared_services_event_service__WEBPACK_IMPORTED_MODULE_3__.EventService), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_18__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](src_services_api_services__WEBPACK_IMPORTED_MODULE_8__.HouseService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdefineComponent"]({
      type: FinaldochouseManagementComponent,
      selectors: [["app-finaldochouse-management"]],
      viewQuery: function FinaldochouseManagementComponent_Query(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵviewQuery"](_c0, 5);
        }
        if (rf & 2) {
          let _t;
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵloadQuery"]()) && (ctx.calendarComponent = _t.first);
        }
      },
      standalone: true,
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵInheritDefinitionFeature"], _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵStandaloneFeature"]],
      decls: 44,
      vars: 10,
      consts: [["dialogUploadFinaldoc", ""], ["accent", "success"], [2, "font-size", "32px"], [1, "bg-white"], [1, "col-12"], [1, "row"], [1, "flex", "form-group", "col-12", "col-md-9", "text-right"], ["for", "date-select1", 1, "mr-3", "mt-2"], ["placeholder", "\u5E74/\u6708/\u65E5", "dateFormat", "yy/mm/dd", 3, "ngModelChange", "appendTo", "ngModel", "maxDate"], ["for", "date-select1", 1, "mr-1", "ml-1", "mt-2"], [1, "form-group", "col-12", "col-md-3", "text-right"], [1, "btn", "btn-info", "mr-2", 3, "click"], [1, "fas", "fa-search", "mr-1"], [1, "col-md-12"], [1, "d-flex", "justify-content-end", "w-full"], [1, "btn", "btn-info", 3, "click"], [1, "table-responsive"], [1, "table", "table-striped", "border", 2, "min-width", "800px", "background-color", "#f3f3f3"], [1, "d-flex", "text-white", 2, "background-color", "#27ae60"], ["scope", "col", 1, "col-5"], ["scope", "col", 1, "col-4"], ["scope", "col", 1, "col-3"], ["class", "d-flex", 4, "ngFor", "ngForOf"], [3, "PageChange", "CollectionSize", "Page", "PageSize"], [1, "d-flex", "justify-content-center"], [1, "btn", "btn-secondary", "btn-sm", 3, "click"], [1, "d-flex"], [1, "col-5"], [1, "col-4"], [1, "col-3"], [1, "btn", "btn-outline-primary", "btn-sm", "m-1", 3, "click", "disabled"], [2, "width", "1000px", "max-height", "95vh"], [1, "px-4"], [1, "form-group", "d-flex", "align-items-baseline"], [1, "d-flex", "flex-col", "col-3"], ["for", "file", "baseLabel", "", 1, "required-field", "align-self-start", 2, "min-width", "100px", "position", "static"], [1, "flex", "flex-col", "items-start", "space-y-4"], ["type", "file", "id", "fileInput", "accept", "image/jpeg, image/jpg, application/pdf", 1, "hidden", 2, "display", "none", 3, "change"], ["for", "fileInput", 1, "cursor-pointer", "bg-blue-500", "hover:bg-blue-700", "text-white", "font-bold", "py-2", "px-4", "rounded"], [1, "fa-solid", "fa-cloud-arrow-up", "mr-2"], ["class", "flex items-center space-x-2", 4, "ngIf"], [1, "form-group"], ["for", "CDocumentName", "baseLabel", "", 1, "required-field", "align-self-start", "col-3", 2, "min-width", "75px"], ["type", "text", "nbInput", "", "placeholder", "\u6587\u4EF6\u540D\u7A31", 1, "w-full", 3, "ngModelChange", "ngModel"], [1, "form-group", "d-flex", "align-items-center"], ["for", "remark", "baseLabel", "", 1, "required-field", "align-self-start", "col-3"], [2, "color", "red"], ["name", "remark", "id", "remark", "rows", "5", "nbInput", "", 1, "w-full", 2, "resize", "none", "max-width", "none", 3, "ngModelChange", "ngModel"], ["for", "CNote", "baseLabel", "", 1, "required-field", "align-self-start", "col-3"], ["name", "CNote", "id", "CNote", "rows", "5", "nbInput", "", 1, "w-full", 2, "resize", "none", "max-width", "none", 3, "ngModelChange", "ngModel"], [1, "btn", "btn-outline-secondary", "m-2", 3, "click"], [1, "btn", "btn-success", "m-2", 3, "click"], [1, "flex", "items-center", "space-x-2"], [1, "text-gray-600"], ["type", "button", 1, "text-red-500", "hover:text-red-700", 3, "click"], [1, "fa-solid", "fa-trash"]],
      template: function FinaldochouseManagementComponent_Template(rf, ctx) {
        if (rf & 1) {
          const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵgetCurrentView"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](0, "nb-card", 1)(1, "nb-card-header")(2, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](3, "\u6236\u5225\u7BA1\u7406 / \u7C3D\u7F72\u6587\u4EF6\u6B77\u7A0B");
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](4, "nb-card-body", 3)(5, "div", 4)(6, "div", 5)(7, "div", 6)(8, "span", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](9, " \u5EFA\u7ACB\u6642\u9593 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](10, "p-calendar", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtwoWayListener"]("ngModelChange", function FinaldochouseManagementComponent_Template_p_calendar_ngModelChange_10_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r1);
            _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtwoWayBindingSet"](ctx.getListFinalDocRequest.CDateStart, $event) || (ctx.getListFinalDocRequest.CDateStart = $event);
            return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"]($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](11, "span", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](12, "~");
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](13, "p-calendar", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtwoWayListener"]("ngModelChange", function FinaldochouseManagementComponent_Template_p_calendar_ngModelChange_13_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r1);
            _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtwoWayBindingSet"](ctx.getListFinalDocRequest.CDateEnd, $event) || (ctx.getListFinalDocRequest.CDateEnd = $event);
            return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"]($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](14, "div", 10)(15, "button", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵlistener"]("click", function FinaldochouseManagementComponent_Template_button_click_15_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r1);
            return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"](ctx.getList());
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](16, "i", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](17, "\u67E5\u8A62");
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](18, "div", 5)(19, "div", 13)(20, "div", 14)(21, "button", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵlistener"]("click", function FinaldochouseManagementComponent_Template_button_click_21_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r1);
            const dialogUploadFinaldoc_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵreference"](43);
            return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"](ctx.addNew(dialogUploadFinaldoc_r2));
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](22, " \u65B0\u589E\u6587\u6A94 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()()()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](23, "nb-card-body", 3)(24, "div", 4)(25, "div", 16)(26, "table", 17)(27, "thead")(28, "tr", 18)(29, "th", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](30, "\u6587\u4EF6\u540D\u7A31");
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](31, "th", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](32, "\u5BA2\u6236\u7C3D\u540D\u6642\u9593");
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](33, "th", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](34, "\u9023\u7D50");
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](35, "tbody");
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtemplate"](36, FinaldochouseManagementComponent_tr_36_Template, 8, 3, "tr", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](37, "ngx-pagination", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtwoWayListener"]("PageChange", function FinaldochouseManagementComponent_Template_ngx_pagination_PageChange_37_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r1);
            _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtwoWayBindingSet"](ctx.pageIndex, $event) || (ctx.pageIndex = $event);
            return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"]($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵlistener"]("PageChange", function FinaldochouseManagementComponent_Template_ngx_pagination_PageChange_37_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r1);
            return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"](ctx.getList());
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](38, "nb-card-footer")(39, "div", 24)(40, "button", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵlistener"]("click", function FinaldochouseManagementComponent_Template_button_click_40_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r1);
            return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"](ctx.goBack());
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](41, " \u8FD4\u56DE\u4E0A\u4E00\u9801 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtemplate"](42, FinaldochouseManagementComponent_ng_template_42_Template, 37, 6, "ng-template", null, 0, _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtemplateRefExtractor"]);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("appendTo", "body");
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtwoWayProperty"]("ngModel", ctx.getListFinalDocRequest.CDateStart);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("maxDate", ctx.maxDate);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("appendTo", "body");
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtwoWayProperty"]("ngModel", ctx.getListFinalDocRequest.CDateEnd);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("maxDate", ctx.maxDate);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](23);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("ngForOf", ctx.listFinalDoc);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("CollectionSize", ctx.totalRecords);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtwoWayProperty"]("Page", ctx.pageIndex);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("PageSize", ctx.pageSize);
        }
      },
      dependencies: [_nebular_theme__WEBPACK_IMPORTED_MODULE_16__.NbCardModule, _nebular_theme__WEBPACK_IMPORTED_MODULE_16__.NbCardComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_16__.NbCardBodyComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_16__.NbCardFooterComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_16__.NbCardHeaderComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_16__.NbInputModule, _nebular_theme__WEBPACK_IMPORTED_MODULE_16__.NbInputDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_19__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_19__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_19__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_19__.NgModel, _nebular_theme__WEBPACK_IMPORTED_MODULE_16__.NbSelectModule, _nebular_theme__WEBPACK_IMPORTED_MODULE_16__.NbOptionModule, _angular_common__WEBPACK_IMPORTED_MODULE_18__.NgIf, _angular_common__WEBPACK_IMPORTED_MODULE_18__.NgFor, _components_pagination_pagination_component__WEBPACK_IMPORTED_MODULE_0__.PaginationComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_16__.NbCheckboxModule, _fullcalendar_angular__WEBPACK_IMPORTED_MODULE_20__.FullCalendarModule, primeng_calendar__WEBPACK_IMPORTED_MODULE_21__.CalendarModule, primeng_calendar__WEBPACK_IMPORTED_MODULE_21__.Calendar],
      styles: ["/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJmaW5hbGRvY2hvdXNlLW1hbmFnZW1lbnQuY29tcG9uZW50LnNjc3MifQ== */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvZmluYWxkb2Nob3VzZS1tYW5hZ2VtZW50L2ZpbmFsZG9jaG91c2UtbWFuYWdlbWVudC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsNExBQTRMIiwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ }),

/***/ 60743:
/*!***********************************************************************************!*\
  !*** ./src/app/pages/household-management/household-management-routing.module.ts ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HouseholdRoutingModule: () => (/* binding */ HouseholdRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _household_management_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./household-management.component */ 78503);
/* harmony import */ var _customer_change_picture_customer_change_picture_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./customer-change-picture/customer-change-picture.component */ 26411);
/* harmony import */ var _sample_selection_result_sample_selection_result_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sample-selection-result/sample-selection-result.component */ 95099);
/* harmony import */ var _modify_floor_plan_modify_floor_plan_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./modify-floor-plan/modify-floor-plan.component */ 65567);
/* harmony import */ var _modify_household_modify_household_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./modify-household/modify-household.component */ 90213);
/* harmony import */ var _modify_house_type_modify_house_type_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./modify-house-type/modify-house-type.component */ 55687);
/* harmony import */ var _standard_house_plan_standard_house_plan_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./standard-house-plan/standard-house-plan.component */ 43955);
/* harmony import */ var _finaldochouse_management_finaldochouse_management_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./finaldochouse-management/finaldochouse-management.component */ 16513);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/core */ 37580);











const routes = [{
  path: '',
  component: _household_management_component__WEBPACK_IMPORTED_MODULE_0__.HouseholdManagementComponent
}, {
  path: "customer-change-picture/:id1/:id2",
  component: _customer_change_picture_customer_change_picture_component__WEBPACK_IMPORTED_MODULE_1__.CustomerChangePictureComponent
}, {
  path: "sample-selection-result/:id1/:id2",
  component: _sample_selection_result_sample_selection_result_component__WEBPACK_IMPORTED_MODULE_2__.SampleSelectionResultComponent
}, {
  path: "modify-floor-plan/:id",
  component: _modify_floor_plan_modify_floor_plan_component__WEBPACK_IMPORTED_MODULE_3__.ModifyFloorPlanComponent
}, {
  path: "modify-household/:id",
  component: _modify_household_modify_household_component__WEBPACK_IMPORTED_MODULE_4__.ModifyHouseholdComponent
}, {
  path: "modify-house-type/:id",
  component: _modify_house_type_modify_house_type_component__WEBPACK_IMPORTED_MODULE_5__.ModifyHouseTypeComponent
}, {
  path: "standard-house-plan/:id",
  component: _standard_house_plan_standard_house_plan_component__WEBPACK_IMPORTED_MODULE_6__.StandardHousePlanComponent
}, {
  path: "finaldochouse_management/:id1/:id2",
  component: _finaldochouse_management_finaldochouse_management_component__WEBPACK_IMPORTED_MODULE_7__.FinaldochouseManagementComponent
}];
class HouseholdRoutingModule {
  static {
    this.ɵfac = function HouseholdRoutingModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || HouseholdRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdefineNgModule"]({
      type: HouseholdRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_9__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_9__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵsetNgModuleScope"](HouseholdRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_9__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_9__.RouterModule]
  });
})();

/***/ }),

/***/ 78503:
/*!******************************************************************************!*\
  !*** ./src/app/pages/household-management/household-management.component.ts ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HouseholdManagementComponent: () => (/* binding */ HouseholdManagementComponent)
/* harmony export */ });
/* harmony import */ var C_Users_kenny_Documents_salechange_product_adminFront_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 89204);
/* harmony import */ var _components_shared_module__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/shared.module */ 12239);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var src_assets_template_quotation_template__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/assets/template/quotation-template */ 96634);
/* harmony import */ var _nebular_theme__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @nebular/theme */ 69375);
/* harmony import */ var _components_base_baseComponent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/base/baseComponent */ 6250);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! rxjs */ 98764);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! rxjs */ 51903);
/* harmony import */ var _nebular_date_fns__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @nebular/date-fns */ 25031);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! moment */ 39545);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var src_app_shared_services_event_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/app/shared/services/event.service */ 35482);
/* harmony import */ var src_app_shared_enum_enumHouseProgress__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/app/shared/enum/enumHouseProgress */ 65938);
/* harmony import */ var src_app_shared_enum_enumHouseType__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/app/shared/enum/enumHouseType */ 91329);
/* harmony import */ var src_app_shared_enum_enumPayStatus__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! src/app/shared/enum/enumPayStatus */ 533);
/* harmony import */ var src_app_shared_enum_enumSignStatus__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! src/app/shared/enum/enumSignStatus */ 23718);
/* harmony import */ var src_app_shared_enum_enumQuotationStatus__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! src/app/shared/enum/enumQuotationStatus */ 92033);
/* harmony import */ var src_app_shared_services_local_storage_service__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! src/app/shared/services/local-storage.service */ 31649);
/* harmony import */ var src_app_shared_constant_constant__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! src/app/shared/constant/constant */ 70907);
/* harmony import */ var src_app_models_quotation_model__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! src/app/models/quotation.model */ 21324);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_shared_helper_allowHelper__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! src/app/shared/helper/allowHelper */ 39290);
/* harmony import */ var src_app_shared_helper_enumHelper__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! src/app/shared/helper/enumHelper */ 85654);
/* harmony import */ var src_app_shared_services_message_service__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! src/app/shared/services/message.service */ 71029);
/* harmony import */ var src_app_shared_helper_validationHelper__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! src/app/shared/helper/validationHelper */ 3824);
/* harmony import */ var src_services_api_services__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! src/services/api/services */ 45654);
/* harmony import */ var src_app_shared_helper_petternHelper__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! src/app/shared/helper/petternHelper */ 2437);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var src_app_shared_services_utility_service__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! src/app/shared/services/utility.service */ 12866);
/* harmony import */ var src_app_services_quotation_service__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! src/app/services/quotation.service */ 44336);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _ng_bootstrap_ng_bootstrap__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @ng-bootstrap/ng-bootstrap */ 48418);
/* harmony import */ var _components_breadcrumb_breadcrumb_component__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ../components/breadcrumb/breadcrumb.component */ 97932);
/* harmony import */ var _theme_directives_label_directive__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ../../@theme/directives/label.directive */ 48584);



































const _c0 = ["fileInput"];
function HouseholdManagementComponent_nb_option_12_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "nb-option", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const case_r2 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("value", case_r2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate1"](" ", case_r2.CBuildCaseName, " ");
  }
}
function HouseholdManagementComponent_nb_option_18_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "nb-option", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const case_r3 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("value", case_r3);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate1"](" ", case_r3.label, " ");
  }
}
function HouseholdManagementComponent_nb_option_35_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "nb-option", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const case_r4 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("value", case_r4);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate1"](" ", case_r4.label, " ");
  }
}
function HouseholdManagementComponent_nb_option_41_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "nb-option", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const case_r5 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("value", case_r5);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate1"](" ", case_r5.label, " ");
  }
}
function HouseholdManagementComponent_nb_option_47_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "nb-option", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const case_r6 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("value", case_r6);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate1"](" ", case_r6.label, " ");
  }
}
function HouseholdManagementComponent_nb_option_53_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "nb-option", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const case_r7 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("value", case_r7);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate1"](" ", case_r7.label, " ");
  }
}
function HouseholdManagementComponent_nb_option_59_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "nb-option", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const case_r8 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("value", case_r8);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate1"](" ", case_r8.label, " ");
  }
}
function HouseholdManagementComponent_nb_option_65_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "nb-option", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const case_r9 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("value", case_r9);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate1"](" ", case_r9.label, " ");
  }
}
function HouseholdManagementComponent_button_74_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "button", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_button_74_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r10);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]();
      const dialogHouseholdMain_r12 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵreference"](114);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx_r10.openModel(dialogHouseholdMain_r12));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](1, " \u6279\u6B21\u65B0\u589E\u6236\u5225\u8CC7\u6599 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
  }
}
function HouseholdManagementComponent_tr_108_button_20_Template(rf, ctx) {
  if (rf & 1) {
    const _r14 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "button", 56);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_tr_108_button_20_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r14);
      const item_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]().$implicit;
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]();
      const dialogUpdateHousehold_r16 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵreference"](112);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx_r10.openModelDetail(dialogUpdateHousehold_r16, item_r15));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](1, " \u7DE8\u8F2F ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
  }
}
function HouseholdManagementComponent_tr_108_Template(rf, ctx) {
  if (rf & 1) {
    const _r13 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "tr")(1, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](3, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](5, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](7, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](9, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](11, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](12);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](13, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](14);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](15, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](16);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](17, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](18);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](19, "td", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](20, HouseholdManagementComponent_tr_108_button_20_Template, 2, 0, "button", 54);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](21, "button", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_tr_108_Template_button_click_21_listener() {
      const item_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r13).$implicit;
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx_r10.onNavidateBuildCaseIdHouseId("customer-change-picture", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](22, " \u6D3D\u8AC7\u7D00\u9304 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](23, "button", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_tr_108_Template_button_click_23_listener() {
      const item_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r13).$implicit;
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx_r10.onNavidateBuildCaseIdHouseId("sample-selection-result", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](24, " \u5BA2\u8B8A\u78BA\u8A8D\u5716\u8AAA ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](25, "button", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_tr_108_Template_button_click_25_listener() {
      const item_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r13).$implicit;
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx_r10.onNavidateBuildCaseIdHouseId("finaldochouse_management", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](26, " \u7C3D\u7F72\u6587\u4EF6\u6B77\u7A0B ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](27, "button", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_tr_108_Template_button_click_27_listener() {
      const item_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r13).$implicit;
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx_r10.resetSecureKey(item_r15));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](28, " \u91CD\u7F6E\u5BC6\u78BC ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](29, "button", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_tr_108_Template_button_click_29_listener() {
      const item_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r13).$implicit;
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]();
      const dialogQuotation_r17 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵreference"](116);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx_r10.openQuotation(dialogQuotation_r17, item_r15));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](30, " \u5831\u50F9\u55AE ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const item_r15 = ctx.$implicit;
    const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate"](item_r15.CHouseHold);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate"](item_r15.CFloor);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate2"](" ", item_r15.CHouseType === 2 ? "\u92B7\u552E\u6236" : "", " ", item_r15.CHouseType === 1 ? "\u5730\u4E3B\u6236" : "", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate"](item_r15.CIsChange === true ? "\u5BA2\u8B8A" : item_r15.CIsChange === false ? "\u6A19\u6E96" : "");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate"](item_r15.CProgressName);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate3"](" ", item_r15.CPayStatus === 0 ? "\u672A\u4ED8\u6B3E" : "", " ", item_r15.CPayStatus === 1 ? "\u5DF2\u4ED8\u6B3E" : "", " ", item_r15.CPayStatus === 2 ? "\u7121\u9808\u4ED8\u6B3E" : "", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate"](item_r15.CSignStatus === 0 || item_r15.CSignStatus == null ? "\u672A\u7C3D\u56DE" : "\u5DF2\u7C3D\u56DE");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate"](ctx_r10.getQuotationStatusText(item_r15.CQuotationStatus));
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate"](item_r15.CIsEnable ? "\u555F\u7528" : "\u505C\u7528");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngIf", ctx_r10.isUpdate);
  }
}
function HouseholdManagementComponent_ng_template_111_nb_card_body_1_nb_option_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "nb-option", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const status_r20 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("value", status_r20);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate1"](" ", status_r20.CBuildCaseName, " ");
  }
}
function HouseholdManagementComponent_ng_template_111_nb_card_body_1_nb_option_34_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "nb-option", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const status_r21 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("value", status_r21);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate1"](" ", status_r21.label, " ");
  }
}
function HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_nb_option_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "nb-option", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const status_r23 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("value", status_r23);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate1"](" ", status_r23.label, " ");
  }
}
function HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_Template(rf, ctx) {
  if (rf & 1) {
    const _r22 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "div", 62)(1, "label", 91);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](2, " \u4ED8\u6B3E\u72C0\u614B ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](3, "nb-select", 92);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_Template_nb_select_ngModelChange_3_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r22);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](3);
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx_r10.detailSelected.CPayStatusSelected, $event) || (ctx_r10.detailSelected.CPayStatusSelected = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](4, HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_nb_option_4_Template, 2, 2, "nb-option", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx_r10.detailSelected.CPayStatusSelected);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngForOf", ctx_r10.options.payStatusOptions);
  }
}
function HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_nb_option_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "nb-option", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const status_r25 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("value", status_r25);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate1"](" ", status_r25.label, " ");
  }
}
function HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_Template(rf, ctx) {
  if (rf & 1) {
    const _r24 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "div", 62)(1, "label", 93);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](2, " \u9032\u5EA6 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](3, "nb-select", 94);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_Template_nb_select_ngModelChange_3_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r24);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](3);
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx_r10.detailSelected.CProgressSelected, $event) || (ctx_r10.detailSelected.CProgressSelected = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](4, HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_nb_option_4_Template, 2, 2, "nb-option", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx_r10.detailSelected.CProgressSelected);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngForOf", ctx_r10.options.progressOptions);
  }
}
function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r19 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "nb-card-body", 61)(1, "div", 62)(2, "label", 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](3, " \u5EFA\u6848\u540D\u7A31 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](4, "nb-select", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_select_ngModelChange_4_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r19);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx_r10.detailSelected.CBuildCaseSelected, $event) || (ctx_r10.detailSelected.CBuildCaseSelected = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](5, HouseholdManagementComponent_ng_template_111_nb_card_body_1_nb_option_5_Template, 2, 2, "nb-option", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](6, "div", 62)(7, "label", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](8, " \u6236\u578B\u540D\u7A31 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](9, "input", 66);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_9_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r19);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx_r10.houseDetail.CHousehold, $event) || (ctx_r10.houseDetail.CHousehold = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](10, "div", 62)(11, "label", 67);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](12, " \u6A13\u5C64 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](13, "input", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_13_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r19);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx_r10.houseDetail.CFloor, $event) || (ctx_r10.houseDetail.CFloor = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](14, "div", 62)(15, "label", 69);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](16, " \u5BA2\u6236\u59D3\u540D ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](17, "input", 70);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_17_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r19);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx_r10.houseDetail.CCustomerName, $event) || (ctx_r10.houseDetail.CCustomerName = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](18, "div", 62)(19, "label", 71);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](20, " \u8EAB\u5206\u8B49\u5B57\u865F ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](21, "input", 72);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_21_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r19);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx_r10.houseDetail.CNationalId, $event) || (ctx_r10.houseDetail.CNationalId = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](22, "div", 62)(23, "label", 73);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](24, " \u96FB\u5B50\u90F5\u4EF6 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](25, "input", 74);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_25_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r19);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx_r10.houseDetail.CMail, $event) || (ctx_r10.houseDetail.CMail = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](26, "div", 62)(27, "label", 75);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](28, " \u806F\u7D61\u96FB\u8A71 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](29, "input", 76);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_29_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r19);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx_r10.houseDetail.CPhone, $event) || (ctx_r10.houseDetail.CPhone = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](30, "div", 62)(31, "label", 77);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](32, " \u6236\u5225\u985E\u578B ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](33, "nb-select", 78);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_select_ngModelChange_33_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r19);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx_r10.detailSelected.CHouseTypeSelected, $event) || (ctx_r10.detailSelected.CHouseTypeSelected = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](34, HouseholdManagementComponent_ng_template_111_nb_card_body_1_nb_option_34_Template, 2, 2, "nb-option", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](35, HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_Template, 5, 2, "div", 79)(36, HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_Template, 5, 2, "div", 79);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](37, "div", 62)(38, "label", 80);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](39, " \u662F\u5426\u5BA2\u8B8A ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](40, "nb-checkbox", 81);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("checkedChange", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_checkbox_checkedChange_40_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r19);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx_r10.houseDetail.CIsChange, $event) || (ctx_r10.houseDetail.CIsChange = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](41, "\u662F ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](42, "div", 62)(43, "label", 82);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](44, " \u662F\u5426\u555F\u7528 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](45, "nb-checkbox", 81);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("checkedChange", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_checkbox_checkedChange_45_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r19);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx_r10.houseDetail.CIsEnable, $event) || (ctx_r10.houseDetail.CIsEnable = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](46, "\u662F ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](47, "div", 83)(48, "label", 84);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](49, " \u5BA2\u8B8A\u6642\u6BB5 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](50, "div", 85)(51, "nb-form-field", 86);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelement"](52, "nb-icon", 87);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](53, "input", 88);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_53_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r19);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx_r10.houseDetail.changeStartDate, $event) || (ctx_r10.houseDetail.changeStartDate = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelement"](54, "nb-datepicker", 89, 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](56, "nb-form-field", 86);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelement"](57, "nb-icon", 87);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](58, "input", 90);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_58_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r19);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx_r10.houseDetail.changeEndDate, $event) || (ctx_r10.houseDetail.changeEndDate = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelement"](59, "nb-datepicker", 89, 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const StartDate_r26 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵreference"](55);
    const EndDate_r27 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵreference"](60);
    const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx_r10.detailSelected.CBuildCaseSelected);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngForOf", ctx_r10.userBuildCaseOptions);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx_r10.houseDetail.CHousehold);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx_r10.houseDetail.CFloor);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx_r10.houseDetail.CCustomerName);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx_r10.houseDetail.CNationalId);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx_r10.houseDetail.CMail);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx_r10.houseDetail.CPhone);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx_r10.detailSelected.CHouseTypeSelected);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngForOf", ctx_r10.options.houseTypeOptions);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngIf", ctx_r10.isChangePayStatus);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngIf", ctx_r10.isChangeProgress);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("checked", ctx_r10.houseDetail.CIsChange);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("checked", ctx_r10.houseDetail.CIsEnable);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("nbDatepicker", StartDate_r26);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx_r10.houseDetail.changeStartDate);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("nbDatepicker", EndDate_r27);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx_r10.houseDetail.changeEndDate);
  }
}
function HouseholdManagementComponent_ng_template_111_button_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r29 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "button", 95);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_ng_template_111_button_5_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r29);
      const ref_r28 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]().dialogRef;
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx_r10.onSubmitDetail(ref_r28));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](1, "\u9001\u51FA");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
  }
}
function HouseholdManagementComponent_ng_template_111_Template(rf, ctx) {
  if (rf & 1) {
    const _r18 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "nb-card", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](1, HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template, 61, 18, "nb-card-body", 58);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](2, "nb-card-footer", 50)(3, "button", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_ng_template_111_Template_button_click_3_listener() {
      const ref_r28 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r18).dialogRef;
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx_r10.onClose(ref_r28));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](4, "\u53D6\u6D88");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](5, HouseholdManagementComponent_ng_template_111_button_5_Template, 2, 0, "button", 60);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngIf", ctx_r10.houseDetail);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngIf", ctx_r10.isCreate);
  }
}
function HouseholdManagementComponent_ng_template_113_button_19_Template(rf, ctx) {
  if (rf & 1) {
    const _r32 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "button", 104);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_ng_template_113_button_19_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r32);
      const ref_r31 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]().dialogRef;
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx_r10.addHouseHoldMain(ref_r31));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](1, "\u5132\u5B58");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
  }
}
function HouseholdManagementComponent_ng_template_113_Template(rf, ctx) {
  if (rf & 1) {
    const _r30 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "nb-card", 57)(1, "nb-card-header");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](2, " \u6236\u5225\u7BA1\u7406 \u300B\u6279\u6B21\u65B0\u589E\u6236\u5225\u8CC7\u6599 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](3, "nb-card-body", 61)(4, "div", 62)(5, "label", 96);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](6, " \u68DF\u5225 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](7, "input", 97);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_ng_template_113_Template_input_ngModelChange_7_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r30);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx_r10.houseHoldMain.CBuildingName, $event) || (ctx_r10.houseHoldMain.CBuildingName = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](8, "div", 62)(9, "label", 98);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](10, "\u7576\u5C64\u6700\u591A\u6236\u6578 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](11, "input", 99);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_ng_template_113_Template_input_ngModelChange_11_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r30);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx_r10.houseHoldMain.CHouseHoldCount, $event) || (ctx_r10.houseHoldMain.CHouseHoldCount = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](12, "div", 62)(13, "label", 100);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](14, "\u672C\u68E0\u7E3D\u6A13\u5C64 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](15, "input", 101);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_ng_template_113_Template_input_ngModelChange_15_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r30);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx_r10.houseHoldMain.CFloor, $event) || (ctx_r10.houseHoldMain.CFloor = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](16, "nb-card-footer", 50)(17, "button", 102);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_ng_template_113_Template_button_click_17_listener() {
      const ref_r31 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r30).dialogRef;
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx_r10.onClose(ref_r31));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](18);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](19, HouseholdManagementComponent_ng_template_113_button_19_Template, 2, 0, "button", 103);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx_r10.houseHoldMain.CBuildingName);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx_r10.houseHoldMain.CHouseHoldCount);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx_r10.houseHoldMain.CFloor);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate"]("\u95DC\u9589");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngIf", ctx_r10.isCreate);
  }
}
function HouseholdManagementComponent_ng_template_115_div_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r34 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "div", 143)(1, "button", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_ng_template_115_div_4_Template_button_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r34);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx_r10.addQuotationItem());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](2, " + \u65B0\u589E\u81EA\u5B9A\u7FA9\u9805\u76EE ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](3, "div")(4, "button", 144);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_ng_template_115_div_4_Template_button_click_4_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r34);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx_r10.loadDefaultItems());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](5, " \u8F09\u5165\u5BA2\u8B8A\u9700\u6C42 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](6, "button", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_ng_template_115_div_4_Template_button_click_6_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r34);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx_r10.loadRegularItems());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](7, " \u8F09\u5165\u9078\u6A23\u8CC7\u6599 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()();
  }
}
function HouseholdManagementComponent_ng_template_115_div_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "div", 145);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelement"](1, "i", 146);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](2, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](3, "\u5831\u50F9\u55AE\u5DF2\u9396\u5B9A");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](4, " - \u6B64\u5831\u50F9\u55AE\u5DF2\u9396\u5B9A\uFF0C\u7121\u6CD5\u9032\u884C\u4FEE\u6539\u3002\u60A8\u53EF\u4EE5\u5217\u5370\u6B64\u5831\u50F9\u55AE\u6216\u7522\u751F\u65B0\u7684\u5831\u50F9\u55AE\u3002 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
  }
}
function HouseholdManagementComponent_ng_template_115_tr_25_button_15_Template(rf, ctx) {
  if (rf & 1) {
    const _r37 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "button", 155);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_ng_template_115_tr_25_button_15_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r37);
      const i_r38 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]().index;
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx_r10.removeQuotationItem(i_r38));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](1, " \u522A\u9664 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
  }
}
function HouseholdManagementComponent_ng_template_115_tr_25_span_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "span", 156);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelement"](1, "i", 157);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
  }
}
function HouseholdManagementComponent_ng_template_115_tr_25_Template(rf, ctx) {
  if (rf & 1) {
    const _r35 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "tr")(1, "td")(2, "input", 147);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_2_listener($event) {
      const item_r36 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r35).$implicit;
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](item_r36.cItemName, $event) || (item_r36.cItemName = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](3, "td")(4, "input", 148);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_4_listener($event) {
      const item_r36 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r35).$implicit;
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](item_r36.cUnitPrice, $event) || (item_r36.cUnitPrice = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("ngModelChange", function HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_4_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r35);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx_r10.calculateTotal());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](5, "td")(6, "input", 149);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_6_listener($event) {
      const item_r36 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r35).$implicit;
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](item_r36.cUnit, $event) || (item_r36.cUnit = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](7, "td")(8, "input", 150);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_8_listener($event) {
      const item_r36 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r35).$implicit;
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](item_r36.cCount, $event) || (item_r36.cCount = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("ngModelChange", function HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_8_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r35);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx_r10.calculateTotal());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](9, "td", 151);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](11, "td")(12, "span", 152);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](13);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](14, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](15, HouseholdManagementComponent_ng_template_115_tr_25_button_15_Template, 2, 0, "button", 153)(16, HouseholdManagementComponent_ng_template_115_tr_25_span_16_Template, 2, 0, "span", 154);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const item_r36 = ctx.$implicit;
    const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵclassProp"]("bg-light", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 1 || item_r36.CQuotationItemType === 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", item_r36.cItemName);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("disabled", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 1 || item_r36.CQuotationItemType === 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", item_r36.cUnitPrice);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("disabled", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵclassProp"]("bg-light", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 1 || item_r36.CQuotationItemType === 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", item_r36.cUnit);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("disabled", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 1 || item_r36.CQuotationItemType === 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", item_r36.cCount);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("disabled", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate1"](" ", ctx_r10.formatCurrency(item_r36.cUnitPrice * item_r36.cCount), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵclassProp"]("badge-primary", item_r36.CQuotationItemType === 1)("badge-info", item_r36.CQuotationItemType === 3)("badge-secondary", item_r36.CQuotationItemType !== 1 && item_r36.CQuotationItemType !== 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate1"](" ", ctx_r10.getQuotationTypeText(item_r36.CQuotationItemType), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngIf", ctx_r10.isQuotationEditable);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngIf", !ctx_r10.isQuotationEditable);
  }
}
function HouseholdManagementComponent_ng_template_115_tr_26_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "tr")(1, "td", 158);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](2, " \u8ACB\u9EDE\u64CA\u300C\u65B0\u589E\u81EA\u5B9A\u7FA9\u9805\u76EE\u300D\u6216\u300C\u8F09\u5165\u5BA2\u8B8A\u9700\u6C42\u300D\u958B\u59CB\u5EFA\u7ACB\u5831\u50F9\u55AE ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
  }
}
function HouseholdManagementComponent_ng_template_115_button_63_Template(rf, ctx) {
  if (rf & 1) {
    const _r39 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "button", 159);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_ng_template_115_button_63_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r39);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx_r10.createNewQuotation());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelement"](1, "i", 160);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](2, " \u7522\u751F\u65B0\u5831\u50F9\u55AE ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
  }
}
function HouseholdManagementComponent_ng_template_115_button_67_Template(rf, ctx) {
  if (rf & 1) {
    const _r41 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "button", 161);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_ng_template_115_button_67_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r41);
      const ref_r40 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]().dialogRef;
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx_r10.lockQuotation(ref_r40));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](1, " \u9396\u5B9A\u5831\u50F9\u55AE ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("disabled", ctx_r10.quotationItems.length === 0);
  }
}
function HouseholdManagementComponent_ng_template_115_button_68_Template(rf, ctx) {
  if (rf & 1) {
    const _r42 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "button", 162);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_ng_template_115_button_68_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r42);
      const ref_r40 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]().dialogRef;
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx_r10.saveQuotation(ref_r40));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](1, " \u5132\u5B58\u5831\u50F9\u55AE ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("disabled", ctx_r10.quotationItems.length === 0);
  }
}
function HouseholdManagementComponent_ng_template_115_Template(rf, ctx) {
  if (rf & 1) {
    const _r33 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "nb-card", 105)(1, "nb-card-header");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](3, "nb-card-body");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](4, HouseholdManagementComponent_ng_template_115_div_4_Template, 8, 0, "div", 106)(5, HouseholdManagementComponent_ng_template_115_div_5_Template, 5, 0, "div", 107);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](6, "div", 108)(7, "table", 109)(8, "thead")(9, "tr")(10, "th", 110);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](11, "\u9805\u76EE\u540D\u7A31");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](12, "th", 111);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](13, "\u55AE\u50F9 (\u5143)");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](14, "th", 112);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](15, "\u55AE\u4F4D");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](16, "th", 112);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](17, "\u6578\u91CF");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](18, "th", 113);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](19, "\u5C0F\u8A08 (\u5143)");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](20, "th", 114);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](21, "\u985E\u578B");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](22, "th", 114);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](23, "\u64CD\u4F5C");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](24, "tbody");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](25, HouseholdManagementComponent_ng_template_115_tr_25_Template, 17, 22, "tr", 49)(26, HouseholdManagementComponent_ng_template_115_tr_26_Template, 3, 0, "tr", 115);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](27, "div", 116)(28, "div", 117)(29, "div", 118)(30, "div", 119)(31, "span", 120);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](32, "\u5C0F\u8A08");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](33, "span", 121);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](34);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](35, "div", 122)(36, "div", 123)(37, "div", 124);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelement"](38, "i", 125);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](39, "div")(40, "span", 126);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](41, "\u71DF\u696D\u7A05");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](42, "span", 127);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](43, "5%");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](44, "div", 128);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelement"](45, "i", 129);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](46, " \u56FA\u5B9A\u70BA\u5C0F\u8A08\u91D1\u984D\u76845% ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](47, "div", 130)(48, "div", 131);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](49);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](50, "div", 132);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](51, "\u542B\u7A05\u91D1\u984D");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelement"](52, "hr", 133);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](53, "div", 134)(54, "span", 121);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](55, "\u7E3D\u91D1\u984D");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](56, "span", 135);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](57);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](58, "nb-card-footer", 136)(59, "div")(60, "button", 137);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_ng_template_115_Template_button_click_60_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r33);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx_r10.printQuotation());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelement"](61, "i", 138);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](62, " \u5217\u5370\u5831\u50F9\u55AE ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](63, HouseholdManagementComponent_ng_template_115_button_63_Template, 3, 0, "button", 139);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](64, "div")(65, "button", 140);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_ng_template_115_Template_button_click_65_listener() {
      const ref_r40 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r33).dialogRef;
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx_r10.onClose(ref_r40));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](66, "\u53D6\u6D88");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](67, HouseholdManagementComponent_ng_template_115_button_67_Template, 2, 1, "button", 141)(68, HouseholdManagementComponent_ng_template_115_button_68_Template, 2, 1, "button", 142);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate2"](" \u5831\u50F9\u55AE - ", ctx_r10.currentHouse == null ? null : ctx_r10.currentHouse.CHouseHold, " (", ctx_r10.currentHouse == null ? null : ctx_r10.currentHouse.CFloor, "\u6A13) ");
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngIf", ctx_r10.isQuotationEditable);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngIf", !ctx_r10.isQuotationEditable);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](20);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngForOf", ctx_r10.quotationItems);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngIf", ctx_r10.quotationItems.length === 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate"](ctx_r10.formatCurrency(ctx_r10.totalAmount));
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](15);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate"](ctx_r10.formatCurrency(ctx_r10.additionalFeeAmount));
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtextInterpolate"](ctx_r10.formatCurrency(ctx_r10.finalTotalAmount));
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("disabled", ctx_r10.quotationItems.length === 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngIf", !ctx_r10.isQuotationEditable);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngIf", ctx_r10.isQuotationEditable);
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngIf", ctx_r10.isQuotationEditable);
  }
}
class HouseholdManagementComponent extends _components_base_baseComponent__WEBPACK_IMPORTED_MODULE_3__.BaseComponent {
  constructor(_allow, enumHelper, dialogService, message, valid, _houseService, _houseHoldMainService, _buildCaseService, pettern, router, _eventService, _ultilityService, quotationService) {
    super(_allow);
    this._allow = _allow;
    this.enumHelper = enumHelper;
    this.dialogService = dialogService;
    this.message = message;
    this.valid = valid;
    this._houseService = _houseService;
    this._houseHoldMainService = _houseHoldMainService;
    this._buildCaseService = _buildCaseService;
    this.pettern = pettern;
    this.router = router;
    this._eventService = _eventService;
    this._ultilityService = _ultilityService;
    this.quotationService = quotationService;
    this.tempBuildCaseID = -1;
    this.pageFirst = 1;
    this.pageSize = 10;
    this.pageIndex = 1;
    this.totalRecords = 0;
    this.statusOptions = [{
      value: 0,
      key: 'allow',
      label: '允許'
    }, {
      value: 1,
      key: 'not allowed',
      label: '不允許'
    }];
    this.cIsEnableOptions = [{
      value: null,
      key: 'all',
      label: '全部'
    }, {
      value: true,
      key: 'enable',
      label: '啟用'
    }, {
      value: false,
      key: 'deactivate',
      label: '停用'
    }];
    this.buildCaseOptions = [{
      label: '全部',
      value: ''
    }];
    this.houseHoldOptions = [{
      label: '全部',
      value: ''
    }];
    this.progressOptions = [{
      label: '全部',
      value: -1
    }];
    this.houseTypeOptions = [{
      label: '全部',
      value: -1
    }];
    this.payStatusOptions = [{
      label: '全部',
      value: -1
    }];
    this.signStatusOptions = [{
      label: '全部',
      value: -1
    }];
    this.quotationStatusOptions = [{
      label: '全部',
      value: -1
    }];
    this.options = {
      progressOptions: this.enumHelper.getEnumOptions(src_app_shared_enum_enumHouseProgress__WEBPACK_IMPORTED_MODULE_6__.EnumHouseProgress),
      payStatusOptions: this.enumHelper.getEnumOptions(src_app_shared_enum_enumPayStatus__WEBPACK_IMPORTED_MODULE_8__.EnumPayStatus),
      houseTypeOptions: this.enumHelper.getEnumOptions(src_app_shared_enum_enumHouseType__WEBPACK_IMPORTED_MODULE_7__.EnumHouseType),
      quotationStatusOptions: this.enumHelper.getEnumOptions(src_app_shared_enum_enumQuotationStatus__WEBPACK_IMPORTED_MODULE_10__.EnumQuotationStatus)
    };
    this.initDetail = {
      CHouseID: 0,
      CMail: "",
      CIsChange: false,
      CPayStatus: 0,
      CIsEnable: false,
      CCustomerName: "",
      CNationalID: "",
      CProgress: "",
      CHouseType: 0,
      CHouseHold: "",
      CPhone: ""
    };
    // 報價單相關
    this.quotationItems = [];
    this.totalAmount = 0;
    // 新增：百分比費用設定
    this.additionalFeeName = '營業稅'; // 固定名稱
    this.additionalFeePercentage = 5; // 固定5%
    this.additionalFeeAmount = 0; // 百分比費用金額
    this.finalTotalAmount = 0; // 最終總金額（含百分比費用）
    this.enableAdditionalFee = true; // 固定啟用營業稅
    this.currentHouse = null;
    this.currentQuotationId = 0;
    this.isQuotationEditable = true; // 報價單是否可編輯
    this.selectedFile = null;
    this.buildingSelectedOptions = [{
      value: '',
      label: '全部'
    }];
    this._eventService.receive().pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_25__.tap)(res => {
      if (res.action == "GET_BUILDCASE" /* EEvent.GET_BUILDCASE */ && !!res.payload) {
        this.tempBuildCaseID = res.payload;
      }
    })).subscribe();
  }
  ngOnInit() {
    this.progressOptions = [...this.progressOptions, ...this.enumHelper.getEnumOptions(src_app_shared_enum_enumHouseProgress__WEBPACK_IMPORTED_MODULE_6__.EnumHouseProgress)];
    this.houseTypeOptions = [...this.houseTypeOptions, ...this.enumHelper.getEnumOptions(src_app_shared_enum_enumHouseType__WEBPACK_IMPORTED_MODULE_7__.EnumHouseType)];
    this.payStatusOptions = [...this.payStatusOptions, ...this.enumHelper.getEnumOptions(src_app_shared_enum_enumPayStatus__WEBPACK_IMPORTED_MODULE_8__.EnumPayStatus)];
    this.signStatusOptions = [...this.signStatusOptions, ...this.enumHelper.getEnumOptions(src_app_shared_enum_enumSignStatus__WEBPACK_IMPORTED_MODULE_9__.EnumSignStatus)];
    this.quotationStatusOptions = [...this.quotationStatusOptions, ...this.enumHelper.getEnumOptions(src_app_shared_enum_enumQuotationStatus__WEBPACK_IMPORTED_MODULE_10__.EnumQuotationStatus)];
    if (src_app_shared_services_local_storage_service__WEBPACK_IMPORTED_MODULE_11__.LocalStorageService.GetSessionStorage(src_app_shared_constant_constant__WEBPACK_IMPORTED_MODULE_12__.STORAGE_KEY.HOUSE_SEARCH) != null && src_app_shared_services_local_storage_service__WEBPACK_IMPORTED_MODULE_11__.LocalStorageService.GetSessionStorage(src_app_shared_constant_constant__WEBPACK_IMPORTED_MODULE_12__.STORAGE_KEY.HOUSE_SEARCH) != undefined && src_app_shared_services_local_storage_service__WEBPACK_IMPORTED_MODULE_11__.LocalStorageService.GetSessionStorage(src_app_shared_constant_constant__WEBPACK_IMPORTED_MODULE_12__.STORAGE_KEY.HOUSE_SEARCH) != "") {
      let previous_search = JSON.parse(src_app_shared_services_local_storage_service__WEBPACK_IMPORTED_MODULE_11__.LocalStorageService.GetSessionStorage(src_app_shared_constant_constant__WEBPACK_IMPORTED_MODULE_12__.STORAGE_KEY.HOUSE_SEARCH));
      this.searchQuery = {
        CBuildCaseSelected: null,
        // CBuildingNameSelected: previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined
        //   ? this.buildingSelectedOptions.find(x => x.value == previous_search.CBuildingNameSelected.value)
        //   : this.buildingSelectedOptions[0],
        CHouseHoldSelected: previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined ? this.houseHoldOptions.find(x => x.value == previous_search.CHouseHoldSelected.value) : this.houseHoldOptions[0],
        CHouseTypeSelected: previous_search.CHouseTypeSelected != null && previous_search.CHouseTypeSelected != undefined ? this.houseTypeOptions.find(x => x.value == previous_search.CHouseTypeSelected.value) : this.houseTypeOptions[0],
        CPayStatusSelected: previous_search.CPayStatusSelected != null && previous_search.CPayStatusSelected != undefined ? this.payStatusOptions.find(x => x.value == previous_search.CPayStatusSelected.value) : this.payStatusOptions[0],
        CProgressSelected: previous_search.CProgressSelected != null && previous_search.CProgressSelected != undefined ? this.progressOptions.find(x => x.value == previous_search.CProgressSelected.value) : this.progressOptions[0],
        CSignStatusSelected: previous_search.CSignStatusSelected != null && previous_search.CSignStatusSelected != undefined ? this.signStatusOptions.find(x => x.value == previous_search.CSignStatusSelected.value) : this.signStatusOptions[0],
        CQuotationStatusSelected: previous_search.CQuotationStatusSelected != null && previous_search.CQuotationStatusSelected != undefined ? this.quotationStatusOptions.find(x => x.value == previous_search.CQuotationStatusSelected.value) : this.quotationStatusOptions[0],
        CIsEnableSeleted: previous_search.CIsEnableSeleted != null && previous_search.CIsEnableSeleted != undefined ? this.cIsEnableOptions.find(x => x.value == previous_search.CIsEnableSeleted.value) : this.cIsEnableOptions[0],
        CFrom: previous_search.CFrom != null && previous_search.CFrom != undefined ? previous_search.CFrom : '',
        CTo: previous_search.CTo != null && previous_search.CTo != undefined ? previous_search.CTo : ''
      };
    } else {
      this.searchQuery = {
        CBuildCaseSelected: null,
        // CBuildingNameSelected: this.buildingSelectedOptions[0],
        CHouseHoldSelected: this.houseHoldOptions[0],
        CHouseTypeSelected: this.houseTypeOptions[0],
        CPayStatusSelected: this.payStatusOptions[0],
        CProgressSelected: this.progressOptions[0],
        CSignStatusSelected: this.signStatusOptions[0],
        CQuotationStatusSelected: this.quotationStatusOptions[0],
        CIsEnableSeleted: this.cIsEnableOptions[0],
        CFrom: '',
        CTo: ''
      };
    }
    this.getListBuildCase();
  }
  onSearch() {
    let sessionSave = {
      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,
      // CBuildingNameSelected: this.searchQuery.CBuildingNameSelected,
      CFrom: this.searchQuery.CFrom,
      CTo: this.searchQuery.CTo,
      CHouseHoldSelected: this.searchQuery.CHouseHoldSelected,
      CHouseTypeSelected: this.searchQuery.CHouseTypeSelected,
      CIsEnableSeleted: this.searchQuery.CIsEnableSeleted,
      CPayStatusSelected: this.searchQuery.CPayStatusSelected,
      CProgressSelected: this.searchQuery.CProgressSelected,
      CSignStatusSelected: this.searchQuery.CSignStatusSelected,
      CQuotationStatusSelected: this.searchQuery.CQuotationStatusSelected
    };
    src_app_shared_services_local_storage_service__WEBPACK_IMPORTED_MODULE_11__.LocalStorageService.AddSessionStorage(src_app_shared_constant_constant__WEBPACK_IMPORTED_MODULE_12__.STORAGE_KEY.HOUSE_SEARCH, JSON.stringify(sessionSave));
    this.getHouseList().subscribe();
  }
  pageChanged(newPage) {
    this.pageIndex = newPage;
    this.getHouseList().subscribe();
  }
  exportHouse() {
    if (this.searchQuery.CBuildCaseSelected.cID) {
      this._houseService.apiHouseExportHousePost$Json({
        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID
      }).subscribe(res => {
        if (res.Entries && res.StatusCode == 0) {
          this._ultilityService.downloadExcelFile(res.Entries, '戶別資訊範本');
        } else {
          this.message.showErrorMSG(res.Message);
        }
      });
    }
  }
  triggerFileInput() {
    this.fileInput.nativeElement.click();
  }
  onFileSelected(event) {
    const input = event.target;
    if (input.files && input.files.length > 0) {
      this.selectedFile = input.files[0];
      this.importExcel();
    }
  }
  importExcel() {
    if (this.selectedFile) {
      const formData = new FormData();
      formData.append('CFile', this.selectedFile);
      this._houseService.apiHouseImportHousePost$Json({
        body: {
          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,
          CFile: this.selectedFile
        }
      }).subscribe(res => {
        if (res.StatusCode === 0) {
          this.message.showSucessMSG(res.Message);
          this.getHouseList().subscribe();
        } else {
          this.message.showErrorMSG(res.Message);
        }
      });
    }
  }
  getListHouseHold() {
    this._houseService.apiHouseGetListHouseHoldPost$Json({
      body: {
        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID
      }
    }).subscribe(res => {
      if (res.Entries && res.StatusCode == 0) {
        this.houseHoldOptions = [{
          value: '',
          label: '全部'
        }, ...res.Entries.map(e => {
          return {
            value: e,
            label: e
          };
        })];
        if (src_app_shared_services_local_storage_service__WEBPACK_IMPORTED_MODULE_11__.LocalStorageService.GetSessionStorage(src_app_shared_constant_constant__WEBPACK_IMPORTED_MODULE_12__.STORAGE_KEY.HOUSE_SEARCH) != null && src_app_shared_services_local_storage_service__WEBPACK_IMPORTED_MODULE_11__.LocalStorageService.GetSessionStorage(src_app_shared_constant_constant__WEBPACK_IMPORTED_MODULE_12__.STORAGE_KEY.HOUSE_SEARCH) != undefined && src_app_shared_services_local_storage_service__WEBPACK_IMPORTED_MODULE_11__.LocalStorageService.GetSessionStorage(src_app_shared_constant_constant__WEBPACK_IMPORTED_MODULE_12__.STORAGE_KEY.HOUSE_SEARCH) != "") {
          let previous_search = JSON.parse(src_app_shared_services_local_storage_service__WEBPACK_IMPORTED_MODULE_11__.LocalStorageService.GetSessionStorage(src_app_shared_constant_constant__WEBPACK_IMPORTED_MODULE_12__.STORAGE_KEY.HOUSE_SEARCH));
          if (previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined) {
            let index = this.houseHoldOptions.findIndex(x => x.value == previous_search.CHouseHoldSelected.value);
            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[index];
          } else {
            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0];
          }
        }
      }
    });
  }
  formatQuery() {
    this.bodyRequest = {
      CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,
      PageIndex: this.pageIndex,
      PageSize: this.pageSize
    };
    if (this.searchQuery.CFrom && this.searchQuery.CTo) {
      this.bodyRequest['CFloor'] = {
        CFrom: this.searchQuery.CFrom,
        CTo: this.searchQuery.CTo
      };
    }
    if (this.searchQuery.CHouseHoldSelected) {
      this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value;
    }
    if (this.searchQuery.CHouseTypeSelected.value) {
      this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value;
    }
    if (typeof this.searchQuery.CIsEnableSeleted.value === "boolean") {
      this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value;
    }
    if (this.searchQuery.CPayStatusSelected.value) {
      this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value;
    }
    if (this.searchQuery.CProgressSelected.value) {
      this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value;
    }
    if (this.searchQuery.CSignStatusSelected.value) {
      this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value;
    }
    if (this.searchQuery.CQuotationStatusSelected.value) {
      this.bodyRequest['CQuotationStatus'] = this.searchQuery.CQuotationStatusSelected.value;
    }
    return this.bodyRequest;
  }
  sortByFloorDescending(arr) {
    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));
  }
  getHouseList() {
    return this._houseService.apiHouseGetHouseListPost$Json({
      body: this.formatQuery()
    }).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_25__.tap)(res => {
      if (res.Entries && res.StatusCode == 0) {
        this.houseList = res.Entries;
        this.totalRecords = res.TotalItems;
      }
    }));
  }
  onSelectionChangeBuildCase() {
    // this.getListBuilding()
    this.getListHouseHold();
    this.getHouseList().subscribe();
  }
  getListBuildCase() {
    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({
      body: {
        CIsPagi: false,
        CStatus: 1
      }
    }).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_25__.tap)(res => {
      if (res.Entries && res.StatusCode == 0) {
        this.userBuildCaseOptions = res.Entries?.length ? res.Entries.map(res => {
          return {
            CBuildCaseName: res.CBuildCaseName,
            cID: res.cID
          };
        }) : [];
        if (src_app_shared_services_local_storage_service__WEBPACK_IMPORTED_MODULE_11__.LocalStorageService.GetSessionStorage(src_app_shared_constant_constant__WEBPACK_IMPORTED_MODULE_12__.STORAGE_KEY.HOUSE_SEARCH) != null && src_app_shared_services_local_storage_service__WEBPACK_IMPORTED_MODULE_11__.LocalStorageService.GetSessionStorage(src_app_shared_constant_constant__WEBPACK_IMPORTED_MODULE_12__.STORAGE_KEY.HOUSE_SEARCH) != undefined && src_app_shared_services_local_storage_service__WEBPACK_IMPORTED_MODULE_11__.LocalStorageService.GetSessionStorage(src_app_shared_constant_constant__WEBPACK_IMPORTED_MODULE_12__.STORAGE_KEY.HOUSE_SEARCH) != "") {
          let previous_search = JSON.parse(src_app_shared_services_local_storage_service__WEBPACK_IMPORTED_MODULE_11__.LocalStorageService.GetSessionStorage(src_app_shared_constant_constant__WEBPACK_IMPORTED_MODULE_12__.STORAGE_KEY.HOUSE_SEARCH));
          if (previous_search.CBuildCaseSelected != null && previous_search.CBuildCaseSelected != undefined) {
            let index = this.userBuildCaseOptions.findIndex(x => x.cID == previous_search.CBuildCaseSelected.cID);
            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index];
          } else {
            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];
          }
        } else {
          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];
        }
      }
    }), (0,rxjs__WEBPACK_IMPORTED_MODULE_25__.tap)(() => {
      // this.getListBuilding()
      this.getListHouseHold();
      setTimeout(() => {
        this.getHouseList().subscribe();
      }, 500);
    })).subscribe();
  }
  getHouseById(CID, ref) {
    this.detailSelected = {};
    this._houseService.apiHouseGetHouseByIdPost$Json({
      body: {
        CHouseID: CID
      }
    }).subscribe(res => {
      if (res.Entries && res.StatusCode == 0) {
        this.houseDetail = {
          ...res.Entries,
          changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,
          changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined
        };
        if (res.Entries.CBuildCaseId) {
          this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId);
        }
        this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus);
        if (res.Entries.CHouseType) {
          this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType);
        } else {
          this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1];
        }
        this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress);
        if (res.Entries.CBuildCaseId) {
          if (this.houseHoldMain) {
            this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId;
          }
        }
        this.dialogService.open(ref);
      }
    });
  }
  findItemInArray(array, key, value) {
    return array.find(item => item[key] === value);
  }
  openModelDetail(ref, item) {
    this.getHouseById(item.CID, ref);
  }
  openModel(ref) {
    this.houseHoldMain = {
      CBuildingName: '',
      CFloor: undefined,
      CHouseHoldCount: undefined
    };
    this.dialogService.open(ref);
  }
  formatDate(CChangeDate) {
    if (CChangeDate) {
      return moment__WEBPACK_IMPORTED_MODULE_4__(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');
    }
    return '';
  }
  onSubmitDetail(ref) {
    this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '', this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '', this.editHouseArgsParam = {
      CCustomerName: this.houseDetail.CCustomerName,
      CHouseHold: this.houseDetail.CHousehold,
      CHouseID: this.houseDetail.CId,
      CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,
      CIsChange: this.houseDetail.CIsChange,
      CIsEnable: this.houseDetail.CIsEnable,
      CMail: this.houseDetail.CMail,
      CNationalID: this.houseDetail.CNationalId,
      CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,
      CPhone: this.houseDetail.CPhone,
      CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,
      CChangeStartDate: this.houseDetail.CChangeStartDate,
      CChangeEndDate: this.houseDetail.CChangeEndDate
    };
    this.validation();
    if (this.valid.errorMessages.length > 0) {
      this.message.showErrorMSGs(this.valid.errorMessages);
      return;
    }
    this._houseService.apiHouseEditHousePost$Json({
      body: this.editHouseArgsParam
    }).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_25__.tap)(res => {
      if (res.StatusCode === 0) {
        this.message.showSucessMSG("執行成功");
        ref.close();
      } else {
        this.message.showErrorMSG(res.Message);
        ref.close();
      }
    }), (0,rxjs__WEBPACK_IMPORTED_MODULE_26__.concatMap)(() => this.getHouseList())).subscribe();
  }
  onSubmit(ref) {
    let bodyReq = {
      CCustomerName: this.houseDetail.CCustomerName,
      CHouseHold: this.houseDetail.CHousehold,
      CHouseID: this.houseDetail.CId,
      CHouseType: this.houseDetail.CHouseType,
      CIsChange: this.houseDetail.CIsChange,
      CIsEnable: this.houseDetail.CIsEnable,
      CMail: this.houseDetail.CMail,
      CNationalID: this.houseDetail.CNationalId,
      CPayStatus: this.houseDetail.CPayStatus,
      CPhone: this.houseDetail.CPhone,
      CProgress: this.houseDetail.CProgress
    };
    this._houseService.apiHouseEditHousePost$Json({
      body: bodyReq
    }).subscribe(res => {
      if (res.StatusCode === 0) {
        this.message.showSucessMSG("執行成功");
        ref.close();
      }
    });
  }
  onClose(ref) {
    ref.close();
  }
  onNavidateId(type, id) {
    const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID;
    this.router.navigate([`/pages/household-management/${type}`, idURL]);
  }
  onNavidateBuildCaseIdHouseId(type, buildCaseId, houseId) {
    this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId]);
  }
  resetSecureKey(item) {
    if (confirm("您想重設密碼嗎？")) {
      this._houseService.apiHouseResetHouseSecureKeyPost$Json({
        body: item.CID
      }).subscribe(res => {
        if (res.StatusCode == 0) {
          this.message.showSucessMSG("執行成功");
        }
      });
    }
  }
  validation() {
    this.valid.clear();
    this.valid.required('[建案名稱]', this.houseDetail.CId);
    this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold);
    this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50);
    this.valid.required('[樓層]', this.houseDetail.CFloor);
    this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50);
    // if (this.editHouseArgsParam.CNationalID) {
    //   this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID)
    // }
    this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern);
    this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone);
    this.valid.required('[進度]', this.editHouseArgsParam.CProgress);
    this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value);
    this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value);
    if (this.houseDetail.CChangeStartDate) {
      this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate);
    }
    if (this.houseDetail.CChangeEndDate) {
      this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate);
    }
    this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '');
  }
  validationHouseHoldMain() {
    this.valid.clear();
    this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID);
    this.valid.required('[棟別]', this.houseHoldMain.CBuildingName);
    this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10);
    this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100);
    this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100);
  }
  addHouseHoldMain(ref) {
    this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID, this.validationHouseHoldMain();
    if (this.valid.errorMessages.length > 0) {
      this.message.showErrorMSGs(this.valid.errorMessages);
      return;
    }
    this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({
      body: this.houseHoldMain
    }).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_25__.tap)(res => {
      if (res.StatusCode === 0) {
        this.message.showSucessMSG("執行成功");
        ref.close();
      }
    }), (0,rxjs__WEBPACK_IMPORTED_MODULE_26__.concatMap)(() => this.getHouseList())).subscribe();
  } // 開啟報價單對話框
  openQuotation(dialog, item) {
    var _this = this;
    return (0,C_Users_kenny_Documents_salechange_product_adminFront_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      _this.currentHouse = item;
      _this.quotationItems = [];
      _this.totalAmount = 0;
      _this.currentQuotationId = 0; // 重置報價單ID
      _this.isQuotationEditable = true; // 預設可編輯
      // 重置百分比費用設定（固定營業稅5%）
      _this.additionalFeeName = '營業稅';
      _this.additionalFeePercentage = 5;
      _this.additionalFeeAmount = 0;
      _this.finalTotalAmount = 0;
      _this.enableAdditionalFee = true;
      // 載入現有報價資料
      try {
        const response = yield _this.quotationService.getQuotationByHouseId(item.CID).toPromise();
        if (response && response.StatusCode === 0 && response.Entries) {
          // 保存當前的報價單ID
          _this.currentQuotationId = response.Entries.CQuotationVersionId || 0;
          // 根據 cQuotationStatus 決定是否可編輯
          if (response.Entries.CQuotationStatus === 2) {
            // 2: 已報價
            _this.isQuotationEditable = false;
          } else {
            _this.isQuotationEditable = true;
          }
          // 載入額外費用設定（固定營業稅5%，不從後端載入）
          _this.enableAdditionalFee = true;
          _this.additionalFeeName = '營業稅';
          _this.additionalFeePercentage = 5;
          // 檢查 Entries 是否有 Items 陣列
          if (response.Entries.Items && Array.isArray(response.Entries.Items)) {
            // 將 API 回傳的資料轉換為 QuotationItem 格式
            _this.quotationItems = response.Entries.Items.map(entry => ({
              cHouseID: response.Entries.CHouseID || item.CID,
              cQuotationID: response.Entries.CQuotationID,
              cItemName: entry.CItemName || '',
              cUnit: entry.CUnit || '',
              cUnitPrice: entry.CUnitPrice || 0,
              cCount: entry.CCount || 1,
              cStatus: entry.CStatus || 1,
              CQuotationItemType: entry.CQuotationItemType && entry.CQuotationItemType > 0 ? entry.CQuotationItemType : src_app_models_quotation_model__WEBPACK_IMPORTED_MODULE_13__.CQuotationItemType.自定義,
              cRemark: entry.CRemark || '',
              cQuotationStatus: entry.CQuotationStatus
            }));
            _this.calculateTotal();
          } else {}
        } else {}
      } catch (error) {
        console.error('載入報價資料失敗:', error);
      }
      _this.dialogService.open(dialog, {
        context: item,
        closeOnBackdropClick: false
      });
    })();
  }
  // 產生新報價單
  createNewQuotation() {
    this.currentQuotationId = 0;
    this.quotationItems = [];
    this.isQuotationEditable = true;
    this.totalAmount = 0;
    this.finalTotalAmount = 0;
    this.additionalFeeAmount = 0;
    this.enableAdditionalFee = true;
    // 顯示成功訊息
    this.message.showSucessMSG('已產生新報價單，可開始編輯');
  }
  // 新增自定義報價項目
  addQuotationItem() {
    this.quotationItems.push({
      cHouseID: this.currentHouse?.CID || 0,
      cItemName: '',
      cUnit: '',
      cUnitPrice: 0,
      cCount: 1,
      cStatus: 1,
      CQuotationItemType: src_app_models_quotation_model__WEBPACK_IMPORTED_MODULE_13__.CQuotationItemType.自定義,
      cRemark: ''
    });
  }
  // 載入客變需求
  loadDefaultItems() {
    var _this2 = this;
    return (0,C_Users_kenny_Documents_salechange_product_adminFront_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      try {
        if (!_this2.currentHouse?.CID) {
          _this2.message.showErrorMSG('請先選擇戶別');
          return;
        }
        const request = {
          CBuildCaseID: _this2.searchQuery?.CBuildCaseSelected?.cID || 0,
          CHouseID: _this2.currentHouse.CID
        };
        const response = yield _this2.quotationService.loadDefaultItems(request).toPromise();
        if (response?.success && response.data) {
          const defaultItems = response.data.map(x => ({
            cQuotationID: x.CQuotationID,
            cHouseID: _this2.currentHouse?.CID,
            cItemName: x.CItemName,
            cUnit: x.CUnit || '',
            cUnitPrice: x.CUnitPrice,
            cCount: x.CCount,
            cStatus: x.CStatus,
            CQuotationItemType: src_app_models_quotation_model__WEBPACK_IMPORTED_MODULE_13__.CQuotationItemType.客變需求,
            cRemark: x.CRemark
          }));
          _this2.quotationItems.push(...defaultItems);
          _this2.calculateTotal();
          _this2.message.showSucessMSG('載入客變需求成功');
        } else {
          _this2.message.showErrorMSG(response?.message || '載入客變需求失敗');
        }
      } catch (error) {
        console.error('載入客變需求錯誤:', error);
        _this2.message.showErrorMSG('載入客變需求失敗');
      }
    })();
  }
  // 載入選樣資料
  loadRegularItems() {
    var _this3 = this;
    return (0,C_Users_kenny_Documents_salechange_product_adminFront_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      try {
        if (!_this3.currentHouse?.CID) {
          _this3.message.showErrorMSG('請先選擇戶別');
          return;
        }
        const request = {
          CBuildCaseID: _this3.searchQuery?.CBuildCaseSelected?.cID || 0,
          CHouseID: _this3.currentHouse.CID
        };
        const response = yield _this3.quotationService.loadRegularItems(request).toPromise();
        if (response?.success && response.data) {
          const regularItems = response.data.map(x => ({
            cQuotationID: x.CQuotationID,
            cHouseID: _this3.currentHouse?.CID,
            cItemName: x.CItemName,
            cUnit: x.CUnit || '',
            cUnitPrice: x.CUnitPrice,
            cCount: x.CCount,
            cStatus: x.CStatus,
            CQuotationItemType: src_app_models_quotation_model__WEBPACK_IMPORTED_MODULE_13__.CQuotationItemType.選樣,
            // 選樣資料
            cRemark: x.CRemark || ''
          }));
          _this3.quotationItems.push(...regularItems);
          _this3.calculateTotal();
          _this3.message.showSucessMSG('載入選樣資料成功');
        } else {
          _this3.message.showErrorMSG(response?.message || '載入選樣資料失敗');
        }
      } catch (error) {
        console.error('載入選樣資料錯誤:', error);
        _this3.message.showErrorMSG('載入選樣資料失敗');
      }
    })();
  }
  // 移除報價項目
  removeQuotationItem(index) {
    const item = this.quotationItems[index];
    this.quotationItems.splice(index, 1);
    this.calculateTotal();
  }
  // 計算總金額
  calculateTotal() {
    this.totalAmount = this.quotationItems.reduce((sum, item) => {
      return sum + item.cUnitPrice * item.cCount;
    }, 0);
    this.calculateFinalTotal();
  }
  // 計算百分比費用和最終總金額（固定營業稅5%）
  calculateFinalTotal() {
    // 固定計算營業稅5%
    this.additionalFeeAmount = Math.round(this.totalAmount * 0.05);
    this.finalTotalAmount = this.totalAmount + this.additionalFeeAmount;
  }
  // 格式化金額
  formatCurrency(amount) {
    return new Intl.NumberFormat('zh-TW', {
      style: 'currency',
      currency: 'TWD',
      minimumFractionDigits: 0
    }).format(amount);
  }
  // 儲存報價單
  saveQuotation(ref) {
    var _this4 = this;
    return (0,C_Users_kenny_Documents_salechange_product_adminFront_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      if (_this4.quotationItems.length === 0) {
        _this4.message.showErrorMSG('請先新增報價項目');
        return;
      }
      // 驗證必填欄位 (調整：允許單價和數量為負數)
      const invalidItems = _this4.quotationItems.filter(item => !item.cItemName.trim());
      if (invalidItems.length > 0) {
        _this4.message.showErrorMSG('請確認所有項目名稱都已正確填寫');
        return;
      }
      try {
        const request = {
          houseId: _this4.currentHouse.CID,
          items: _this4.quotationItems,
          quotationId: _this4.currentQuotationId,
          // 傳遞當前的報價單ID
          // 額外費用相關欄位
          cShowOther: _this4.enableAdditionalFee,
          // 啟用額外費用
          cOtherName: _this4.additionalFeeName,
          // 額外費用名稱
          cOtherPercent: _this4.additionalFeePercentage // 額外費用百分比
        };
        const response = yield _this4.quotationService.saveQuotation(request).toPromise();
        if (response?.success) {
          _this4.message.showSucessMSG('報價單儲存成功');
          ref.close();
        } else {
          _this4.message.showErrorMSG(response?.message || '儲存失敗');
        }
      } catch (error) {
        _this4.message.showErrorMSG('報價單儲存失敗');
      }
    })();
  }
  // 匯出報價單
  exportQuotation() {
    var _this5 = this;
    return (0,C_Users_kenny_Documents_salechange_product_adminFront_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      try {
        const blob = yield _this5.quotationService.exportQuotation(_this5.currentHouse.CID).toPromise();
        if (blob) {
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `報價單_${_this5.currentHouse.CHouseHold}_${_this5.currentHouse.CFloor}樓.pdf`;
          link.click();
          window.URL.revokeObjectURL(url);
        } else {
          _this5.message.showErrorMSG('匯出報價單失敗：未收到檔案資料');
        }
      } catch (error) {
        _this5.message.showErrorMSG('匯出報價單失敗');
      }
    })();
  }
  // 列印報價單
  printQuotation() {
    if (this.quotationItems.length === 0) {
      this.message.showErrorMSG('沒有可列印的報價項目');
      return;
    }
    try {
      // 建立列印內容
      const printContent = this.generatePrintContent();
      // 建立新的視窗進行列印
      const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
      if (printWindow) {
        printWindow.document.open();
        printWindow.document.write(printContent);
        printWindow.document.close();
        // 等待內容載入完成後列印
        printWindow.onload = function () {
          setTimeout(() => {
            printWindow.print();
            // 列印後不自動關閉視窗，讓使用者可以預覽
          }, 500);
        };
      } else {
        this.message.showErrorMSG('無法開啟列印視窗，請檢查瀏覽器是否阻擋彈出視窗');
      }
    } catch (error) {
      console.error('列印報價單錯誤:', error);
      this.message.showErrorMSG('列印報價單時發生錯誤');
    }
  }
  // 產生列印內容
  generatePrintContent() {
    // 使用導入的模板
    const template = src_assets_template_quotation_template__WEBPACK_IMPORTED_MODULE_2__.QUOTATION_TEMPLATE;
    // 準備數據
    const currentDate = new Date().toLocaleDateString('zh-TW');
    const buildCaseName = this.searchQuery.CBuildCaseSelected?.CBuildCaseName || '';
    // 生成項目HTML
    let itemsHtml = '';
    this.quotationItems.forEach((item, index) => {
      const subtotal = item.cUnitPrice * item.cCount;
      const quotationType = this.getQuotationTypeText(item.CQuotationItemType);
      const unit = item.cUnit || '';
      itemsHtml += `
          <tr>
            <td class="text-center">${index + 1}</td>
            <td>${item.cItemName}</td>
            <td class="text-right">${this.formatCurrency(item.cUnitPrice)}</td>
            <td class="text-center">${unit}</td>
            <td class="text-center">${item.cCount}</td>
            <td class="text-right">${this.formatCurrency(subtotal)}</td>
            <td class="text-center">${quotationType}</td>
          </tr>
        `;
    });
    // 生成額外費用HTML
    const additionalFeeHtml = this.enableAdditionalFee ? `
        <div class="additional-fee">
          ${this.additionalFeeName} (${this.additionalFeePercentage}%)：${this.formatCurrency(this.additionalFeeAmount)}
        </div>
      ` : '';
    // 替換模板中的占位符
    const html = template.replace(/{{buildCaseName}}/g, buildCaseName).replace(/{{houseHold}}/g, this.currentHouse?.CHouseHold || '').replace(/{{floor}}/g, this.currentHouse?.CFloor || '').replace(/{{customerName}}/g, this.currentHouse?.CCustomerName || '').replace(/{{printDate}}/g, currentDate).replace(/{{itemsHtml}}/g, itemsHtml).replace(/{{subtotalAmount}}/g, this.formatCurrency(this.totalAmount)).replace(/{{additionalFeeHtml}}/g, additionalFeeHtml).replace(/{{totalAmount}}/g, this.formatCurrency(this.finalTotalAmount)).replace(/{{printDateTime}}/g, new Date().toLocaleString('zh-TW'));
    return html;
  }
  // 鎖定報價單
  lockQuotation(ref) {
    var _this6 = this;
    return (0,C_Users_kenny_Documents_salechange_product_adminFront_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      if (_this6.quotationItems.length === 0) {
        _this6.message.showErrorMSG('請先新增報價項目');
        return;
      }
      if (!_this6.currentQuotationId) {
        _this6.message.showErrorMSG('無效的報價單ID');
        return;
      }
      try {
        const response = yield _this6.quotationService.lockQuotation(_this6.currentQuotationId).toPromise();
        if (response.success) {
          _this6.message.showSucessMSG('報價單已成功鎖定');
          console.log('報價單鎖定成功:', {
            quotationId: _this6.currentQuotationId,
            message: response.message
          });
        } else {
          _this6.message.showErrorMSG(response.message || '報價單鎖定失敗');
          console.error('報價單鎖定失敗:', response.message);
        }
        ref.close();
      } catch (error) {
        _this6.message.showErrorMSG('報價單鎖定失敗');
        console.error('鎖定報價單錯誤:', error);
      }
    })();
  }
  // 取得報價類型文字
  getQuotationTypeText(quotationType) {
    switch (quotationType) {
      case src_app_models_quotation_model__WEBPACK_IMPORTED_MODULE_13__.CQuotationItemType.客變需求:
        return '客變需求';
      case src_app_models_quotation_model__WEBPACK_IMPORTED_MODULE_13__.CQuotationItemType.自定義:
        return '自定義';
      case src_app_models_quotation_model__WEBPACK_IMPORTED_MODULE_13__.CQuotationItemType.選樣:
        return '選樣';
      default:
        return '未知';
    }
  }
  getQuotationStatusText(status) {
    switch (status) {
      case src_app_shared_enum_enumQuotationStatus__WEBPACK_IMPORTED_MODULE_10__.EnumQuotationStatus.待報價:
        return '待報價';
      case src_app_shared_enum_enumQuotationStatus__WEBPACK_IMPORTED_MODULE_10__.EnumQuotationStatus.已報價:
        return '已報價';
      case src_app_shared_enum_enumQuotationStatus__WEBPACK_IMPORTED_MODULE_10__.EnumQuotationStatus.已簽回:
        return '已簽回';
      default:
        return '未知';
    }
  }
  static {
    this.ɵfac = function HouseholdManagementComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || HouseholdManagementComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵdirectiveInject"](src_app_shared_helper_allowHelper__WEBPACK_IMPORTED_MODULE_14__.AllowHelper), _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵdirectiveInject"](src_app_shared_helper_enumHelper__WEBPACK_IMPORTED_MODULE_15__.EnumHelper), _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵdirectiveInject"](_nebular_theme__WEBPACK_IMPORTED_MODULE_27__.NbDialogService), _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵdirectiveInject"](src_app_shared_services_message_service__WEBPACK_IMPORTED_MODULE_16__.MessageService), _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵdirectiveInject"](src_app_shared_helper_validationHelper__WEBPACK_IMPORTED_MODULE_17__.ValidationHelper), _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵdirectiveInject"](src_services_api_services__WEBPACK_IMPORTED_MODULE_18__.HouseService), _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵdirectiveInject"](src_services_api_services__WEBPACK_IMPORTED_MODULE_18__.HouseHoldMainService), _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵdirectiveInject"](src_services_api_services__WEBPACK_IMPORTED_MODULE_18__.BuildCaseService), _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵdirectiveInject"](src_app_shared_helper_petternHelper__WEBPACK_IMPORTED_MODULE_19__.PetternHelper), _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_28__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵdirectiveInject"](src_app_shared_services_event_service__WEBPACK_IMPORTED_MODULE_5__.EventService), _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵdirectiveInject"](src_app_shared_services_utility_service__WEBPACK_IMPORTED_MODULE_20__.UtilityService), _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵdirectiveInject"](src_app_services_quotation_service__WEBPACK_IMPORTED_MODULE_21__.QuotationService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵdefineComponent"]({
      type: HouseholdManagementComponent,
      selectors: [["ngx-household-management"]],
      viewQuery: function HouseholdManagementComponent_Query(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵviewQuery"](_c0, 5);
        }
        if (rf & 2) {
          let _t;
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵloadQuery"]()) && (ctx.fileInput = _t.first);
        }
      },
      standalone: true,
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵInheritDefinitionFeature"], _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵStandaloneFeature"]],
      decls: 117,
      vars: 23,
      consts: [["fileInput", ""], ["dialogUpdateHousehold", ""], ["dialogHouseholdMain", ""], ["dialogQuotation", ""], ["StartDate", ""], ["EndDate", ""], ["accent", "success"], [1, "font-bold", "text-[#818181]"], [1, "d-flex", "flex-wrap"], [1, "col-md-6"], [1, "form-group", "d-flex", "align-items-center", "w-full"], ["for", "buildingName", 1, "label", "col-3"], ["placeholder", "\u5EFA\u6848", 1, "col-9", 3, "ngModelChange", "selectedChange", "ngModel"], [3, "value", 4, "ngFor", "ngForOf"], ["for", "cHouseType", 1, "label", "col-3"], [1, "col-9", 3, "ngModelChange", "ngModel"], [1, "form-group", "d-flex", "align-items-center"], ["for", "cFloorFrom", 1, "label", "col-3"], [1, "ml-3"], ["type", "text", "id", "CFrom", "nbInput", "", 1, "w-full", "col-4", 3, "ngModelChange", "ngModel"], ["for", "cFloorTo", 1, "label", "col-1"], [1, "mr-3"], ["type", "text", "id", "CTo", "nbInput", "", 1, "w-full", 3, "ngModelChange", "ngModel"], ["for", "cHousehold", 1, "label", "col-3"], ["placeholder", "\u6236\u578B", 1, "col-9", 3, "ngModelChange", "ngModel"], ["for", "cPayStatus", 1, "label", "col-3"], ["placeholder", "\u7E73\u6B3E\u72C0\u614B", 1, "col-9", 3, "ngModelChange", "ngModel"], ["for", "cProgress", 1, "label", "col-3"], ["placeholder", "\u9032\u5EA6", 1, "col-9", 3, "ngModelChange", "ngModel"], ["for", "cStatus", 1, "label", "col-3"], ["placeholder", "\u72C0\u614B", 1, "col-9", 3, "ngModelChange", "ngModel"], ["for", "cSignStatus", 1, "label", "col-3"], ["placeholder", "\u7C3D\u56DE\u72C0\u614B", 1, "col-9", 3, "ngModelChange", "ngModel"], ["for", "cQuotationStatus", 1, "label", "col-3"], ["placeholder", "\u5831\u50F9\u55AE\u72C0\u614B", 1, "col-9", 3, "ngModelChange", "ngModel"], [1, "col-md-12"], [1, "d-flex", "justify-content-end", "w-full", "mt-2", "mb-3"], [1, "btn", "btn-secondary", "btn-sm", 3, "click"], [1, "fas", "fa-search"], [1, "d-flex", "justify-content-end", "w-full", "mt-3"], ["class", "btn btn-info mx-1 btn-sm", 3, "click", 4, "ngIf"], [1, "btn", "btn-info", "mx-1", "btn-sm", 3, "click"], ["type", "file", "accept", ".xlsx, .xls", 2, "display", "none", 3, "change"], [1, "btn", "btn-info", "btn-sm", 3, "click"], [1, "table-responsive", "mt-4"], [1, "table", "table-striped", "border", 2, "min-width", "1000px", "background-color", "#f3f3f3"], [2, "background-color", "#27ae60", "color", "white"], ["scope", "col", 1, "col-1"], ["scope", "col", 1, "col-4"], [4, "ngFor", "ngForOf"], [1, "d-flex", "justify-content-center"], ["aria-label", "Pagination", 3, "pageChange", "page", "pageSize", "collectionSize"], [3, "value"], [1, "text-center", "w-32", "px-0"], ["class", "btn btn-outline-success btn-sm text-left m-[2px]", 3, "click", 4, "ngIf"], [1, "btn", "btn-outline-success", "btn-sm", "m-[2px]", 3, "click"], [1, "btn", "btn-outline-success", "btn-sm", "text-left", "m-[2px]", 3, "click"], [2, "width", "500px", "max-height", "95vh"], ["class", "px-4", 4, "ngIf"], [1, "btn", "btn-outline-secondary", "m-2", "px-8", 3, "click"], ["class", "btn btn-primary m-2 bg-[#169BD5] px-8", 3, "click", 4, "ngIf"], [1, "px-4"], [1, "form-group"], ["for", "cBuildCaseId", "baseLabel", "", 1, "required-field", "mr-4", 2, "min-width", "75px"], ["placeholder", "\u5EFA\u6848\u540D\u7A31", "disabled", "true", 1, "w-full", 3, "ngModelChange", "ngModel"], ["for", "cHousehold", "baseLabel", "", 1, "required-field", "mr-4", 2, "min-width", "75px"], ["type", "text", "nbInput", "", "placeholder", "\u6236\u578B\u540D\u7A31", 1, "w-full", 3, "ngModelChange", "ngModel"], ["for", "cFloor", "baseLabel", "", 1, "required-field", "mr-4", 2, "min-width", "75px"], ["type", "number", "nbInput", "", "placeholder", "\u6A13\u5C64", "min", "1", "max", "100", "disabled", "true", 1, "w-full", 3, "ngModelChange", "ngModel"], ["for", "cCustomerName", "baseLabel", "", 1, "mr-4", 2, "min-width", "75px"], ["type", "text", "nbInput", "", "placeholder", "\u5BA2\u6236\u59D3\u540D", 1, "w-full", 3, "ngModelChange", "ngModel"], ["for", "cNationalId", "baseLabel", "", 1, "mr-4", 2, "min-width", "75px"], ["type", "text", "nbInput", "", "placeholder", "\u8EAB\u5206\u8B49\u5B57\u865F", "maxlength", "20", 1, "w-full", 3, "ngModelChange", "ngModel"], ["for", "cMail", "baseLabel", "", 1, "mr-4", 2, "min-width", "75px"], ["type", "text", "nbInput", "", "placeholder", "\u96FB\u5B50\u90F5\u4EF6", 1, "w-full", 3, "ngModelChange", "ngModel"], ["for", "cPhone", "baseLabel", "", 1, "mr-4", 2, "min-width", "75px"], ["type", "text", "nbInput", "", "placeholder", "\u806F\u7D61\u96FB\u8A71", 1, "w-full", 3, "ngModelChange", "ngModel"], ["for", "cHouseType", "baseLabel", "", 1, "required-field", "mr-4", 2, "min-width", "75px"], ["placeholder", "\u6236\u5225\u985E\u578B", 1, "w-full", 3, "ngModelChange", "ngModel"], ["class", "form-group", 4, "ngIf"], ["for", "cIsChange", "baseLabel", "", 1, "mr-4", 2, "min-width", "75px"], ["status", "basic", 3, "checkedChange", "checked"], ["for", "cIsEnable", "baseLabel", "", 1, "mr-4", 2, "min-width", "75px"], [1, "form-group", "flex", "flex-row"], ["for", "cIsEnable", "baseLabel", "", 1, "mr-4", "content-center", 2, "min-width", "75px"], [1, "max-w-xs", "flex", "flex-row"], [1, "w-1/2"], ["nbPrefix", "", "icon", "calendar-outline"], ["nbInput", "", "type", "text", "id", "StartDate", "placeholder", "yyyy-mm-dd", 1, "w-[42%]", "mr-2", 3, "ngModelChange", "nbDatepicker", "ngModel"], ["format", "yyyy-MM-dd"], ["nbInput", "", "type", "text", "id", "EndDate", "placeholder", "yyyy-mm-dd", 1, "w-[42%]", "ml-2", 3, "ngModelChange", "nbDatepicker", "ngModel"], ["for", "cPayStatus", "baseLabel", "", 1, "required-field", "mr-4", 2, "min-width", "75px"], ["placeholder", "\u4ED8\u6B3E\u72C0\u614B", 1, "w-full", 3, "ngModelChange", "ngModel"], ["for", "cProgress", "baseLabel", "", 1, "required-field", "mr-4", 2, "min-width", "75px"], ["placeholder", "\u9032\u5EA6", 1, "w-full", 3, "ngModelChange", "ngModel"], [1, "btn", "btn-primary", "m-2", "bg-[#169BD5]", "px-8", 3, "click"], ["for", "cBuildingName", "baseLabel", "", 1, "required-field", "mr-4", 2, "min-width", "75px"], ["type", "text", "nbInput", "", "placeholder", "\u68DF\u5225", 1, "w-full", 3, "ngModelChange", "ngModel"], ["for", "CHouseHoldCount", "baseLabel", "", 1, "required-field", "mr-4", 2, "min-width", "75px"], ["type", "number", "nbInput", "", "placeholder", "\u7576\u5C64\u6700\u591A\u6236\u6578", 1, "w-full", 3, "ngModelChange", "ngModel"], ["for", "CFloor", "baseLabel", "", 1, "required-field", "mr-4", 2, "min-width", "75px"], ["type", "number", "nbInput", "", "placeholder", "\u672C\u68E0\u7E3D\u6A13\u5C64", 1, "w-full", 3, "ngModelChange", "ngModel"], [1, "btn", "btn-primary", "mr-4", 3, "click"], ["class", "btn btn-primary", 3, "click", 4, "ngIf"], [1, "btn", "btn-primary", 3, "click"], [2, "width", "1200px", "max-height", "95vh"], ["class", "mb-4 d-flex justify-content-between", 4, "ngIf"], ["class", "mb-4 alert alert-warning", 4, "ngIf"], [1, "table-responsive"], [1, "table", "table-bordered"], ["width", "25%"], ["width", "15%"], ["width", "8%"], ["width", "18%"], ["width", "10%"], [4, "ngIf"], [1, "mt-4"], [1, "card", "border-0", "shadow-sm"], [1, "card-body", "p-4"], [1, "d-flex", "justify-content-between", "align-items-center", "mb-3"], [1, "h6", "mb-0", "text-muted"], [1, "h5", "mb-0", "text-dark", "fw-bold"], [1, "tax-section", "d-flex", "justify-content-between", "align-items-center", "mb-3", "p-3", "bg-light", "rounded"], [1, "d-flex", "align-items-center"], [1, "tax-icon-wrapper", "me-3"], [1, "fas", "fa-receipt", "text-info"], [1, "fw-medium", "text-dark"], [1, "tax-percentage", "ms-1", "badge", "bg-info", "text-white"], [1, "small", "text-muted", "mt-1"], [1, "fas", "fa-info-circle", "me-1"], [1, "text-end"], [1, "tax-amount", "h6", "mb-0", "text-info", "fw-bold"], [1, "small", "text-muted"], [1, "my-3"], [1, "d-flex", "justify-content-between", "align-items-center"], [1, "h4", "mb-0", "text-primary", "fw-bold"], [1, "d-flex", "justify-content-between"], ["title", "\u5217\u5370\u5831\u50F9\u55AE", 1, "btn", "btn-outline-info", "btn-sm", "me-2", 3, "click", "disabled"], [1, "fas", "fa-print", "me-1"], ["class", "btn btn-outline-success btn-sm me-2", "title", "\u7522\u751F\u65B0\u5831\u50F9\u55AE", 3, "click", 4, "ngIf"], [1, "btn", "btn-outline-secondary", "m-2", 3, "click"], ["class", "btn btn-warning m-2", 3, "disabled", "click", 4, "ngIf"], ["class", "btn btn-primary m-2", 3, "disabled", "click", 4, "ngIf"], [1, "mb-4", "d-flex", "justify-content-between"], [1, "btn", "btn-secondary", "btn-sm", "me-2", 3, "click"], [1, "mb-4", "alert", "alert-warning"], [1, "fas", "fa-lock", "me-2"], ["type", "text", "nbInput", "", 1, "w-full", 3, "ngModelChange", "ngModel", "disabled"], ["type", "number", "nbInput", "", "min", "0", "step", "0.01", 1, "w-full", 3, "ngModelChange", "ngModel", "disabled"], ["type", "text", "nbInput", "", "placeholder", "\u55AE\u4F4D", 1, "w-full", 3, "ngModelChange", "ngModel", "disabled"], ["type", "number", "nbInput", "", "step", "0.01", 1, "w-full", 3, "ngModelChange", "ngModel", "disabled"], [1, "text-right"], [1, "badge"], ["class", "btn btn-danger btn-sm", 3, "click", 4, "ngIf"], ["class", "text-muted", 4, "ngIf"], [1, "btn", "btn-danger", "btn-sm", 3, "click"], [1, "text-muted"], [1, "fas", "fa-lock"], ["colspan", "7", 1, "text-center", "text-muted", "py-4"], ["title", "\u7522\u751F\u65B0\u5831\u50F9\u55AE", 1, "btn", "btn-outline-success", "btn-sm", "me-2", 3, "click"], [1, "fas", "fa-plus", "me-1"], [1, "btn", "btn-warning", "m-2", 3, "click", "disabled"], [1, "btn", "btn-primary", "m-2", 3, "click", "disabled"]],
      template: function HouseholdManagementComponent_Template(rf, ctx) {
        if (rf & 1) {
          const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵgetCurrentView"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](0, "nb-card", 6)(1, "nb-card-header");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelement"](2, "ngx-breadcrumb");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](3, "nb-card-body")(4, "h1", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](5, "\u60A8\u53EF\u8207\u6B64\u7BA1\u7406\u5404\u6236\u5225\u5167\u4E4B\u76F8\u95DC\u8CC7\u8A0A\uFF0C\u5305\u542B\u57FA\u672C\u8CC7\u6599\u3001\u7E73\u6B3E\u72C0\u6CC1\u3001\u4E0A\u50B3\u5BA2\u8B8A\u5716\u9762\u3001\u6AA2\u8996\u5BA2\u8B8A\u7D50\u679C\u7B49\u7B49\u3002 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](6, "div", 8)(7, "div", 9)(8, "div", 10)(9, "label", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](10, "\u5EFA\u6848");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](11, "nb-select", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_Template_nb_select_ngModelChange_11_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r1);
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);
            return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("selectedChange", function HouseholdManagementComponent_Template_nb_select_selectedChange_11_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r1);
            return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx.onSelectionChangeBuildCase());
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](12, HouseholdManagementComponent_nb_option_12_Template, 2, 2, "nb-option", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](13, "div", 9)(14, "div", 10)(15, "label", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](16, "\u985E\u578B");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](17, "nb-select", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_Template_nb_select_ngModelChange_17_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r1);
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx.searchQuery.CHouseTypeSelected, $event) || (ctx.searchQuery.CHouseTypeSelected = $event);
            return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](18, HouseholdManagementComponent_nb_option_18_Template, 2, 2, "nb-option", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](19, "div", 9)(20, "div", 16)(21, "label", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](22, "\u6A13 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](23, "nb-form-field", 18)(24, "input", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_Template_input_ngModelChange_24_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r1);
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx.searchQuery.CFrom, $event) || (ctx.searchQuery.CFrom = $event);
            return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](25, "label", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](26, "~ ");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](27, "nb-form-field", 21)(28, "input", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_Template_input_ngModelChange_28_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r1);
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx.searchQuery.CTo, $event) || (ctx.searchQuery.CTo = $event);
            return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelement"](29, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](30, "div", 9)(31, "div", 10)(32, "label", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](33, " \u6236\u578B ");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](34, "nb-select", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_Template_nb_select_ngModelChange_34_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r1);
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx.searchQuery.CHouseHoldSelected, $event) || (ctx.searchQuery.CHouseHoldSelected = $event);
            return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](35, HouseholdManagementComponent_nb_option_35_Template, 2, 2, "nb-option", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](36, "div", 9)(37, "div", 10)(38, "label", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](39, " \u7E73\u6B3E\u72C0\u614B ");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](40, "nb-select", 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_Template_nb_select_ngModelChange_40_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r1);
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx.searchQuery.CPayStatusSelected, $event) || (ctx.searchQuery.CPayStatusSelected = $event);
            return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](41, HouseholdManagementComponent_nb_option_41_Template, 2, 2, "nb-option", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](42, "div", 9)(43, "div", 10)(44, "label", 27);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](45, " \u9032\u5EA6 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](46, "nb-select", 28);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_Template_nb_select_ngModelChange_46_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r1);
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx.searchQuery.CProgressSelected, $event) || (ctx.searchQuery.CProgressSelected = $event);
            return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](47, HouseholdManagementComponent_nb_option_47_Template, 2, 2, "nb-option", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](48, "div", 9)(49, "div", 10)(50, "label", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](51, " \u72C0\u614B ");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](52, "nb-select", 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_Template_nb_select_ngModelChange_52_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r1);
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx.searchQuery.CIsEnableSeleted, $event) || (ctx.searchQuery.CIsEnableSeleted = $event);
            return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](53, HouseholdManagementComponent_nb_option_53_Template, 2, 2, "nb-option", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](54, "div", 9)(55, "div", 10)(56, "label", 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](57, " \u7C3D\u56DE\u72C0\u614B ");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](58, "nb-select", 32);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_Template_nb_select_ngModelChange_58_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r1);
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx.searchQuery.CSignStatusSelected, $event) || (ctx.searchQuery.CSignStatusSelected = $event);
            return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](59, HouseholdManagementComponent_nb_option_59_Template, 2, 2, "nb-option", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](60, "div", 9)(61, "div", 10)(62, "label", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](63, " \u5831\u50F9\u55AE\u72C0\u614B ");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](64, "nb-select", 34);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("ngModelChange", function HouseholdManagementComponent_Template_nb_select_ngModelChange_64_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r1);
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx.searchQuery.CQuotationStatusSelected, $event) || (ctx.searchQuery.CQuotationStatusSelected = $event);
            return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](65, HouseholdManagementComponent_nb_option_65_Template, 2, 2, "nb-option", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelement"](66, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](67, "div", 35)(68, "div", 36)(69, "button", 37);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_Template_button_click_69_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r1);
            return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx.onSearch());
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](70, " \u67E5\u8A62 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelement"](71, "i", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](72, "div", 35)(73, "div", 39);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](74, HouseholdManagementComponent_button_74_Template, 2, 0, "button", 40);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](75, "button", 41);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_Template_button_click_75_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r1);
            return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx.onNavidateId("modify-floor-plan"));
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](76, " \u4FEE\u6539\u6A13\u5C64\u6236\u578B ");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](77, "button", 41);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_Template_button_click_77_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r1);
            return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx.exportHouse());
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](78, " \u532F\u51FA\u6236\u5225\u660E\u7D30\u6A94 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](79, "input", 42, 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("change", function HouseholdManagementComponent_Template_input_change_79_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r1);
            return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx.onFileSelected($event));
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](81, "button", 43);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("click", function HouseholdManagementComponent_Template_button_click_81_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r1);
            return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx.triggerFileInput());
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](82, " \u532F\u5165\u66F4\u65B0\u6236\u5225\u660E\u7D30\u6A94 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](83, "div", 44)(84, "table", 45)(85, "thead")(86, "tr", 46)(87, "th", 47);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](88, "\u6236\u578B");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](89, "th", 47);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](90, "\u6A13\u5C64");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](91, "th", 47);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](92, "\u6236\u5225");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](93, "th", 47);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](94, "\u985E\u578B");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](95, "th", 47);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](96, "\u9032\u5EA6");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](97, "th", 47);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](98, "\u7E73\u6B3E\u72C0\u614B");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](99, "th", 47);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](100, "\u7C3D\u56DE\u72C0\u614B");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](101, "th", 47);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](102, "\u5831\u50F9\u55AE\u72C0\u614B");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](103, "th", 47);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](104, "\u72C0\u614B");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](105, "th", 48);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtext"](106, "\u64CD\u4F5C");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](107, "tbody");
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](108, HouseholdManagementComponent_tr_108_Template, 31, 13, "tr", 49);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementStart"](109, "nb-card-footer", 50)(110, "ngb-pagination", 51);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayListener"]("pageChange", function HouseholdManagementComponent_Template_ngb_pagination_pageChange_110_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r1);
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayBindingSet"](ctx.pageIndex, $event) || (ctx.pageIndex = $event);
            return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"]($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵlistener"]("pageChange", function HouseholdManagementComponent_Template_ngb_pagination_pageChange_110_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵrestoreView"](_r1);
            return _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵresetView"](ctx.pageChanged($event));
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplate"](111, HouseholdManagementComponent_ng_template_111_Template, 6, 2, "ng-template", null, 1, _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplateRefExtractor"])(113, HouseholdManagementComponent_ng_template_113_Template, 20, 5, "ng-template", null, 2, _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplateRefExtractor"])(115, HouseholdManagementComponent_ng_template_115_Template, 69, 13, "ng-template", null, 3, _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtemplateRefExtractor"]);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](11);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx.searchQuery.CBuildCaseSelected);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngForOf", ctx.userBuildCaseOptions);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx.searchQuery.CHouseTypeSelected);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngForOf", ctx.houseTypeOptions);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx.searchQuery.CFrom);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx.searchQuery.CTo);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx.searchQuery.CHouseHoldSelected);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngForOf", ctx.houseHoldOptions);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx.searchQuery.CPayStatusSelected);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngForOf", ctx.payStatusOptions);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx.searchQuery.CProgressSelected);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngForOf", ctx.progressOptions);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx.searchQuery.CIsEnableSeleted);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngForOf", ctx.cIsEnableOptions);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx.searchQuery.CSignStatusSelected);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngForOf", ctx.signStatusOptions);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("ngModel", ctx.searchQuery.CQuotationStatusSelected);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngForOf", ctx.quotationStatusOptions);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngIf", ctx.isCreate);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](34);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("ngForOf", ctx.houseList);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵtwoWayProperty"]("page", ctx.pageIndex);
          _angular_core__WEBPACK_IMPORTED_MODULE_24__["ɵɵproperty"]("pageSize", ctx.pageSize)("collectionSize", ctx.totalRecords);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_29__.CommonModule, _angular_common__WEBPACK_IMPORTED_MODULE_29__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_29__.NgIf, _components_shared_module__WEBPACK_IMPORTED_MODULE_1__.SharedModule, _angular_forms__WEBPACK_IMPORTED_MODULE_30__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_30__.NumberValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_30__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_30__.MaxLengthValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_30__.MinValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_30__.MaxValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_30__.NgModel, _nebular_theme__WEBPACK_IMPORTED_MODULE_27__.NbCardComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_27__.NbCardBodyComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_27__.NbCardFooterComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_27__.NbCardHeaderComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_27__.NbCheckboxComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_27__.NbInputDirective, _nebular_theme__WEBPACK_IMPORTED_MODULE_27__.NbSelectComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_27__.NbOptionComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_27__.NbFormFieldComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_27__.NbPrefixDirective, _nebular_theme__WEBPACK_IMPORTED_MODULE_27__.NbIconComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_27__.NbDatepickerDirective, _nebular_theme__WEBPACK_IMPORTED_MODULE_27__.NbDatepickerComponent, _ng_bootstrap_ng_bootstrap__WEBPACK_IMPORTED_MODULE_31__.NgbPagination, _components_breadcrumb_breadcrumb_component__WEBPACK_IMPORTED_MODULE_22__.BreadcrumbComponent, _theme_directives_label_directive__WEBPACK_IMPORTED_MODULE_23__.BaseLabelDirective, _nebular_theme__WEBPACK_IMPORTED_MODULE_27__.NbDatepickerModule, _nebular_date_fns__WEBPACK_IMPORTED_MODULE_32__.NbDateFnsDateModule],
      styles: [".card[_ngcontent-%COMP%] {\n  transition: all 0.3s ease;\n}\n.card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;\n}\n\n.tax-section[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;\n  border: 1px solid #dee2e6;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n.tax-section[_ngcontent-%COMP%]:hover {\n  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%) !important;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n.tax-section[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 4px;\n  height: 100%;\n  background: linear-gradient(to bottom, #0dcaf0, #0aa2c0);\n  transition: width 0.3s ease;\n}\n.tax-section[_ngcontent-%COMP%]:hover::before {\n  width: 6px;\n}\n\n.tax-icon-wrapper[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #0dcaf0, #0aa2c0);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 2px 8px rgba(13, 202, 240, 0.3);\n  transition: all 0.3s ease;\n}\n.tax-icon-wrapper[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  font-size: 1.1rem;\n  color: white !important;\n}\n.tax-icon-wrapper[_ngcontent-%COMP%]:hover {\n  transform: scale(1.1);\n  box-shadow: 0 4px 12px rgba(13, 202, 240, 0.4);\n}\n\n.tax-percentage[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  padding: 0.25rem 0.5rem;\n  border-radius: 12px;\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\n}\n\n@keyframes _ngcontent-%COMP%_pulse {\n  0% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n  100% {\n    transform: scale(1);\n  }\n}\n.tax-amount[_ngcontent-%COMP%] {\n  transition: all 0.3s ease;\n}\n.tax-amount[_ngcontent-%COMP%]:hover {\n  transform: scale(1.05);\n  color: #0aa2c0 !important;\n}\n\n.text-primary[_ngcontent-%COMP%] {\n  color: #0d6efd !important;\n}\n\n.text-info[_ngcontent-%COMP%] {\n  color: #0dcaf0 !important;\n}\n\nhr[_ngcontent-%COMP%] {\n  border-top: 2px solid #dee2e6;\n  opacity: 0.5;\n}\n\n.h5[_ngcontent-%COMP%], \n.h6[_ngcontent-%COMP%] {\n  transition: all 0.2s ease;\n}\n\n.text-primary.fw-bold[_ngcontent-%COMP%] {\n  text-shadow: 0 1px 2px rgba(13, 110, 253, 0.1);\n  transition: all 0.3s ease;\n}\n.text-primary.fw-bold[_ngcontent-%COMP%]:hover {\n  transform: scale(1.02);\n  text-shadow: 0 2px 4px rgba(13, 110, 253, 0.2);\n}\n\n.fa-info-circle[_ngcontent-%COMP%] {\n  opacity: 0.7;\n  transition: opacity 0.2s ease;\n}\n.fa-info-circle[_ngcontent-%COMP%]:hover {\n  opacity: 1;\n}\n\n@media (max-width: 768px) {\n  .card-body[_ngcontent-%COMP%] {\n    padding: 1.5rem !important;\n  }\n  .h4[_ngcontent-%COMP%], \n   .h5[_ngcontent-%COMP%], \n   .h6[_ngcontent-%COMP%] {\n    font-size: 1rem !important;\n  }\n  .tax-icon-wrapper[_ngcontent-%COMP%] {\n    width: 35px;\n    height: 35px;\n  }\n  .tax-icon-wrapper[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n    font-size: 1rem;\n  }\n  .tax-section[_ngcontent-%COMP%] {\n    padding: 1rem !important;\n  }\n  .tax-section[_ngcontent-%COMP%]::before {\n    width: 3px;\n  }\n  .tax-section[_ngcontent-%COMP%]:hover::before {\n    width: 4px;\n  }\n}\n/*# sourceMappingURL=data:application/json;base64,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 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 15798:
/*!***************************************************************************!*\
  !*** ./src/app/pages/household-management/household-management.module.ts ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HouseholdManagementModule: () => (/* binding */ HouseholdManagementModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var src_app_theme_theme_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/@theme/theme.module */ 3253);
/* harmony import */ var _nebular_theme__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @nebular/theme */ 69375);
/* harmony import */ var _components_shared_module__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/shared.module */ 12239);
/* harmony import */ var _customer_change_picture_customer_change_picture_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./customer-change-picture/customer-change-picture.component */ 26411);
/* harmony import */ var _modify_floor_plan_modify_floor_plan_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./modify-floor-plan/modify-floor-plan.component */ 65567);
/* harmony import */ var _modify_household_modify_household_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./modify-household/modify-household.component */ 90213);
/* harmony import */ var _modify_house_type_modify_house_type_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./modify-house-type/modify-house-type.component */ 55687);
/* harmony import */ var _standard_house_plan_standard_house_plan_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./standard-house-plan/standard-house-plan.component */ 43955);
/* harmony import */ var primeng_calendar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! primeng/calendar */ 41314);
/* harmony import */ var _household_management_routing_module__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./household-management-routing.module */ 60743);
/* harmony import */ var _sample_selection_result_sample_selection_result_component__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./sample-selection-result/sample-selection-result.component */ 95099);
/* harmony import */ var _theme_pipes_specialChangeSource_pipe__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../@theme/pipes/specialChangeSource.pipe */ 41098);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/core */ 37580);















class HouseholdManagementModule {
  static {
    this.ɵfac = function HouseholdManagementModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || HouseholdManagementModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdefineNgModule"]({
      type: HouseholdManagementModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdefineInjector"]({
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_11__.CommonModule, _household_management_routing_module__WEBPACK_IMPORTED_MODULE_7__.HouseholdRoutingModule, _components_shared_module__WEBPACK_IMPORTED_MODULE_1__.SharedModule, src_app_theme_theme_module__WEBPACK_IMPORTED_MODULE_0__.ThemeModule, primeng_calendar__WEBPACK_IMPORTED_MODULE_12__.CalendarModule, _nebular_theme__WEBPACK_IMPORTED_MODULE_13__.NbMenuModule.forRoot()]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵsetNgModuleScope"](HouseholdManagementModule, {
    declarations: [_customer_change_picture_customer_change_picture_component__WEBPACK_IMPORTED_MODULE_2__.CustomerChangePictureComponent, _sample_selection_result_sample_selection_result_component__WEBPACK_IMPORTED_MODULE_8__.SampleSelectionResultComponent, _modify_floor_plan_modify_floor_plan_component__WEBPACK_IMPORTED_MODULE_3__.ModifyFloorPlanComponent, _modify_household_modify_household_component__WEBPACK_IMPORTED_MODULE_4__.ModifyHouseholdComponent, _modify_house_type_modify_house_type_component__WEBPACK_IMPORTED_MODULE_5__.ModifyHouseTypeComponent, _standard_house_plan_standard_house_plan_component__WEBPACK_IMPORTED_MODULE_6__.StandardHousePlanComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_11__.CommonModule, _household_management_routing_module__WEBPACK_IMPORTED_MODULE_7__.HouseholdRoutingModule, _components_shared_module__WEBPACK_IMPORTED_MODULE_1__.SharedModule, src_app_theme_theme_module__WEBPACK_IMPORTED_MODULE_0__.ThemeModule, primeng_calendar__WEBPACK_IMPORTED_MODULE_12__.CalendarModule, _nebular_theme__WEBPACK_IMPORTED_MODULE_13__.NbMenuModule, _theme_pipes_specialChangeSource_pipe__WEBPACK_IMPORTED_MODULE_9__.SpecialChangeSourcePipe]
  });
})();

/***/ }),

/***/ 65567:
/*!*********************************************************************************************!*\
  !*** ./src/app/pages/household-management/modify-floor-plan/modify-floor-plan.component.ts ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ModifyFloorPlanComponent: () => (/* binding */ ModifyFloorPlanComponent)
/* harmony export */ });
/* harmony import */ var _components_base_baseComponent__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../components/base/baseComponent */ 6250);
/* harmony import */ var src_app_shared_services_event_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/shared/services/event.service */ 35482);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_shared_helper_allowHelper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/shared/helper/allowHelper */ 39290);
/* harmony import */ var _nebular_theme__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @nebular/theme */ 69375);
/* harmony import */ var src_services_api_services__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/services/api/services */ 45654);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var src_app_shared_services_message_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/app/shared/services/message.service */ 71029);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _components_breadcrumb_breadcrumb_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/breadcrumb/breadcrumb.component */ 97932);












function ModifyFloorPlanComponent_nb_option_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "nb-option", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const building_r1 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("value", building_r1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", building_r1.label, " ");
  }
}
function ModifyFloorPlanComponent_div_37_tr_3_th_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "th")(1, "div", 33)(2, "nb-checkbox", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("checkedChange", function ModifyFloorPlanComponent_div_37_tr_3_th_2_Template_nb_checkbox_checkedChange_2_listener($event) {
      const idx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r2).index;
      const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r3.enableAllAtIndex($event, idx_r3));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "span", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4, "\u5168\u9078\u7121\u6B64\u6236\u578B ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const idx_r3 = ctx.index;
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("checked", ctx_r3.isCheckAllColumnChecked(idx_r3));
  }
}
function ModifyFloorPlanComponent_div_37_tr_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "tr");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "th");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, ModifyFloorPlanComponent_div_37_tr_3_th_2_Template, 5, 1, "th", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngForOf", ctx_r3.houseList[0]);
  }
}
function ModifyFloorPlanComponent_div_37_tr_5_td_8_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "td")(1, "div", 33)(2, "p", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](4, "nb-checkbox", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtwoWayListener"]("checkedChange", function ModifyFloorPlanComponent_div_37_tr_5_td_8_Template_nb_checkbox_checkedChange_4_listener($event) {
      const house_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r7).$implicit;
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtwoWayBindingSet"](house_r8.CIsSelected, $event) || (house_r8.CIsSelected = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](5, "span", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](6, "\u7121\u6B64\u6236\u578B");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const house_r8 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate2"]("", house_r8.CHouseHold || "null", " - ", house_r8.CFloor, "");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtwoWayProperty"]("checked", house_r8.CIsSelected);
  }
}
function ModifyFloorPlanComponent_div_37_tr_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "tr")(1, "td")(2, "div", 33)(3, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4, "\u00A0");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](5, "nb-checkbox", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("checkedChange", function ModifyFloorPlanComponent_div_37_tr_5_Template_nb_checkbox_checkedChange_5_listener($event) {
      const row_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r5).$implicit;
      const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r3.enableAllRow($event, row_r6));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](6, "span", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](7, "\u5168\u9078\u7121\u6B64\u6236\u578B");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](8, ModifyFloorPlanComponent_div_37_tr_5_td_8_Template, 7, 3, "td", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const row_r6 = ctx.$implicit;
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("checked", ctx_r3.isCheckAllRowChecked(row_r6));
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngForOf", row_r6);
  }
}
function ModifyFloorPlanComponent_div_37_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 29)(1, "table", 30)(2, "thead");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](3, ModifyFloorPlanComponent_div_37_tr_3_Template, 3, 1, "tr", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](4, "tbody");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](5, ModifyFloorPlanComponent_div_37_tr_5_Template, 9, 2, "tr", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r3.houseList.length);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngForOf", ctx_r3.houseList);
  }
}
class ModifyFloorPlanComponent extends _components_base_baseComponent__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(_allow, dialogService, _houseService, route, location, message, router, _eventService) {
    super(_allow);
    this._allow = _allow;
    this.dialogService = dialogService;
    this._houseService = _houseService;
    this.route = route;
    this.location = location;
    this.message = message;
    this.router = router;
    this._eventService = _eventService;
    this.buildingSelectedOptions = [{
      value: '',
      label: '全部'
    }];
    this.isHouseList = false;
  }
  getListBuilding() {
    this._houseService.apiHouseGetListBuildingPost$Json({
      body: {
        CBuildCaseID: this.buildCaseId
      }
    }).subscribe(res => {
      if (res.Entries && res.StatusCode == 0) {
        this.buildingSelectedOptions = [{
          value: '',
          label: '全部'
        }, ...res.Entries.map(e => {
          return {
            value: e,
            label: e
          };
        })];
      }
    });
  }
  clear() {
    this.searchQuery = {
      CFrom: 1,
      CTo: 100,
      CBuildingNameSelected: this.buildingSelectedOptions[0]
    };
  }
  groupByFloor(customerData) {
    const groupedData = [];
    // Get all unique floor numbers (handling potential nulls)
    const uniqueFloors = Array.from(new Set(customerData.map(customer => customer.CFloor).filter(floor => floor !== null)));
    // Create an empty array for each unique floor
    for (const floor of uniqueFloors) {
      groupedData.push([]);
    }
    // Place each customer in the correct floor array
    for (const customer of customerData) {
      const floorIndex = uniqueFloors.indexOf(customer.CFloor); // Find the index of the customer's floor in the uniqueFloors array
      if (floorIndex !== -1) {
        groupedData[floorIndex].push({
          ...customer,
          CIsSelected: customer.CIsEnable === false ? true : false
        });
      } // Add customer to the corresponding array in groupedData
    }
    return groupedData;
  }
  sortByFloorDescending(arr) {
    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));
  }
  getHouseList() {
    this.isHouseList = false;
    if (this.buildCaseId) {
      this._houseService.apiHouseGetHouseListPost$Json({
        body: {
          CBuildCaseID: this.buildCaseId,
          CBuildingName: this.searchQuery.CBuildingNameSelected.value || null,
          CFloor: {
            CFrom: this.searchQuery.CFrom,
            CTo: this.searchQuery.CTo
          },
          CIsPagi: false
        }
      }).subscribe(res => {
        if (res.StatusCode == 0) {
          if (res.Entries) {
            const rest = this.sortByFloorDescending(res.Entries);
            this.houseList = this.groupByFloor(rest);
            this.isHouseList = true;
          }
        }
      });
    }
  }
  goBack() {
    this._eventService.push({
      action: "GET_BUILDCASE" /* EEvent.GET_BUILDCASE */,
      payload: this.buildCaseId
    });
    this.location.back();
  }
  onSubmit() {
    let bodyParam = this.houseList.flat().map(item => {
      return {
        CIsEnable: !item.CIsSelected,
        CHouseID: item.CID
      };
    });
    this._houseService.apiHouseEditListHousePost$Json({
      body: {
        mode: 1,
        Args: bodyParam
      }
    }).subscribe(res => {
      if (res.StatusCode == 0) {
        this.message.showSucessMSG("執行成功");
        console.log(res);
        this.getHouseList();
      }
    });
  }
  isCheckAllRowChecked(row) {
    return row.every(item => item.CIsSelected);
  }
  isCheckAllColumnChecked(index) {
    if (this.isHouseList) {
      if (index < 0 || index >= this.houseList[0].length) {
        throw new Error("Invalid index. Index must be within the bounds of the array.");
      }
      for (const floorData of this.houseList) {
        if (index >= floorData.length || !floorData[index].CIsSelected) {
          return false; // Found a customer with CIsSelected not true (or missing)
        }
      }
      return true; // All customers at the given index have CIsSelected as true
    }
    return false;
  }
  enableAllAtIndex(checked, index) {
    if (index < 0) {
      throw new Error("Invalid index. Index must be a non-negative number.");
    }
    for (const floorData of this.houseList) {
      if (index < floorData.length) {
        // Check if index is valid for this floor
        floorData[index].CIsSelected = checked;
      }
    }
  }
  enableAllRow(checked, row) {
    for (const item of row) {
      item.CIsSelected = checked;
    }
  }
  ngOnInit() {
    this.searchQuery = {
      CBuildingNameSelected: this.buildingSelectedOptions[0],
      CFrom: 1,
      CTo: 100
    };
    this.route.paramMap.subscribe(params => {
      if (params) {
        const idParam = params.get('id');
        const id = idParam ? +idParam : 0;
        this.buildCaseId = id;
        this.getListBuilding();
        this.getHouseList();
      }
    });
  }
  pageChanged(newPage) {
    this.pageIndex = newPage;
  }
  onOpen(ref) {
    this.dialogService.open(ref);
  }
  onClose(ref) {
    ref.close();
  }
  onNavigateWithId(type) {
    this.router.navigate([`/pages/household-management/${type}`, this.buildCaseId]);
  }
  static {
    this.ɵfac = function ModifyFloorPlanComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || ModifyFloorPlanComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](src_app_shared_helper_allowHelper__WEBPACK_IMPORTED_MODULE_2__.AllowHelper), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_nebular_theme__WEBPACK_IMPORTED_MODULE_7__.NbDialogService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](src_services_api_services__WEBPACK_IMPORTED_MODULE_3__.HouseService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_8__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_9__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](src_app_shared_services_message_service__WEBPACK_IMPORTED_MODULE_4__.MessageService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_8__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](src_app_shared_services_event_service__WEBPACK_IMPORTED_MODULE_1__.EventService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineComponent"]({
      type: ModifyFloorPlanComponent,
      selectors: [["ngx-modify-floor-plan"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵInheritDefinitionFeature"]],
      decls: 47,
      vars: 5,
      consts: [["accent", "success"], [1, "font-bold", "text-[#818181]"], [1, "d-flex", "flex-wrap"], [1, "col-md-4"], [1, "form-group", "d-flex", "align-items-center", "w-full"], ["for", "buildingName", 1, "label", "col-3"], ["placeholder", "\u72C0\u614B", 1, "w-full", 3, "ngModelChange", "ngModel"], [3, "value", 4, "ngFor", "ngForOf"], [1, "col-md-5"], [1, "form-group", "d-flex", "align-items-center"], ["for", "cFloorFrom", 1, "label", "col-3"], [1, "ml-3"], ["type", "number", "id", "search", "nbInput", "", 1, "w-full", "col-4", 3, "ngModelChange", "ngModel"], ["for", "cFloorTo", 1, "label", "col-1"], [1, "mr-3"], ["type", "number", "id", "search", "nbInput", "", 1, "w-full", 3, "ngModelChange", "ngModel"], [1, "col-md-3"], [1, "d-flex", "justify-content-end", "w-full"], [1, "btn", "btn-secondary", "mx-2", 3, "click"], [1, "btn", "btn-secondary", 3, "click"], [1, "fas", "fa-search"], [1, "col-md-12"], [1, "btn", "btn-primary", 3, "click"], [1, "btn", "btn-primary", "mx-2", 3, "click"], ["class", "table-responsive mt-4", 4, "ngIf"], [1, "d-flex", "justify-content-center"], [1, "inline"], [1, "d-flex", "justify-content-center", "w-full"], [3, "value"], [1, "table-responsive", "mt-4"], [1, "table", "table-bordered", 2, "min-width", "800px", "background-color", "#f3f3f3"], [4, "ngIf"], [4, "ngFor", "ngForOf"], [1, "w-max"], ["status", "basic", 3, "checkedChange", "checked"], [1, "font-medium"], [1, "font-bold"]],
      template: function ModifyFloorPlanComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "nb-card", 0)(1, "nb-card-header");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](2, "ngx-breadcrumb");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "nb-card-body");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](4, "h1", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](5, "div", 2)(6, "div", 3)(7, "div", 4)(8, "label", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](9, "\u68DF\u5225");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](10, "nb-select", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtwoWayListener"]("ngModelChange", function ModifyFloorPlanComponent_Template_nb_select_ngModelChange_10_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtwoWayBindingSet"](ctx.searchQuery.CBuildingNameSelected, $event) || (ctx.searchQuery.CBuildingNameSelected = $event);
            return $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](11, ModifyFloorPlanComponent_nb_option_11_Template, 2, 2, "nb-option", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](12, "div", 8)(13, "div", 9)(14, "label", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](15, "\u6A13 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](16, "nb-form-field", 11)(17, "input", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtwoWayListener"]("ngModelChange", function ModifyFloorPlanComponent_Template_input_ngModelChange_17_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtwoWayBindingSet"](ctx.searchQuery.CFrom, $event) || (ctx.searchQuery.CFrom = $event);
            return $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](18, "label", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](19, "~ ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](20, "nb-form-field", 14)(21, "input", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtwoWayListener"]("ngModelChange", function ModifyFloorPlanComponent_Template_input_ngModelChange_21_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtwoWayBindingSet"](ctx.searchQuery.CTo, $event) || (ctx.searchQuery.CTo = $event);
            return $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](22, "div", 16)(23, "div", 17)(24, "button", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function ModifyFloorPlanComponent_Template_button_click_24_listener() {
            return ctx.clear();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](25, " \u6E05\u9664 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](26, "button", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function ModifyFloorPlanComponent_Template_button_click_26_listener() {
            return ctx.getHouseList();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](27, " \u67E5\u8A62 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](28, "i", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](29, "div", 21)(30, "div", 17)(31, "button", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function ModifyFloorPlanComponent_Template_button_click_31_listener() {
            return ctx.onNavigateWithId("modify-floor-plan");
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](32, " 1.\u8ABF\u6574\u6236\u578B\u7D44\u6210 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](33, "button", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function ModifyFloorPlanComponent_Template_button_click_33_listener() {
            return ctx.onNavigateWithId("modify-household");
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](34, " 2.\u4FEE\u6539\u6236\u578B\u540D\u7A31 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](35, "button", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function ModifyFloorPlanComponent_Template_button_click_35_listener() {
            return ctx.onNavigateWithId("modify-house-type");
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](36, " 3.\u8A2D\u5B9A\u5730\u4E3B\u6236 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](37, ModifyFloorPlanComponent_div_37_Template, 6, 2, "div", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](38, "nb-card-footer", 25)(39, "div", 26)(40, "div", 27)(41, "button", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function ModifyFloorPlanComponent_Template_button_click_41_listener() {
            return ctx.goBack();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](42, " \u8FD4\u56DE\u4E0A\u4E00\u9801 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](43, "button", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function ModifyFloorPlanComponent_Template_button_click_43_listener() {
            return ctx.getHouseList();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](44, " \u53D6\u6D88 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](45, "button", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function ModifyFloorPlanComponent_Template_button_click_45_listener() {
            return ctx.onSubmit();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](46, " \u5132\u5B58 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtwoWayProperty"]("ngModel", ctx.searchQuery.CBuildingNameSelected);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngForOf", ctx.buildingSelectedOptions);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtwoWayProperty"]("ngModel", ctx.searchQuery.CFrom);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtwoWayProperty"]("ngModel", ctx.searchQuery.CTo);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](16);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.isHouseList);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_9__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_9__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.NumberValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.NgModel, _nebular_theme__WEBPACK_IMPORTED_MODULE_7__.NbCardComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_7__.NbCardBodyComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_7__.NbCardFooterComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_7__.NbCardHeaderComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_7__.NbCheckboxComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_7__.NbInputDirective, _nebular_theme__WEBPACK_IMPORTED_MODULE_7__.NbSelectComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_7__.NbOptionComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_7__.NbFormFieldComponent, _components_breadcrumb_breadcrumb_component__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbComponent],
      styles: ["/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJtb2RpZnktZmxvb3ItcGxhbi5jb21wb25lbnQuc2NzcyJ9 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvbW9kaWZ5LWZsb29yLXBsYW4vbW9kaWZ5LWZsb29yLXBsYW4uY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLGdMQUFnTCIsInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 55687:
/*!*********************************************************************************************!*\
  !*** ./src/app/pages/household-management/modify-house-type/modify-house-type.component.ts ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ModifyHouseTypeComponent: () => (/* binding */ ModifyHouseTypeComponent)
/* harmony export */ });
/* harmony import */ var _components_base_baseComponent__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../components/base/baseComponent */ 6250);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_shared_helper_allowHelper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/shared/helper/allowHelper */ 39290);
/* harmony import */ var _nebular_theme__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @nebular/theme */ 69375);
/* harmony import */ var src_services_api_services__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/services/api/services */ 45654);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var src_app_shared_services_message_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/shared/services/message.service */ 71029);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _components_breadcrumb_breadcrumb_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/breadcrumb/breadcrumb.component */ 97932);










function ModifyHouseTypeComponent_nb_option_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "nb-option", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const building_r1 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("value", building_r1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", building_r1.label, " ");
  }
}
function ModifyHouseTypeComponent_div_37_tr_3_th_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "th", 34)(1, "div", 36)(2, "nb-checkbox", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("checkedChange", function ModifyHouseTypeComponent_div_37_tr_3_th_2_Template_nb_checkbox_checkedChange_2_listener($event) {
      const idx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r2).index;
      const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r3.enableAllAtIndex($event, idx_r3));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "span", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](4, "\u5168\u9078\u70BA\u5730\u4E3B\u6236 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const idx_r3 = ctx.index;
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("checked", ctx_r3.isCheckAllColumnChecked(idx_r3));
  }
}
function ModifyHouseTypeComponent_div_37_tr_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "tr", 33);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "th", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](2, ModifyHouseTypeComponent_div_37_tr_3_th_2_Template, 5, 1, "th", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx_r3.houseList[0]);
  }
}
function ModifyHouseTypeComponent_div_37_tr_5_td_8_nb_checkbox_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "nb-checkbox", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayListener"]("checkedChange", function ModifyHouseTypeComponent_div_37_tr_5_td_8_nb_checkbox_4_Template_nb_checkbox_checkedChange_0_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r7);
      const house_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayBindingSet"](house_r8.CHouseTypeBool, $event) || (house_r8.CHouseTypeBool = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "span", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2, " \u662F\u5730\u4E3B\u6236 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const house_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayProperty"]("checked", house_r8.CHouseTypeBool);
  }
}
function ModifyHouseTypeComponent_div_37_tr_5_td_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "td", 40)(1, "div", 36)(2, "p", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](4, ModifyHouseTypeComponent_div_37_tr_5_td_8_nb_checkbox_4_Template, 3, 1, "nb-checkbox", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const house_r8 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngClass", !house_r8.CIsEnable ? "bg-slate-400" : "");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate2"]("", house_r8.CHouseHold || "null", " - ", house_r8.CFloor, "");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", house_r8.CIsEnable);
  }
}
function ModifyHouseTypeComponent_div_37_tr_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "tr", 33)(1, "td", 34)(2, "div", 36)(3, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](4, "\u00A0");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "nb-checkbox", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("checkedChange", function ModifyHouseTypeComponent_div_37_tr_5_Template_nb_checkbox_checkedChange_5_listener($event) {
      const row_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r5).$implicit;
      const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r3.enableAllRow($event, row_r6));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](6, "span", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](7, "\u5168\u9078\u70BA\u5730\u4E3B\u6236 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](8, ModifyHouseTypeComponent_div_37_tr_5_td_8_Template, 5, 4, "td", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const row_r6 = ctx.$implicit;
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("checked", ctx_r3.isCheckAllRowChecked(row_r6));
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", row_r6);
  }
}
function ModifyHouseTypeComponent_div_37_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 29)(1, "table", 30)(2, "thead");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](3, ModifyHouseTypeComponent_div_37_tr_3_Template, 3, 1, "tr", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "tbody");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](5, ModifyHouseTypeComponent_div_37_tr_5_Template, 9, 2, "tr", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r3.houseList.length);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx_r3.houseList);
  }
}
class ModifyHouseTypeComponent extends _components_base_baseComponent__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(_allow, dialogService, _houseService, route, location, message, router) {
    super(_allow);
    this._allow = _allow;
    this.dialogService = dialogService;
    this._houseService = _houseService;
    this.route = route;
    this.location = location;
    this.message = message;
    this.router = router;
    this.buildingSelectedOptions = [{
      value: '',
      label: '全部'
    }];
    this.isHouseList = false;
  }
  getListBuilding() {
    this._houseService.apiHouseGetListBuildingPost$Json({
      body: {
        CBuildCaseID: this.buildCaseId
      }
    }).subscribe(res => {
      if (res.Entries && res.StatusCode == 0) {
        this.buildingSelectedOptions = [{
          value: '',
          label: '全部'
        }, ...res.Entries.map(e => {
          return {
            value: e,
            label: e
          };
        })];
      }
    });
  }
  groupByFloor(customerData) {
    const groupedData = [];
    const uniqueFloors = Array.from(new Set(customerData.map(customer => customer.CFloor).filter(floor => floor !== null)));
    for (const floor of uniqueFloors) {
      groupedData.push([]);
    }
    for (const customer of customerData) {
      const floorIndex = uniqueFloors.indexOf(customer.CFloor); // Find the index of the customer's floor in the uniqueFloors array
      if (floorIndex !== -1) {
        let custemp = {
          ...customer
        };
        if (customer.CIsEnable) {
          custemp = {
            ...custemp,
            CHouseTypeBool: customer.CHouseType && customer.CHouseType == 1 ? true : false
          };
        }
        groupedData[floorIndex].push(custemp);
      }
    }
    return groupedData;
  }
  sortByFloorDescending(arr) {
    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));
  }
  getHouseList() {
    this.isHouseList = false;
    if (this.buildCaseId) {
      this._houseService.apiHouseGetHouseListPost$Json({
        body: {
          CBuildCaseID: this.buildCaseId,
          CBuildingName: this.searchQuery.CBuildingNameSelected.value || null,
          CFloor: {
            CFrom: this.searchQuery.CFrom,
            CTo: this.searchQuery.CTo
          },
          CIsPagi: false
        }
      }).subscribe(res => {
        if (res.Entries && res.StatusCode == 0) {
          const rest = this.sortByFloorDescending(res.Entries);
          this.houseList = this.groupByFloor(rest);
          this.isHouseList = true;
          console.log('this.houseList', this.houseList);
        }
      });
    }
  }
  goBack() {
    this.location.back();
  }
  onSubmit() {
    let bodyParam = this.houseList.flat().map(item => {
      return {
        CHouseType: item.CHouseTypeBool === true ? 1 : 2,
        CHouseID: item.CID,
        CIsEnable: item.CIsEnable
      };
    });
    this._houseService.apiHouseEditListHousePost$Json({
      body: {
        mode: 3,
        Args: bodyParam.filter(e => e.CIsEnable)
      }
    }).subscribe(res => {
      if (res.StatusCode == 0) {
        this.message.showSucessMSG("執行成功");
        this.getHouseList();
      }
    });
  }
  isCheckAllRowChecked(row) {
    let count = 0;
    for (let i = 0; i < row.length; i++) {
      const item = row[i];
      if (!item.CHouseTypeBool && item.CIsEnable) {
        return false;
      }
      if (!item.CIsEnable) {
        count = count + 1;
      }
    }
    if (count === row.length) {
      return false; //If all row are disabled, they will not be checked.
    }
    return true;
  }
  isCheckAllColumnChecked(index) {
    if (this.isHouseList) {
      if (index < 0 || index >= this.houseList[0].length) {
        throw new Error("Invalid index. Index must be within the bounds of the array.");
      }
      let count = 0;
      for (const floorData of this.houseList) {
        if (floorData[index].CIsEnable) {
          if (index >= floorData.length || !floorData[index].CHouseTypeBool) {
            return false; // Found a customer with CHouseTypeBool not true (or missing)
          }
        } else {
          count = count + 1;
        }
        if (count === this.houseList.length) {
          return false; //If all columns are disabled, they will not be checked.
        }
      }
      return true; // All customers at the given index have CIsEnable as true
    }
    return false;
  }
  enableAllAtIndex(checked, index) {
    if (index < 0) {
      throw new Error("Invalid index. Index must be a non-negative number.");
    }
    for (const floorData of this.houseList) {
      if (index < floorData.length) {
        // Check if index is valid for this floor
        if (floorData[index].CIsEnable) {
          floorData[index].CHouseTypeBool = checked;
        }
      }
    }
  }
  enableAllRow(checked, row) {
    for (const item of row) {
      if (item.CIsEnable) {
        item.CHouseTypeBool = checked;
      }
    }
  }
  ngOnInit() {
    this.searchQuery = {
      CBuildingNameSelected: this.buildingSelectedOptions[0],
      CFrom: 1,
      CTo: 100
    };
    this.route.paramMap.subscribe(params => {
      if (params) {
        const idParam = params.get('id');
        const id = idParam ? +idParam : 0;
        this.buildCaseId = id;
        this.getListBuilding();
        this.getHouseList();
      }
    });
  }
  pageChanged(newPage) {
    this.pageIndex = newPage;
  }
  clear() {
    this.searchQuery = {
      CFrom: 1,
      CTo: 100,
      CBuildingNameSelected: this.buildingSelectedOptions[0]
    };
  }
  onOpen(ref) {
    this.dialogService.open(ref);
  }
  onClose(ref) {
    ref.close();
  }
  onNavigateWithId(type) {
    this.router.navigate([`/pages/household-management/${type}`, this.buildCaseId]);
  }
  static {
    this.ɵfac = function ModifyHouseTypeComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || ModifyHouseTypeComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](src_app_shared_helper_allowHelper__WEBPACK_IMPORTED_MODULE_1__.AllowHelper), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbDialogService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](src_services_api_services__WEBPACK_IMPORTED_MODULE_2__.HouseService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_8__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](src_app_shared_services_message_service__WEBPACK_IMPORTED_MODULE_3__.MessageService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.Router));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineComponent"]({
      type: ModifyHouseTypeComponent,
      selectors: [["ngx-modify-house-type"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵInheritDefinitionFeature"]],
      decls: 47,
      vars: 5,
      consts: [["accent", "success"], [1, "font-bold", "text-[#818181]"], [1, "d-flex", "flex-wrap"], [1, "col-md-4"], [1, "form-group", "d-flex", "align-items-center", "w-full"], ["for", "buildingName", 1, "label", "col-3"], ["placeholder", "\u72C0\u614B", 1, "w-full", 3, "ngModelChange", "ngModel"], [3, "value", 4, "ngFor", "ngForOf"], [1, "col-md-5"], [1, "form-group", "d-flex", "align-items-center"], ["for", "cFloorFrom", 1, "label", "col-3"], [1, "ml-3"], ["type", "number", "id", "search", "nbInput", "", 1, "w-full", "col-4", 3, "ngModelChange", "ngModel"], ["for", "cFloorTo", 1, "label", "col-1"], [1, "mr-3"], ["type", "number", "id", "search", "nbInput", "", 1, "w-full", 3, "ngModelChange", "ngModel"], [1, "col-md-3"], [1, "d-flex", "justify-content-end", "w-full"], [1, "btn", "btn-secondary", "mx-2", 3, "click"], [1, "btn", "btn-secondary", 3, "click"], [1, "fas", "fa-search"], [1, "col-md-12"], [1, "btn", "btn-primary", 3, "click"], [1, "btn", "btn-primary", "mx-2", 3, "click"], ["class", "table-responsive mt-4", 4, "ngIf"], [1, "d-flex", "justify-content-center"], [1, "inline"], [1, "d-flex", "justify-content-center", "w-full"], [3, "value"], [1, "table-responsive", "mt-4"], [1, "table", "table-bordered", 2, "min-width", "1000px", "background-color", "#f3f3f3"], ["class", "text-center", 4, "ngIf"], ["class", "text-center", 4, "ngFor", "ngForOf"], [1, "text-center"], [1, "px-1"], ["class", " px-1", 4, "ngFor", "ngForOf"], [1, "w-max"], ["status", "basic", 3, "checkedChange", "checked"], [1, "font-medium"], [3, "ngClass", 4, "ngFor", "ngForOf"], [3, "ngClass"], [1, "font-bold"], ["status", "basic", 3, "checked", "checkedChange", 4, "ngIf"]],
      template: function ModifyHouseTypeComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "nb-card", 0)(1, "nb-card-header");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "ngx-breadcrumb");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "nb-card-body");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](4, "h1", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "div", 2)(6, "div", 3)(7, "div", 4)(8, "label", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](9, "\u68DF\u5225");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](10, "nb-select", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayListener"]("ngModelChange", function ModifyHouseTypeComponent_Template_nb_select_ngModelChange_10_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayBindingSet"](ctx.searchQuery.CBuildingNameSelected, $event) || (ctx.searchQuery.CBuildingNameSelected = $event);
            return $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](11, ModifyHouseTypeComponent_nb_option_11_Template, 2, 2, "nb-option", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](12, "div", 8)(13, "div", 9)(14, "label", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](15, "\u6A13 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](16, "nb-form-field", 11)(17, "input", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayListener"]("ngModelChange", function ModifyHouseTypeComponent_Template_input_ngModelChange_17_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayBindingSet"](ctx.searchQuery.CFrom, $event) || (ctx.searchQuery.CFrom = $event);
            return $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](18, "label", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](19, "~ ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](20, "nb-form-field", 14)(21, "input", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayListener"]("ngModelChange", function ModifyHouseTypeComponent_Template_input_ngModelChange_21_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayBindingSet"](ctx.searchQuery.CTo, $event) || (ctx.searchQuery.CTo = $event);
            return $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](22, "div", 16)(23, "div", 17)(24, "button", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModifyHouseTypeComponent_Template_button_click_24_listener() {
            return ctx.clear();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](25, " \u6E05\u9664 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](26, "button", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModifyHouseTypeComponent_Template_button_click_26_listener() {
            return ctx.getHouseList();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](27, " \u67E5\u8A62 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](28, "i", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](29, "div", 21)(30, "div", 17)(31, "button", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModifyHouseTypeComponent_Template_button_click_31_listener() {
            return ctx.onNavigateWithId("modify-floor-plan");
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](32, " 1.\u8ABF\u6574\u6236\u578B\u7D44\u6210 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](33, "button", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModifyHouseTypeComponent_Template_button_click_33_listener() {
            return ctx.onNavigateWithId("modify-household");
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](34, " 2.\u4FEE\u6539\u6236\u578B\u540D\u7A31 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](35, "button", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModifyHouseTypeComponent_Template_button_click_35_listener() {
            return ctx.onNavigateWithId("modify-house-type");
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](36, " 3.\u8A2D\u5B9A\u5730\u4E3B\u6236 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](37, ModifyHouseTypeComponent_div_37_Template, 6, 2, "div", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](38, "nb-card-footer", 25)(39, "div", 26)(40, "div", 27)(41, "button", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModifyHouseTypeComponent_Template_button_click_41_listener() {
            return ctx.goBack();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](42, " \u8FD4\u56DE\u4E0A\u4E00\u9801 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](43, "button", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModifyHouseTypeComponent_Template_button_click_43_listener() {
            return ctx.getHouseList();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](44, " \u53D6\u6D88 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](45, "button", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModifyHouseTypeComponent_Template_button_click_45_listener() {
            return ctx.onSubmit();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](46, " \u5132\u5B58 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayProperty"]("ngModel", ctx.searchQuery.CBuildingNameSelected);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx.buildingSelectedOptions);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayProperty"]("ngModel", ctx.searchQuery.CFrom);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayProperty"]("ngModel", ctx.searchQuery.CTo);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](16);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.isHouseList);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_8__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.NumberValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.NgModel, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbCardComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbCardBodyComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbCardFooterComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbCardHeaderComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbCheckboxComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbInputDirective, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbSelectComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbOptionComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbFormFieldComponent, _components_breadcrumb_breadcrumb_component__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbComponent],
      styles: ["/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJtb2RpZnktaG91c2UtdHlwZS5jb21wb25lbnQuc2NzcyJ9 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvbW9kaWZ5LWhvdXNlLXR5cGUvbW9kaWZ5LWhvdXNlLXR5cGUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLGdMQUFnTCIsInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 90213:
/*!*******************************************************************************************!*\
  !*** ./src/app/pages/household-management/modify-household/modify-household.component.ts ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ModifyHouseholdComponent: () => (/* binding */ ModifyHouseholdComponent)
/* harmony export */ });
/* harmony import */ var _components_base_baseComponent__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../components/base/baseComponent */ 6250);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_shared_helper_allowHelper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/shared/helper/allowHelper */ 39290);
/* harmony import */ var _nebular_theme__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @nebular/theme */ 69375);
/* harmony import */ var src_services_api_services__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/services/api/services */ 45654);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var src_app_shared_services_message_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/shared/services/message.service */ 71029);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _components_breadcrumb_breadcrumb_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/breadcrumb/breadcrumb.component */ 97932);










const _c0 = a0 => ({
  "opacity-50 cursor-not-allowed": a0
});
function ModifyHouseholdComponent_nb_option_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "nb-option", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const building_r1 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("value", building_r1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", building_r1.label, " ");
  }
}
function ModifyHouseholdComponent_div_37_tr_3_th_4_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 38)(1, "input", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayListener"]("ngModelChange", function ModifyHouseholdComponent_div_37_tr_3_th_4_div_1_Template_input_ngModelChange_1_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r2);
      const house_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayBindingSet"](house_r3.CHouseHold, $event) || (house_r3.CHouseHold = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "br");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "span", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModifyHouseholdComponent_div_37_tr_3_th_4_div_1_Template_span_click_3_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r2);
      const house_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;
      const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r3.clearForm(house_r3));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](4, " \u53D6\u6D88 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "span", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModifyHouseholdComponent_div_37_tr_3_th_4_div_1_Template_span_click_5_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r2);
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
      const house_r3 = ctx_r4.$implicit;
      const idx_r6 = ctx_r4.index;
      const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r3.onUpdateAllCol(house_r3, idx_r6));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](6, " \u6279\u6B21\u5132\u5B58 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const house_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayProperty"]("ngModel", house_r3.CHouseHold);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction1"](2, _c0, house_r3.CHouseHold === ""));
  }
}
function ModifyHouseholdComponent_div_37_tr_3_th_4_div_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 42)(1, "span", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModifyHouseholdComponent_div_37_tr_3_th_4_div_2_Template_span_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r7);
      const house_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](house_r3.isEdit = !house_r3.isEdit);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2, "\u6279\u6B21\u4FEE\u6539\u540D\u7A31 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
}
function ModifyHouseholdComponent_div_37_tr_3_th_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "th");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, ModifyHouseholdComponent_div_37_tr_3_th_4_div_1_Template, 7, 4, "div", 36)(2, ModifyHouseholdComponent_div_37_tr_3_th_4_div_2_Template, 3, 0, "div", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const house_r3 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", house_r3.isEdit === true);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", house_r3.isEdit !== true);
  }
}
function ModifyHouseholdComponent_div_37_tr_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "tr", 33)(1, "th")(2, "span", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, "\u6A13\u5C64");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](4, ModifyHouseholdComponent_div_37_tr_3_th_4_Template, 3, 2, "th", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx_r3.houseListItem);
  }
}
function ModifyHouseholdComponent_div_37_tr_5_td_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "td")(1, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const row_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](row_r8[0].CFloor);
  }
}
function ModifyHouseholdComponent_div_37_tr_5_td_2_p_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "p", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const house_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](house_r9.CHouseHold || "null");
  }
}
function ModifyHouseholdComponent_div_37_tr_5_td_2_div_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 38)(1, "input", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayListener"]("ngModelChange", function ModifyHouseholdComponent_div_37_tr_5_td_2_div_2_Template_input_ngModelChange_1_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r10);
      const house_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayBindingSet"](house_r9.CHouseHold, $event) || (house_r9.CHouseHold = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "br");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "span", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModifyHouseholdComponent_div_37_tr_5_td_2_div_2_Template_span_click_3_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r10);
      const house_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;
      const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r3.closeEditItem(house_r9));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](4, " \u53D6\u6D88 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "span", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModifyHouseholdComponent_div_37_tr_5_td_2_div_2_Template_span_click_5_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r10);
      const house_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;
      const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r3.onUpdateItem(house_r9));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](6, " \u5132\u5B58 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const house_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayProperty"]("ngModel", house_r9.CHouseHold);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction1"](2, _c0, house_r9.CHouseHold === ""));
  }
}
function ModifyHouseholdComponent_div_37_tr_5_td_2_div_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r11 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 48)(1, "span", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModifyHouseholdComponent_div_37_tr_5_td_2_div_3_Template_span_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r11);
      const house_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;
      const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r3.onEditItem(house_r9));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2, "\u4FEE\u6539\u540D\u7A31 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
}
function ModifyHouseholdComponent_div_37_tr_5_td_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "td", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, ModifyHouseholdComponent_div_37_tr_5_td_2_p_1_Template, 2, 1, "p", 47)(2, ModifyHouseholdComponent_div_37_tr_5_td_2_div_2_Template, 7, 4, "div", 36)(3, ModifyHouseholdComponent_div_37_tr_5_td_2_div_3_Template, 3, 0, "div", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const house_r9 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngClass", !house_r9.CIsEnable ? "bg-slate-400" : "");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", !house_r9.isEdit);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", house_r9.CIsEnable && house_r9.isEdit === true);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", house_r9.CIsEnable && house_r9.isEdit !== true);
  }
}
function ModifyHouseholdComponent_div_37_tr_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "tr", 33);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, ModifyHouseholdComponent_div_37_tr_5_td_1_Template, 3, 1, "td", 44)(2, ModifyHouseholdComponent_div_37_tr_5_td_2_Template, 4, 4, "td", 45);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const row_r8 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", row_r8.length);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", row_r8);
  }
}
function ModifyHouseholdComponent_div_37_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 29)(1, "table", 30)(2, "thead");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](3, ModifyHouseholdComponent_div_37_tr_3_Template, 5, 1, "tr", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "tbody");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](5, ModifyHouseholdComponent_div_37_tr_5_Template, 3, 2, "tr", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r3.houseList.length);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx_r3.houseList);
  }
}
class ModifyHouseholdComponent extends _components_base_baseComponent__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(_allow, dialogService, _houseService, route, location, message, router) {
    super(_allow);
    this._allow = _allow;
    this.dialogService = dialogService;
    this._houseService = _houseService;
    this.route = route;
    this.location = location;
    this.message = message;
    this.router = router;
    this.buildingSelectedOptions = [{
      value: '',
      label: '全部'
    }];
    this.isHouseList = false;
  }
  getListBuilding() {
    this._houseService.apiHouseGetListBuildingPost$Json({
      body: {
        CBuildCaseID: this.buildCaseId
      }
    }).subscribe(res => {
      if (res.Entries && res.StatusCode == 0) {
        this.buildingSelectedOptions = [{
          value: '',
          label: '全部'
        }, ...res.Entries.map(e => {
          return {
            value: e,
            label: e
          };
        })];
      }
    });
  }
  groupByFloor(customerData) {
    const groupedData = [];
    // Get all unique floor numbers (handling potential nulls)
    const uniqueFloors = Array.from(new Set(customerData.map(customer => customer.CFloor).filter(floor => floor !== null)));
    // Create an empty array for each unique floor
    for (const floor of uniqueFloors) {
      groupedData.push([]);
    }
    // Place each customer in the correct floor array
    for (const customer of customerData) {
      const floorIndex = uniqueFloors.indexOf(customer.CFloor); // Find the index of the customer's floor in the uniqueFloors array
      if (floorIndex !== -1) {
        groupedData[floorIndex].push(customer);
      } // Add customer to the corresponding array in groupedData
    }
    return groupedData;
  }
  sortByFloorDescending(arr) {
    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));
  }
  getHouseList() {
    this.isHouseList = false;
    if (this.buildCaseId) {
      this._houseService.apiHouseGetHouseListPost$Json({
        body: {
          CBuildCaseID: this.buildCaseId,
          CBuildingName: this.searchQuery.CBuildingNameSelected.value || null,
          CFloor: {
            CFrom: this.searchQuery.CFrom,
            CTo: this.searchQuery.CTo
          },
          CIsPagi: false
        }
      }).subscribe(res => {
        if (res.Entries && res.StatusCode == 0) {
          const rest = this.sortByFloorDescending(res.Entries);
          this.houseList = this.groupByFloor(rest);
          this.houseListItem = this.houseList[0].map(item => {
            return {
              CHouseHold: "",
              isEdit: false
            };
          });
          this.houseList.forEach(element => {
            if (element.CIsEnable) {
              element['isEdit'] = false;
            }
          });
          this.isHouseList = true;
        }
      });
    }
  }
  goBack() {
    this.location.back();
  }
  onSubmit() {
    let bodyParam = this.houseList.flat().map(item => {
      return {
        CIsEnable: item.CIsEnable,
        CHouseID: item.CID,
        CHouseHold: item.CHouseHold
      };
    });
    this._houseService.apiHouseEditListHousePost$Json({
      body: {
        mode: 2,
        Args: bodyParam.filter(e => e.CIsEnable)
      }
    }).subscribe(res => {
      if (res.StatusCode == 0) {
        this.message.showSucessMSG("執行成功");
        this.getHouseList();
      }
    });
  }
  onEditItem(house) {
    house.isEdit = !house.isEdit;
    this.currentCHouseHold = house.CHouseHold;
  }
  closeEditItem(house) {
    house.isEdit = !house.isEdit;
    if (this.currentCHouseHold) {
      house.CHouseHold = this.currentCHouseHold;
    }
  }
  onUpdateItem(item) {
    this._houseService.apiHouseEditListHousePost$Json({
      body: {
        mode: 2,
        Args: [{
          CIsEnable: item.CIsEnable,
          CHouseID: item.CID,
          CHouseHold: item.CHouseHold
        }]
      }
    }).subscribe(res => {
      if (res.StatusCode == 0) {
        this.message.showSucessMSG("執行成功");
        this.getHouseList();
      }
    });
  }
  clearForm(house) {
    house.isEdit = !house.isEdit;
    house.CHouseHold = '';
  }
  onUpdateAllCol(item, index) {
    if (item.CHouseHold === '') return;
    let param = [];
    this.houseList.forEach(element => {
      if (element[index].CIsEnable) {
        param.push({
          CHouseHold: item.CHouseHold,
          CHouseID: element[index].CID,
          CIsEnable: element[index].CIsEnable
        });
      }
    });
    this._houseService.apiHouseEditListHousePost$Json({
      body: {
        mode: 2,
        Args: param
      }
    }).subscribe(res => {
      if (res.StatusCode == 0) {
        this.message.showSucessMSG("執行成功");
        this.getHouseList();
      }
    });
  }
  ngOnInit() {
    this.searchQuery = {
      CBuildingNameSelected: this.buildingSelectedOptions[0],
      CFrom: 1,
      CTo: 100
    };
    this.route.paramMap.subscribe(params => {
      if (params) {
        const idParam = params.get('id');
        const id = idParam ? +idParam : 0;
        this.buildCaseId = id;
        this.getListBuilding();
        this.getHouseList();
      }
    });
  }
  pageChanged(newPage) {
    this.pageIndex = newPage;
  }
  onOpen(ref) {
    this.dialogService.open(ref);
  }
  onClose(ref) {
    ref.close();
  }
  clear() {
    this.searchQuery = {
      CFrom: 1,
      CTo: 100,
      CBuildingNameSelected: this.buildingSelectedOptions[0]
    };
  }
  onNavigateWithId(type) {
    this.router.navigate([`/pages/household-management/${type}`, this.buildCaseId]);
  }
  static {
    this.ɵfac = function ModifyHouseholdComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || ModifyHouseholdComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](src_app_shared_helper_allowHelper__WEBPACK_IMPORTED_MODULE_1__.AllowHelper), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbDialogService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](src_services_api_services__WEBPACK_IMPORTED_MODULE_2__.HouseService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_8__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](src_app_shared_services_message_service__WEBPACK_IMPORTED_MODULE_3__.MessageService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.Router));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineComponent"]({
      type: ModifyHouseholdComponent,
      selectors: [["ngx-modify-household"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵInheritDefinitionFeature"]],
      decls: 47,
      vars: 5,
      consts: [["accent", "success"], [1, "font-bold", "text-[#818181]"], [1, "d-flex", "flex-wrap"], [1, "col-md-4"], [1, "form-group", "d-flex", "align-items-center", "w-full"], ["for", "buildingName", 1, "label", "col-3"], ["placeholder", "\u72C0\u614B", 1, "w-full", 3, "ngModelChange", "ngModel"], [3, "value", 4, "ngFor", "ngForOf"], [1, "col-md-5"], [1, "form-group", "d-flex", "align-items-center"], ["for", "cFloorFrom", 1, "label", "col-3"], [1, "ml-3"], ["type", "number", "id", "search", "nbInput", "", 1, "w-full", "col-4", 3, "ngModelChange", "ngModel"], ["for", "cFloorTo", 1, "label", "col-1"], [1, "mr-3"], ["type", "number", "id", "search", "nbInput", "", 1, "w-full", 3, "ngModelChange", "ngModel"], [1, "col-md-3"], [1, "d-flex", "justify-content-end", "w-full"], [1, "btn", "btn-secondary", "mx-2", 3, "click"], [1, "btn", "btn-secondary", 3, "click"], [1, "fas", "fa-search"], [1, "col-md-12"], [1, "btn", "btn-primary", 3, "click"], [1, "btn", "btn-primary", "mx-2", 3, "click"], ["class", "table-responsive mt-4", 4, "ngIf"], [1, "d-flex", "justify-content-center"], [1, "inline"], [1, "d-flex", "justify-content-center", "w-full"], [3, "value"], [1, "table-responsive", "mt-4"], [1, "table", "table-bordered", 2, "min-width", "800px", "background-color", "#f3f3f3"], ["class", "text-center", 4, "ngIf"], ["class", "text-center", 4, "ngFor", "ngForOf"], [1, "text-center"], [1, "block", "w-8"], [4, "ngFor", "ngForOf"], ["class", "font-bold float-left w-32", 4, "ngIf"], ["class", "font-bold w-max", 4, "ngIf"], [1, "font-bold", "float-left", "w-32"], ["type", "text", "id", "CHouseHold", "nbInput", "", 1, "w-full", 3, "ngModelChange", "ngModel"], [1, "font-normal", "text-blue-400", "underline", "inline-block", "w-[40%]", "hover:underline", "cursor-pointer", 3, "click"], [1, "font-normal", "text-blue-400", "underline", "inline-block", "w-[60%]", "hover:underline", "cursor-pointer", 3, "click", "ngClass"], [1, "font-bold", "w-max"], [1, "font-normal", "text-blue-400", "hover:underline", "underline", "cursor-pointer", "block", "min-w-3", 3, "click"], [4, "ngIf"], [3, "ngClass", 4, "ngFor", "ngForOf"], [3, "ngClass"], ["class", "font-bold", 4, "ngIf"], [1, "font-bold"], [1, "font-normal", "text-blue-400", "underline", "inline-block", "w-[50%]", "hover:underline", "cursor-pointer", 3, "click"], [1, "font-normal", "text-blue-400", "underline", "inline-block", "w-[50%]", "hover:underline", "cursor-pointer", 3, "click", "ngClass"], [1, "font-normal", "text-blue-400", "hover:underline", "underline", "cursor-pointer", 3, "click"]],
      template: function ModifyHouseholdComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "nb-card", 0)(1, "nb-card-header");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "ngx-breadcrumb");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "nb-card-body");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](4, "h1", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "div", 2)(6, "div", 3)(7, "div", 4)(8, "label", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](9, "\u68DF\u5225");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](10, "nb-select", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayListener"]("ngModelChange", function ModifyHouseholdComponent_Template_nb_select_ngModelChange_10_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayBindingSet"](ctx.searchQuery.CBuildingNameSelected, $event) || (ctx.searchQuery.CBuildingNameSelected = $event);
            return $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](11, ModifyHouseholdComponent_nb_option_11_Template, 2, 2, "nb-option", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](12, "div", 8)(13, "div", 9)(14, "label", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](15, "\u6A13 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](16, "nb-form-field", 11)(17, "input", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayListener"]("ngModelChange", function ModifyHouseholdComponent_Template_input_ngModelChange_17_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayBindingSet"](ctx.searchQuery.CFrom, $event) || (ctx.searchQuery.CFrom = $event);
            return $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](18, "label", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](19, "~ ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](20, "nb-form-field", 14)(21, "input", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayListener"]("ngModelChange", function ModifyHouseholdComponent_Template_input_ngModelChange_21_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayBindingSet"](ctx.searchQuery.CTo, $event) || (ctx.searchQuery.CTo = $event);
            return $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](22, "div", 16)(23, "div", 17)(24, "button", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModifyHouseholdComponent_Template_button_click_24_listener() {
            return ctx.clear();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](25, " \u6E05\u9664 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](26, "button", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModifyHouseholdComponent_Template_button_click_26_listener() {
            return ctx.getHouseList();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](27, " \u67E5\u8A62 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](28, "i", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](29, "div", 21)(30, "div", 17)(31, "button", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModifyHouseholdComponent_Template_button_click_31_listener() {
            return ctx.onNavigateWithId("modify-floor-plan");
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](32, " 1.\u8ABF\u6574\u6236\u578B\u7D44\u6210 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](33, "button", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModifyHouseholdComponent_Template_button_click_33_listener() {
            return ctx.onNavigateWithId("modify-household");
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](34, " 2.\u4FEE\u6539\u6236\u578B\u540D\u7A31 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](35, "button", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModifyHouseholdComponent_Template_button_click_35_listener() {
            return ctx.onNavigateWithId("modify-house-type");
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](36, " 3.\u8A2D\u5B9A\u5730\u4E3B\u6236 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](37, ModifyHouseholdComponent_div_37_Template, 6, 2, "div", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](38, "nb-card-footer", 25)(39, "div", 26)(40, "div", 27)(41, "button", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModifyHouseholdComponent_Template_button_click_41_listener() {
            return ctx.goBack();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](42, " \u8FD4\u56DE\u4E0A\u4E00\u9801 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](43, "button", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModifyHouseholdComponent_Template_button_click_43_listener() {
            return ctx.getHouseList();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](44, " \u53D6\u6D88 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](45, "button", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModifyHouseholdComponent_Template_button_click_45_listener() {
            return ctx.onSubmit();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](46, " \u5132\u5B58 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayProperty"]("ngModel", ctx.searchQuery.CBuildingNameSelected);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx.buildingSelectedOptions);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayProperty"]("ngModel", ctx.searchQuery.CFrom);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayProperty"]("ngModel", ctx.searchQuery.CTo);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](16);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.isHouseList);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_8__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.NumberValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.NgModel, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbCardComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbCardBodyComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbCardFooterComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbCardHeaderComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbInputDirective, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbSelectComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbOptionComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbFormFieldComponent, _components_breadcrumb_breadcrumb_component__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbComponent],
      styles: ["/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJtb2RpZnktaG91c2Vob2xkLmNvbXBvbmVudC5zY3NzIn0= */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvbW9kaWZ5LWhvdXNlaG9sZC9tb2RpZnktaG91c2Vob2xkLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSxnTEFBZ0wiLCJzb3VyY2VSb290IjoiIn0= */"]
    });
  }
}

/***/ }),

/***/ 95099:
/*!*********************************************************************************************************!*\
  !*** ./src/app/pages/household-management/sample-selection-result/sample-selection-result.component.ts ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SampleSelectionResultComponent: () => (/* binding */ SampleSelectionResultComponent)
/* harmony export */ });
/* harmony import */ var _components_base_baseComponent__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../components/base/baseComponent */ 6250);
/* harmony import */ var src_app_shared_services_event_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/shared/services/event.service */ 35482);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! moment */ 39545);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_shared_helper_allowHelper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/shared/helper/allowHelper */ 39290);
/* harmony import */ var _nebular_theme__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @nebular/theme */ 69375);
/* harmony import */ var src_app_shared_helper_validationHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/app/shared/helper/validationHelper */ 3824);
/* harmony import */ var src_services_api_services__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/services/api/services */ 45654);
/* harmony import */ var src_app_shared_services_message_service__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/app/shared/services/message.service */ 71029);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _ng_bootstrap_ng_bootstrap__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @ng-bootstrap/ng-bootstrap */ 48418);
/* harmony import */ var _theme_directives_label_directive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../@theme/directives/label.directive */ 48584);
/* harmony import */ var _theme_pipes_date_format_pipe__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../@theme/pipes/date-format.pipe */ 59908);
/* harmony import */ var _theme_pipes_mapping_pipe__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../@theme/pipes/mapping.pipe */ 55446);
/* harmony import */ var _theme_pipes_specialChangeSource_pipe__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../../@theme/pipes/specialChangeSource.pipe */ 41098);


















function SampleSelectionResultComponent_tr_27_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](0, "tr", 16)(1, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](3, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](5, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵpipe"](7, "dateFormat");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](8, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵpipe"](10, "getDocumentStatus");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](11, "td", 17)(12, "button", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵlistener"]("click", function SampleSelectionResultComponent_tr_27_Template_button_click_12_listener() {
      const item_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵrestoreView"](_r3).$implicit;
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵresetView"](ctx_r4.openPdfInNewTab(item_r4));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](13, "\u6AA2\u8996");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const item_r4 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtextInterpolate"](item_r4.CIsChange === true ? "\u5BA2\u8B8A" : item_r4.CIsChange === false ? "\u9078\u6A23" : "");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtextInterpolate"](item_r4.CDocumentName);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtextInterpolate"](item_r4.CSignDate ? _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵpipeBind1"](7, 5, item_r4.CSignDate) : "");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵpipeBind1"](10, 7, item_r4.CDocumentStatus));
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("disabled", !item_r4.CFile);
  }
}
function SampleSelectionResultComponent_ng_template_34_tr_34_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](0, "tr")(1, "td")(2, "nb-checkbox", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtwoWayListener"]("checkedChange", function SampleSelectionResultComponent_ng_template_34_tr_34_Template_nb_checkbox_checkedChange_2_listener($event) {
      const drawing_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵrestoreView"](_r7).$implicit;
      _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtwoWayBindingSet"](drawing_r8.isChecked, $event) || (drawing_r8.isChecked = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](3, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵpipe"](5, "specialChangeSource");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](6, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](8, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](10, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](11);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](12, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](13);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const drawing_r8 = ctx.$implicit;
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtwoWayProperty"]("checked", drawing_r8.isChecked);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵpipeBind1"](5, 6, drawing_r8.CSource));
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtextInterpolate"](ctx_r4.formatDate(drawing_r8.CChangeDate));
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtextInterpolate"](drawing_r8.CDrawingName);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtextInterpolate"](ctx_r4.formatDate(drawing_r8.CCreateDT));
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtextInterpolate"](ctx_r4.formatDate(drawing_r8.CApproveDate));
  }
}
function SampleSelectionResultComponent_ng_template_34_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](0, "nb-card", 19)(1, "nb-card-header");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](3, "nb-card-body", 20)(4, "h4");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](5, " \u8ACB\u78BA\u8A8D\u8981\u5C07\u54EA\u4E9B\u5716\u9762\u6574\u5408\u70BA\u4E00\u4EFD\u6587\u4EF6\u4F9B\u5BA2\u6236\u7C3D\u540D\u78BA\u8A8D\u3002 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](6, "div", 21)(7, "label", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](8, " \u6587\u4EF6\u540D\u7A31 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](9, "input", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtwoWayListener"]("ngModelChange", function SampleSelectionResultComponent_ng_template_34_Template_input_ngModelChange_9_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵrestoreView"](_r6);
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵnextContext"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtwoWayBindingSet"](ctx_r4.finalDoc.CDocumentName, $event) || (ctx_r4.finalDoc.CDocumentName = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](10, "div", 21)(11, "label", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](12, " \u9078\u6A23\u7D50\u679C ");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](13, "nb-checkbox", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtwoWayListener"]("checkedChange", function SampleSelectionResultComponent_ng_template_34_Template_nb_checkbox_checkedChange_13_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵrestoreView"](_r6);
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵnextContext"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtwoWayBindingSet"](ctx_r4.isChecked, $event) || (ctx_r4.isChecked = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](14, "\u9078\u6A23\u7D50\u679C ");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](15, "div", 21)(16, "label", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](17, " \u5BA2\u8B8A\u5716 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](18, "h4");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](19, "\u50C5\u80FD\u52FE\u9078\u5DF2\u901A\u904E\u5BE9\u6838\u4E4B\u5716\u9762\u3002");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](20, "table", 27)(21, "thead")(22, "tr", 28)(23, "th");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](24, "\u4F86\u6E90");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](25, "th");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](26, "\u8A0E\u8AD6\u65E5\u671F");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](27, "th");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](28, "\u5716\u9762\u540D\u7A31");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](29, "th");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](30, "\u4E0A\u50B3\u65E5\u671F");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](31, "th");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](32, "\u901A\u904E\u65E5\u671F");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](33, "tbody");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtemplate"](34, SampleSelectionResultComponent_ng_template_34_tr_34_Template, 14, 8, "tr", 29);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](35, "div", 30)(36, "label", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](37, "\u9001\u5BE9\u8CC7\u8A0A ");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](38, "p", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](39, "\u5167\u90E8\u5BE9\u6838\u4EBA\u54E1\u67E5\u770B");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](40, "textarea", 33);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtwoWayListener"]("ngModelChange", function SampleSelectionResultComponent_ng_template_34_Template_textarea_ngModelChange_40_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵrestoreView"](_r6);
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵnextContext"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtwoWayBindingSet"](ctx_r4.finalDoc.CApproveRemark, $event) || (ctx_r4.finalDoc.CApproveRemark = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](41, "        ");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](42, "div", 30)(43, "label", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](44, "\u6458\u8981\u8A3B\u8A18 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](45, "p", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](46, "\u5BA2\u6236\u65BC\u6587\u4EF6\u4E2D\u67E5\u770B");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](47, "textarea", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtwoWayListener"]("ngModelChange", function SampleSelectionResultComponent_ng_template_34_Template_textarea_ngModelChange_47_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵrestoreView"](_r6);
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵnextContext"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtwoWayBindingSet"](ctx_r4.finalDoc.CNote, $event) || (ctx_r4.finalDoc.CNote = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](48, "        ");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](49, "nb-card-footer", 13)(50, "button", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵlistener"]("click", function SampleSelectionResultComponent_ng_template_34_Template_button_click_50_listener() {
      const ref_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵrestoreView"](_r6).dialogRef;
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵresetView"](ctx_r4.onClose(ref_r9));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](51, "\u53D6\u6D88");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](52, "button", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵlistener"]("click", function SampleSelectionResultComponent_ng_template_34_Template_button_click_52_listener() {
      const ref_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵrestoreView"](_r6).dialogRef;
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵresetView"](ctx_r4.onCreateFinalDoc(ref_r9));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](53, "\u78BA\u8A8D\u9001\u51FA\u5BE9\u6838");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtextInterpolate2"](" \u6236\u5225\u7BA1\u7406 > \u5BA2\u8B8A\u78BA\u8A8D\u5716\u8AAA > ", ctx_r4.houseByID.CHousehold, " ", ctx_r4.houseByID.CFloor, "F ");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtwoWayProperty"]("ngModel", ctx_r4.finalDoc.CDocumentName);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtwoWayProperty"]("checked", ctx_r4.isChecked);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](21);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("ngForOf", ctx_r4.listSpecialChangeAvailable);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtwoWayProperty"]("ngModel", ctx_r4.finalDoc.CApproveRemark);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtwoWayProperty"]("ngModel", ctx_r4.finalDoc.CNote);
  }
}
class SampleSelectionResultComponent extends _components_base_baseComponent__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(_allow, dialogService, valid, _finalDocumentService, message, route, location, _eventService, _houseService) {
    super(_allow);
    this._allow = _allow;
    this.dialogService = dialogService;
    this.valid = valid;
    this._finalDocumentService = _finalDocumentService;
    this.message = message;
    this.route = route;
    this.location = location;
    this._eventService = _eventService;
    this._houseService = _houseService;
    this.documentStatusOptions = ['待審核', '已駁回', '待客戶簽回', '客戶已簽回'];
    this.isChecked = true;
  }
  ngOnInit() {
    this.route.paramMap.subscribe(params => {
      if (params) {
        this.buildCaseId = +(params.get('id1') ?? 0);
        this.houseID = +(params.get('id2') ?? 0);
        if (this.houseID) {
          this.getHouseById();
        }
        this.getListFinalDoc();
      }
    });
  }
  getHouseById() {
    this._houseService.apiHouseGetHouseByIdPost$Json({
      body: {
        CHouseID: this.houseID
      }
    }).subscribe(res => {
      if (res.Entries && res.StatusCode == 0) {
        this.houseByID = res.Entries;
      }
    });
  }
  openPdfInNewTab(data) {
    if (data && data.CFile) window.open(data.CFile, '_blank');
  }
  getListFinalDoc() {
    this._finalDocumentService.apiFinalDocumentGetListFinalDocPost$Json({
      body: {
        CHouseID: this.houseID,
        PageIndex: this.pageIndex,
        PageSize: this.pageSize
      }
    }).subscribe(res => {
      if (res.TotalItems && res.Entries && res.StatusCode == 0) {
        this.listFinalDoc = res.Entries ?? [];
        this.totalRecords = res.TotalItems;
      }
    });
  }
  getListSpecialChangeAvailable() {
    this._finalDocumentService.apiFinalDocumentGetListSpecialChangeAvailablePost$Json({
      body: {
        CHouseID: this.houseID
      }
    }).subscribe(res => {
      this.listSpecialChangeAvailable = [];
      if (res.TotalItems && res.Entries && res.StatusCode == 0) {
        this.listSpecialChangeAvailable = res.Entries ?? [];
        if (res.Entries.length) {
          this.listSpecialChangeAvailable = res.Entries.map(e => {
            return {
              ...e,
              isChecked: false
            };
          });
        }
      }
    });
  }
  validation() {
    this.valid.clear();
    this.valid.required('[文件名稱]', this.finalDoc.CDocumentName);
    this.valid.required('[送審資訊]', this.finalDoc.CApproveRemark);
    this.valid.required('[系統操作說明]', this.finalDoc.CNote);
  }
  goBack() {
    this._eventService.push({
      action: "GET_BUILDCASE" /* EEvent.GET_BUILDCASE */,
      payload: this.buildCaseId
    });
    this.location.back();
  }
  getCheckedCIDs(changeArray) {
    if (changeArray && changeArray.length) {
      return changeArray.filter(change => change.isChecked).map(change => change.CID);
    }
    return [];
  }
  onCreateFinalDoc(ref) {
    this.validation();
    if (this.valid.errorMessages.length > 0) {
      this.message.showErrorMSGs(this.valid.errorMessages);
      return;
    }
    const param = {
      ...this.finalDoc,
      CSpecialChange: this.getCheckedCIDs(this.listSpecialChangeAvailable)
    };
    this._finalDocumentService.apiFinalDocumentCreateFinalDocPost$Json({
      body: param
    }).subscribe(res => {
      if (res.StatusCode === 0) {
        this.getListFinalDoc();
        this.message.showSucessMSG("執行成功");
        ref.close();
      } else {
        this.message.showErrorMSG(res.Message);
      }
    });
  }
  pageChanged(newPage) {
    this.pageIndex = newPage;
    this.getListFinalDoc();
  }
  addNew(ref) {
    this.finalDoc = {
      CHouseID: this.houseID,
      CDocumentName: '',
      CApproveRemark: '',
      CNote: ""
    };
    this.getListSpecialChangeAvailable();
    this.dialogService.open(ref);
  }
  onOpenModel(ref) {
    this.dialogService.open(ref);
  }
  onClose(ref) {
    ref.close();
  }
  formatDate(date) {
    return moment__WEBPACK_IMPORTED_MODULE_2__(date).format('YYYY/MM/DD HH:mm');
  }
  static {
    this.ɵfac = function SampleSelectionResultComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || SampleSelectionResultComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdirectiveInject"](src_app_shared_helper_allowHelper__WEBPACK_IMPORTED_MODULE_3__.AllowHelper), _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdirectiveInject"](_nebular_theme__WEBPACK_IMPORTED_MODULE_12__.NbDialogService), _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdirectiveInject"](src_app_shared_helper_validationHelper__WEBPACK_IMPORTED_MODULE_4__.ValidationHelper), _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdirectiveInject"](src_services_api_services__WEBPACK_IMPORTED_MODULE_5__.FinalDocumentService), _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdirectiveInject"](src_app_shared_services_message_service__WEBPACK_IMPORTED_MODULE_6__.MessageService), _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_13__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_14__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdirectiveInject"](src_app_shared_services_event_service__WEBPACK_IMPORTED_MODULE_1__.EventService), _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdirectiveInject"](src_services_api_services__WEBPACK_IMPORTED_MODULE_5__.HouseService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdefineComponent"]({
      type: SampleSelectionResultComponent,
      selectors: [["ngx-sample-selection-result"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵInheritDefinitionFeature"]],
      decls: 36,
      vars: 4,
      consts: [["dialogConfirmImage", ""], ["accent", "success"], [2, "font-size", "32px"], [1, "font-bold", "text-[#818181]"], [1, "d-flex", "flex-wrap"], [1, "col-md-12"], [1, "d-flex", "justify-content-end", "w-full"], [1, "btn", "btn-info", 3, "click"], [1, "table-responsive", "mt-4"], [1, "table", "table-striped", "border", 2, "min-width", "1000px", "background-color", "#f3f3f3"], [1, "text-center", 2, "background-color", "#27ae60", "color", "white"], ["scope", "col", 1, "col-1"], ["class", "text-center", 4, "ngFor", "ngForOf"], [1, "d-flex", "justify-content-center"], ["aria-label", "Pagination", 3, "pageChange", "page", "pageSize", "collectionSize"], [1, "btn", "btn-secondary", "btn-sm", 3, "click"], [1, "text-center"], [1, "text-center", "w-32"], [1, "btn", "btn-outline-primary", "btn-sm", "m-1", 3, "click", "disabled"], [2, "width", "1000px", "max-height", "95vh"], [1, "px-4"], [1, "form-group"], ["for", "CDocumentName", "baseLabel", "", 1, "required-field", "mr-4", 2, "min-width", "75px"], ["type", "text", "nbInput", "", "placeholder", "\u6587\u4EF6\u540D\u7A31", 1, "w-full", 3, "ngModelChange", "ngModel"], ["for", "isChecked", "baseLabel", "", 1, "required-field", "mr-4", 2, "min-width", "75px"], ["status", "basic", "disabled", "", 3, "checkedChange", "checked"], ["for", "\u5BA2\u8B8A\u5716", "baseLabel", "", 1, "mr-4", 2, "min-width", "75px"], [1, "table", "border", "table-striped", 2, "min-width", "600px"], [2, "background-color", "#27ae60", "color", "white"], [4, "ngFor", "ngForOf"], [1, "form-group", "d-flex", "align-items-center"], ["for", "remark", "baseLabel", "", 1, "required-field", "align-self-start", "col-3"], [2, "color", "red"], ["name", "remark", "id", "remark", "rows", "5", "nbInput", "", 1, "w-full", 2, "resize", "none", "max-width", "none", 3, "ngModelChange", "ngModel"], ["for", "CNote", "baseLabel", "", 1, "required-field", "align-self-start", "col-3"], ["name", "CNote", "id", "CNote", "rows", "5", "nbInput", "", 1, "w-full", 2, "resize", "none", "max-width", "none", 3, "ngModelChange", "ngModel"], [1, "btn", "btn-outline-secondary", "m-2", 3, "click"], [1, "btn", "btn-success", "m-2", 3, "click"], ["status", "basic", 3, "checkedChange", "checked"]],
      template: function SampleSelectionResultComponent_Template(rf, ctx) {
        if (rf & 1) {
          const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵgetCurrentView"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](0, "nb-card", 1)(1, "nb-card-header")(2, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](3, "\u6236\u5225\u7BA1\u7406 / \u5BA2\u8B8A\u78BA\u8A8D\u5716\u8AAA");
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](4, "nb-card-body")(5, "h1", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](6, "\u60A8\u53EF\u8207\u6B64\u6AA2\u8996\u8A72\u6236\u5225\u5BA2\u6236\u5C0D\u65BC\u9078\u6A23\u7D50\u679C\u4E4B\u7C3D\u8A8D\u6587\u4EF6\uFF0C\u4E26\u53EF\u9078\u64C7\u8981\u5C07\u54EA\u4E9B\u5BA2\u8B8A\u5716\u9762\u6574\u5408\u70BA\u4E00\u4EFD\u5716\u9762\u8ACB\u5BA2\u6236\u7C3D\u56DE\u78BA\u8A8D\u3002 \u5982\u679C\u8A72\u4F4D\u5BA2\u6236\u6709\u591A\u4EFD\u7C3D\u56DE\u6A94\u6848\uFF0C\u65BC\u5BA2\u6236\u7AEF\u50C5\u6703\u986F\u793A\u6700\u65B0\u7684\u4E00\u4EFD\u6587\u4EF6\u3002");
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](7, "div", 4)(8, "div", 5)(9, "div", 6)(10, "button", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵlistener"]("click", function SampleSelectionResultComponent_Template_button_click_10_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵrestoreView"](_r1);
            const dialogConfirmImage_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵreference"](35);
            return _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵresetView"](ctx.addNew(dialogConfirmImage_r2));
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](11, " \u65B0\u589E\u78BA\u8A8D\u5BA2\u8B8A\u5716");
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](12, "div", 8)(13, "table", 9)(14, "thead")(15, "tr", 10)(16, "th", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](17, "\u985E\u578B");
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](18, "th", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](19, "\u6587\u4EF6\u540D\u7A31");
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](20, "th", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](21, "\u7C3D\u56DE\u65E5\u671F ");
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](22, "th", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](23, "\u72C0\u614B");
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](24, "th", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](25, "\u64CD\u4F5C");
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](26, "tbody");
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtemplate"](27, SampleSelectionResultComponent_tr_27_Template, 14, 9, "tr", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](28, "nb-card-footer", 13)(29, "ngb-pagination", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtwoWayListener"]("pageChange", function SampleSelectionResultComponent_Template_ngb_pagination_pageChange_29_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵrestoreView"](_r1);
            _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtwoWayBindingSet"](ctx.pageIndex, $event) || (ctx.pageIndex = $event);
            return _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵresetView"]($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵlistener"]("pageChange", function SampleSelectionResultComponent_Template_ngb_pagination_pageChange_29_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵrestoreView"](_r1);
            return _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵresetView"](ctx.pageChanged($event));
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](30, "nb-card-footer")(31, "div", 13)(32, "button", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵlistener"]("click", function SampleSelectionResultComponent_Template_button_click_32_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵrestoreView"](_r1);
            return _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵresetView"](ctx.goBack());
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](33, " \u8FD4\u56DE\u4E0A\u4E00\u9801 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtemplate"](34, SampleSelectionResultComponent_ng_template_34_Template, 54, 7, "ng-template", null, 0, _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtemplateRefExtractor"]);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](27);
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("ngForOf", ctx.listFinalDoc);
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtwoWayProperty"]("page", ctx.pageIndex);
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("pageSize", ctx.pageSize)("collectionSize", ctx.totalRecords);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_14__.NgForOf, _angular_forms__WEBPACK_IMPORTED_MODULE_15__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_15__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_15__.NgModel, _nebular_theme__WEBPACK_IMPORTED_MODULE_12__.NbCardComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_12__.NbCardBodyComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_12__.NbCardFooterComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_12__.NbCardHeaderComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_12__.NbCheckboxComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_12__.NbInputDirective, _ng_bootstrap_ng_bootstrap__WEBPACK_IMPORTED_MODULE_16__.NgbPagination, _theme_directives_label_directive__WEBPACK_IMPORTED_MODULE_7__.BaseLabelDirective, _theme_pipes_date_format_pipe__WEBPACK_IMPORTED_MODULE_8__.DateFormatPipe, _theme_pipes_mapping_pipe__WEBPACK_IMPORTED_MODULE_9__.DocumentStatusPipe, _theme_pipes_specialChangeSource_pipe__WEBPACK_IMPORTED_MODULE_10__.SpecialChangeSourcePipe],
      styles: ["/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJzYW1wbGUtc2VsZWN0aW9uLXJlc3VsdC5jb21wb25lbnQuc2NzcyJ9 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvc2FtcGxlLXNlbGVjdGlvbi1yZXN1bHQvc2FtcGxlLXNlbGVjdGlvbi1yZXN1bHQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLHdMQUF3TCIsInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 43955:
/*!*************************************************************************************************!*\
  !*** ./src/app/pages/household-management/standard-house-plan/standard-house-plan.component.ts ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   StandardHousePlanComponent: () => (/* binding */ StandardHousePlanComponent)
/* harmony export */ });
/* harmony import */ var _components_base_baseComponent__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../components/base/baseComponent */ 6250);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_shared_helper_allowHelper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/shared/helper/allowHelper */ 39290);
/* harmony import */ var _nebular_theme__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @nebular/theme */ 69375);
/* harmony import */ var src_services_api_services__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/services/api/services */ 45654);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var src_app_shared_services_message_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/shared/services/message.service */ 71029);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _ng_bootstrap_ng_bootstrap__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @ng-bootstrap/ng-bootstrap */ 48418);
/* harmony import */ var _components_breadcrumb_breadcrumb_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/breadcrumb/breadcrumb.component */ 97932);











function StandardHousePlanComponent_nb_option_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "nb-option", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const building_r2 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("value", building_r2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", building_r2.label, " ");
  }
}
function StandardHousePlanComponent_tr_29_span_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const i_r4 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", i_r4.CHouseHold, " \u3001 ");
  }
}
function StandardHousePlanComponent_tr_29_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "tr")(1, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](4, StandardHousePlanComponent_tr_29_span_4_Template, 2, 1, "span", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const item_r5 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](item_r5.CFileName);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", item_r5.CHouse);
  }
}
function StandardHousePlanComponent_ng_template_32_button_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "button", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function StandardHousePlanComponent_ng_template_32_button_5_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r7);
      const ref_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().dialogRef;
      const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r8.submitEditHouseRegularPic(ref_r8));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "\u4E0A\u50B3\u6A94\u6848");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}
function StandardHousePlanComponent_ng_template_32_div_6_label_13_Template(rf, ctx) {
  if (rf & 1) {
    const _r12 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "label", 32)(1, "nb-checkbox", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayListener"]("checkedChange", function StandardHousePlanComponent_ng_template_32_div_6_label_13_Template_nb_checkbox_checkedChange_1_listener($event) {
      const i_r13 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r12).$implicit;
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayBindingSet"](i_r13.CIsSelect, $event) || (i_r13.CIsSelect = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("checkedChange", function StandardHousePlanComponent_ng_template_32_div_6_label_13_Template_nb_checkbox_checkedChange_1_listener($event) {
      const i_r13 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r12).$implicit;
      const idx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().index;
      const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r8.checkItem($event, idx_r14, i_r13));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "span", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const i_r13 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayProperty"]("checked", i_r13.CIsSelect);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](i_r13.CHouseHold || "null");
  }
}
function StandardHousePlanComponent_ng_template_32_div_6_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 28)(1, "div", 29)(2, "label", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "div")(5, "label", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](6, " \u9069\u7528\u6236\u578B ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](7, "label", 32)(8, "span", 33);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](9, "\u9069\u7528\u6236\u578B ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](10, "nb-checkbox", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("checkedChange", function StandardHousePlanComponent_ng_template_32_div_6_Template_nb_checkbox_checkedChange_10_listener($event) {
      const houseRegularPic_r11 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r10).$implicit;
      const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r8.checkAll($event, houseRegularPic_r11));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](11, "\u5168\u9078 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](12, "div", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](13, StandardHousePlanComponent_ng_template_32_div_6_label_13_Template, 4, 2, "label", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](14, "div", 37)(15, "button", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function StandardHousePlanComponent_ng_template_32_div_6_Template_button_click_15_listener() {
      const houseRegularPic_r11 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r10).$implicit;
      const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r8.onDeleteHouseRegularPic(houseRegularPic_r11));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](16, " \u522A\u9664 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()()();
  }
  if (rf & 2) {
    const houseRegularPic_r11 = ctx.$implicit;
    const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", houseRegularPic_r11.CFileName, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("checked", ctx_r8.isAllChecked(houseRegularPic_r11));
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", houseRegularPic_r11.CHouse);
  }
}
function StandardHousePlanComponent_ng_template_32_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "nb-card", 21)(1, "nb-card-header");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2, " \u6236\u5225\u7BA1\u7406\u300B\u8A2D\u5B9A\u6236\u578B\u6A19\u6E96\u5716\u300B\u4FEE\u6539 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "nb-card-body", 22)(4, "div", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](5, StandardHousePlanComponent_ng_template_32_button_5_Template, 2, 0, "button", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](6, StandardHousePlanComponent_ng_template_32_div_6_Template, 17, 3, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](7, "nb-card-footer", 18)(8, "button", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function StandardHousePlanComponent_ng_template_32_Template_button_click_8_listener() {
      const ref_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r6).dialogRef;
      const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r8.onClose(ref_r8));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](9, "\u8FD4\u56DE\u4E0A\u4E00\u9801");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](10, "button", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function StandardHousePlanComponent_ng_template_32_Template_button_click_10_listener() {
      const ref_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r6).dialogRef;
      const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r8.onClose(ref_r8));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](11, "\u5132\u5B58");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r8.listHouseRegularPic.length);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx_r8.listHouseRegularPic);
  }
}
class StandardHousePlanComponent extends _components_base_baseComponent__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(_allow, dialogService, _houseService, route, message) {
    super(_allow);
    this._allow = _allow;
    this.dialogService = dialogService;
    this._houseService = _houseService;
    this.route = route;
    this.message = message;
    this.buildingSelectedOptions = [{
      value: '',
      label: '全部'
    }];
  }
  getListBuilding() {
    this._houseService.apiHouseGetListBuildingPost$Json({
      body: {
        CBuildCaseID: this.buildCaseId
      }
    }).subscribe(res => {
      if (res.Entries && res.StatusCode == 0) {
        this.buildingSelectedOptions = [{
          value: '',
          label: '全部'
        }, ...res.Entries.map(e => {
          return {
            value: e,
            label: e
          };
        })];
        this.selectedBuilding = this.buildingSelectedOptions[0];
        this.getListHouseRegularPic();
      }
    });
  }
  getListHouseRegularPic() {
    let param = {
      CBuildCaseID: this.buildCaseId,
      CBuildingName: this.selectedBuilding.value,
      PageIndex: this.pageIndex,
      PageSize: this.pageSize
    };
    if (!this.selectedBuilding.value) {
      delete param.CBuildingName;
    }
    this._houseService.apiHouseGetListHouseRegularPicPost$Json({
      body: param
    }).subscribe(res => {
      if (res.Entries && res.StatusCode == 0) {
        this.listHouseRegularPic = res.Entries ?? [];
        this.totalRecords = res.TotalItems;
      }
    });
  }
  checkAll(checked, houseRegularPic) {
    if (houseRegularPic.CHouse && houseRegularPic.CHouse.length > 0) {
      houseRegularPic.CHouse.forEach(item => item.CIsSelect = checked);
    }
    if (checked) {
      this.listHouseRegularPic.forEach((element, index) => {
        if (element.CRegularPictureID !== houseRegularPic.CRegularPictureID) {
          if (element.CHouse && Array.isArray(element.CHouse)) {
            element.CHouse.forEach((item, o) => {
              item.CIsSelect = false;
            });
          }
        }
      });
    }
  }
  checkItem(checked, idx, i) {
    if (checked) {
      this.listHouseRegularPic.forEach((element, index) => {
        if (index !== idx) {
          if (element.CHouse && Array.isArray(element.CHouse)) {
            element.CHouse.forEach((item, o) => {
              if (item.CHouseID === i.CHouseID) {
                item.CIsSelect = false;
              }
            });
          }
        }
      });
    }
  }
  isAllChecked(houseRegularPic) {
    return houseRegularPic.CHouse.every(item => item.CIsSelect);
  }
  extractSelectedHouses(data) {
    const result = [];
    for (const item of data) {
      for (const house of item.CHouse) {
        if (house.CIsSelect) {
          result.push({
            CHouseID: house.CHouseID,
            CRegularPictureID: item.CRegularPictureID
          });
        }
      }
    }
    return result;
  }
  submitEditHouseRegularPic(ref) {
    let bodyHouseRegularPic = this.extractSelectedHouses(this.listHouseRegularPic);
    this._houseService.apiHouseEditHouseRegularPicPost$Json({
      body: {
        CHousePic: bodyHouseRegularPic
      }
    }).subscribe(res => {
      if (res.StatusCode === 0) {
        this.message.showSucessMSG("執行成功");
        ref.close();
      }
    });
  }
  onDeleteHouseRegularPic(houseRegularPic) {
    if (window.confirm(`確定要刪除【項目${houseRegularPic.CFileName}】?`)) {
      this._houseService.apiHouseDeleteRegularPicturePost$Json({
        body: {
          CRegularPictureID: houseRegularPic.CRegularPictureID
        }
      }).subscribe(res => {
        if (res.StatusCode === 0) {
          this.message.showSucessMSG("執行成功");
          this.getListHouseRegularPic();
        }
      });
    }
  }
  clear() {
    this.selectedBuilding = this.buildingSelectedOptions[0];
  }
  ngOnInit() {
    this.route.paramMap.subscribe(params => {
      if (params) {
        const idParam = params.get('id');
        const id = idParam ? +idParam : 0;
        this.buildCaseId = id;
        this.getListBuilding();
      }
    });
  }
  pageChanged(newPage) {
    this.pageIndex = newPage;
    this.getListHouseRegularPic();
  }
  onOpen(ref) {
    this.dialogService.open(ref);
  }
  onClose(ref) {
    ref.close();
  }
  static {
    this.ɵfac = function StandardHousePlanComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || StandardHousePlanComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](src_app_shared_helper_allowHelper__WEBPACK_IMPORTED_MODULE_1__.AllowHelper), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbDialogService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](src_services_api_services__WEBPACK_IMPORTED_MODULE_2__.HouseService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](src_app_shared_services_message_service__WEBPACK_IMPORTED_MODULE_3__.MessageService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineComponent"]({
      type: StandardHousePlanComponent,
      selectors: [["ngx-standard-house-plan"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵInheritDefinitionFeature"]],
      decls: 34,
      vars: 6,
      consts: [["dialog", ""], ["accent", "success"], [1, "font-bold", "text-[#818181]"], [1, "d-flex", "flex-wrap"], [1, "col-md-6"], [1, "form-group", "d-flex", "align-items-center", "w-full"], ["for", "buildingName", 1, "label", "col-3"], ["placeholder", "\u72C0\u614B", 1, "w-full", 3, "ngModelChange", "ngModel"], [3, "value", 4, "ngFor", "ngForOf"], [1, "d-flex", "justify-content-end", "w-full"], [1, "btn", "btn-secondary", 3, "click"], [1, "btn", "btn-secondary", "mx-2", 3, "click"], [1, "btn", "btn-info", 3, "click"], [1, "table-responsive", "mt-4"], [1, "table", "table-striped", "border", 2, "min-width", "1000px", "background-color", "#f3f3f3"], [2, "background-color", "#27ae60", "color", "white"], ["scope", "col", 1, "col-1"], [4, "ngFor", "ngForOf"], [1, "d-flex", "justify-content-center"], ["aria-label", "Pagination", 3, "pageChange", "page", "pageSize", "collectionSize"], [3, "value"], [2, "width", "700px", "max-height", "95vh"], [1, "px-4"], [1, "d-flex", "justify-end"], ["class", "btn btn-primary mx-2", 3, "click", 4, "ngIf"], ["class", "bg-white p-4 rounded shadow m-2", 4, "ngFor", "ngForOf"], [1, "btn", "btn-primary", "btn-sm", "mx-2", 3, "click"], [1, "btn", "btn-primary", "mx-2", 3, "click"], [1, "bg-white", "p-4", "rounded", "shadow", "m-2"], [1, "mb-2"], ["for", "standard-drawing", 1, "block", "text-gray-700", "font-bold", "mb-2"], ["for", "applicable-models", 1, "block", "text-gray-700", "font-bold", "mb-2"], [1, "inline-flex", "items-center", "mr-4"], [1, "mr-2"], ["status", "basic", 3, "checkedChange", "checked"], [1, "flex", "flex-wrap"], ["class", "inline-flex items-center mr-4", 4, "ngFor", "ngForOf"], [1, "w-full", "text-right"], ["type", "button", 1, "btn", "btn-outline-dark", "py-2", "px-4", "btn-sm", "ml-6", 3, "click"], [1, "ml-2"]],
      template: function StandardHousePlanComponent_Template(rf, ctx) {
        if (rf & 1) {
          const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "nb-card", 1)(1, "nb-card-header");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "ngx-breadcrumb");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "nb-card-body");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](4, "h1", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "div", 3)(6, "div", 4)(7, "div", 5)(8, "label", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](9, "\u68DF\u5225");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](10, "nb-select", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayListener"]("ngModelChange", function StandardHousePlanComponent_Template_nb_select_ngModelChange_10_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r1);
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayBindingSet"](ctx.selectedBuilding, $event) || (ctx.selectedBuilding = $event);
            return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"]($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](11, StandardHousePlanComponent_nb_option_11_Template, 2, 2, "nb-option", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](12, "div", 4)(13, "div", 9)(14, "button", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function StandardHousePlanComponent_Template_button_click_14_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r1);
            return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx.clear());
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](15, " \u6E05\u9664 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](16, "button", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function StandardHousePlanComponent_Template_button_click_16_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r1);
            return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx.getListHouseRegularPic());
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](17, " \u67E5\u8A62 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](18, "button", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function StandardHousePlanComponent_Template_button_click_18_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r1);
            const dialog_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](33);
            return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx.onOpen(dialog_r3));
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](19, " \u68DF\u5225 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](20, "div", 13)(21, "table", 14)(22, "thead")(23, "tr", 15)(24, "th", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](25, "\u6A94\u6848");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](26, "th", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](27, "\u9069\u7528\u6236\u578B ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](28, "tbody");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](29, StandardHousePlanComponent_tr_29_Template, 5, 2, "tr", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](30, "nb-card-footer", 18)(31, "ngb-pagination", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayListener"]("pageChange", function StandardHousePlanComponent_Template_ngb_pagination_pageChange_31_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r1);
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayBindingSet"](ctx.pageIndex, $event) || (ctx.pageIndex = $event);
            return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"]($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("pageChange", function StandardHousePlanComponent_Template_ngb_pagination_pageChange_31_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r1);
            return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx.pageChanged($event));
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](32, StandardHousePlanComponent_ng_template_32_Template, 12, 2, "ng-template", null, 0, _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplateRefExtractor"]);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayProperty"]("ngModel", ctx.selectedBuilding);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx.buildingSelectedOptions);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](18);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx.listHouseRegularPic);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayProperty"]("page", ctx.pageIndex);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("pageSize", ctx.pageSize)("collectionSize", ctx.totalRecords);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_8__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.NgModel, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbCardComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbCardBodyComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbCardFooterComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbCardHeaderComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbCheckboxComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbSelectComponent, _nebular_theme__WEBPACK_IMPORTED_MODULE_6__.NbOptionComponent, _ng_bootstrap_ng_bootstrap__WEBPACK_IMPORTED_MODULE_10__.NgbPagination, _components_breadcrumb_breadcrumb_component__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbComponent],
      styles: ["/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJzdGFuZGFyZC1ob3VzZS1wbGFuLmNvbXBvbmVudC5zY3NzIn0= */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvc3RhbmRhcmQtaG91c2UtcGxhbi9zdGFuZGFyZC1ob3VzZS1wbGFuLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSxvTEFBb0wiLCJzb3VyY2VSb290IjoiIn0= */"]
    });
  }
}

/***/ }),

/***/ 44336:
/*!***********************************************!*\
  !*** ./src/app/services/quotation.service.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QuotationService: () => (/* binding */ QuotationService)
/* harmony export */ });
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs/operators */ 70271);
/* harmony import */ var _models_quotation_model__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../models/quotation.model */ 21324);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _services_api_services_quotation_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../services/api/services/quotation.service */ 33006);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common/http */ 46443);





class QuotationService {
  constructor(apiQuotationService, http) {
    this.apiQuotationService = apiQuotationService;
    this.http = http;
    this.apiUrl = '/api/Quotation';
  }
  // 取得報價單列表
  getQuotationList(request) {
    return this.apiQuotationService.apiQuotationGetListPost$Json({
      body: request
    });
  }
  // 取得單筆報價單資料
  getQuotationData(quotationId) {
    const request = {
      cQuotationID: quotationId
    };
    return this.apiQuotationService.apiQuotationGetDataPost$Json({
      body: request
    });
  } // 取得戶別的報價項目
  getQuotationByHouseId(houseId) {
    const request = {
      cHouseID: houseId
    };
    return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({
      body: request
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.map)(response => {
      // 將 API 響應的小寫屬性名轉換為前端期望的大寫屬性名
      if (response) {
        return {
          StatusCode: response.StatusCode,
          Message: response.Message,
          TotalItems: response.TotalItems,
          Entries: response.Entries // 保持原始結構，因為 entries 是一個物件而不是陣列
        };
      }
      return response;
    }));
  }
  // 儲存報價單 (支援單一項目)
  saveQuotationItem(quotation) {
    return this.apiQuotationService.apiQuotationSaveDataPost$Json({
      body: quotation
    });
  } // 儲存報價單 (支援批量保存多個項目) - 使用單一請求
  saveQuotation(request) {
    // 將 QuotationItem 轉換為 QuotationItemModel 格式
    const quotationItems = request.items.map(item => {
      const quotationType = item.CQuotationItemType && item.CQuotationItemType > 0 ? item.CQuotationItemType : _models_quotation_model__WEBPACK_IMPORTED_MODULE_0__.CQuotationItemType.自定義;
      return {
        CItemName: item.cItemName,
        CUnit: item.cUnit || '',
        CUnitPrice: item.cUnitPrice,
        CCount: item.cCount,
        CStatus: item.cStatus || 1,
        CQuotationItemType: quotationType,
        CRemark: item.cRemark || ''
      };
    });
    // 建立 SaveDataQuotation 請求，並直接添加額外費用欄位
    const saveRequest = {
      CHouseID: request.houseId,
      CQuotationID: request.quotationId || 0,
      // 使用傳入的 quotationId，如果沒有則為 0（新建報價單）
      Items: quotationItems,
      // 額外費用相關欄位 - 直接添加到請求中
      CShowOther: request.cShowOther || false,
      COtherName: request.cOtherName || '',
      COtherPercent: request.cOtherPercent || 0
    };
    // 調試用 - 印出最終送出的請求資料
    console.log('QuotationService saveRequest:', saveRequest);
    return this.apiQuotationService.apiQuotationSaveDataPost$Json({
      body: saveRequest
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.map)(response => ({
      success: response?.StatusCode === 0,
      message: response?.StatusCode === 0 ? '報價單保存成功' : '報價單保存失敗',
      data: request.items
    })));
  }
  // 取得預設報價項目 (保持原有方法以兼容現有代碼)
  getDefaultQuotationItems() {
    // 使用 GetList 方法獲取預設項目
    const request = {
      pageIndex: 0,
      pageSize: 100
      // 其他預設參數可能需要根據實際 API 需求調整
    };
    return this.apiQuotationService.apiQuotationGetListPost$Json({
      body: request
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.map)(response => {
      // 轉換 API 響應格式以保持兼容性
      return {
        success: response.StatusCode === 0,
        // 假設 statusCode 0 表示成功
        message: response.Message || '',
        data: response.Entries || []
      };
    }));
  } // 載入預設報價項目 (LoadDefaultItems API)
  loadDefaultItems(request) {
    // 使用注入的 HttpClient 直接發送請求，但使用 apiQuotationService 的 rootUrl
    return this.http.post(`${this.apiQuotationService.rootUrl}/api/Quotation/LoadDefaultItems`, request).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.map)(response => {
      // 轉換 API 響應格式以保持兼容性
      return {
        success: response.StatusCode === 0,
        // 假設 StatusCode 0 表示成功
        message: response.Message || '',
        data: response.Entries || []
      };
    }));
  } // 載入常規報價項目 (LoadRegularItems API)
  loadRegularItems(request) {
    // 使用注入的 HttpClient 直接發送請求，但使用 apiQuotationService 的 rootUrl
    return this.http.post(`${this.apiQuotationService.rootUrl}/api/Quotation/LoadRegularItems`, request).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.map)(response => {
      // 轉換 API 響應格式以保持兼容性
      return {
        success: response.StatusCode === 0,
        // 假設 StatusCode 0 表示成功
        message: response.Message || '',
        data: response.Entries || []
      };
    }));
  }
  // 更新報價項目 (使用 SaveData 方法)
  updateQuotationItem(quotationId, item) {
    const saveData = {
      CHouseID: item.cHouseID,
      CQuotationID: quotationId,
      Items: [{
        CItemName: item.cItemName,
        CUnitPrice: item.cUnitPrice,
        CCount: item.cCount,
        CStatus: item.cStatus || 1,
        CQuotationItemType: item.CQuotationItemType || _models_quotation_model__WEBPACK_IMPORTED_MODULE_0__.CQuotationItemType.自定義
      }]
    };
    return this.apiQuotationService.apiQuotationSaveDataPost$Json({
      body: saveData
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.map)(response => {
      return {
        success: response.StatusCode === 0,
        // 假設 StatusCode 0 表示成功
        message: response.Message || '',
        data: [item] // 包裝為陣列以符合 QuotationResponse 格式
      };
    }));
  }
  // 匯出報價單 (需要另外實作，因為 swagger 中沒有此 API)
  exportQuotation(houseId) {
    // 這個方法可能需要使用其他 API 或保持原有實作
    // 暫時拋出錯誤提示需要實作
    throw new Error('Export quotation functionality needs to be implemented separately');
  }
  // 鎖定報價單
  lockQuotation(quotationId) {
    return this.http.post(`${this.apiQuotationService.rootUrl}/api/Quotation/LockQuotation`, quotationId).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.map)(response => {
      return {
        success: response.StatusCode === 0,
        message: response.Message || '',
        data: response.Entries || null
      };
    }));
  }
  static {
    this.ɵfac = function QuotationService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || QuotationService)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵinject"](_services_api_services_quotation_service__WEBPACK_IMPORTED_MODULE_1__.QuotationService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_4__.HttpClient));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineInjectable"]({
      token: QuotationService,
      factory: QuotationService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 65938:
/*!**************************************************!*\
  !*** ./src/app/shared/enum/enumHouseProgress.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EnumHouseProgress: () => (/* binding */ EnumHouseProgress)
/* harmony export */ });
var EnumHouseProgress;
(function (EnumHouseProgress) {
  EnumHouseProgress[EnumHouseProgress["\u5C1A\u672A\u958B\u59CB"] = 0] = "\u5C1A\u672A\u958B\u59CB";
  EnumHouseProgress[EnumHouseProgress["\u5DF2\u95B1\u8B80\u64CD\u4F5C\u8AAA\u660E"] = 1] = "\u5DF2\u95B1\u8B80\u64CD\u4F5C\u8AAA\u660E";
  EnumHouseProgress[EnumHouseProgress["\u9078\u6A23\u5B8C\u6210"] = 2] = "\u9078\u6A23\u5B8C\u6210";
  EnumHouseProgress[EnumHouseProgress["\u7C3D\u7F72\u5B8C\u6210"] = 3] = "\u7C3D\u7F72\u5B8C\u6210";
})(EnumHouseProgress || (EnumHouseProgress = {}));

/***/ }),

/***/ 533:
/*!**********************************************!*\
  !*** ./src/app/shared/enum/enumPayStatus.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EnumPayStatus: () => (/* binding */ EnumPayStatus)
/* harmony export */ });
var EnumPayStatus;
(function (EnumPayStatus) {
  EnumPayStatus[EnumPayStatus["\u672A\u4ED8\u6B3E"] = 0] = "\u672A\u4ED8\u6B3E";
  EnumPayStatus[EnumPayStatus["\u5DF2\u4ED8\u6B3E"] = 1] = "\u5DF2\u4ED8\u6B3E";
  EnumPayStatus[EnumPayStatus["\u7121\u9808\u4ED8\u6B3E"] = 2] = "\u7121\u9808\u4ED8\u6B3E";
})(EnumPayStatus || (EnumPayStatus = {}));

/***/ }),

/***/ 92033:
/*!****************************************************!*\
  !*** ./src/app/shared/enum/enumQuotationStatus.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EnumQuotationStatus: () => (/* binding */ EnumQuotationStatus)
/* harmony export */ });
var EnumQuotationStatus;
(function (EnumQuotationStatus) {
  EnumQuotationStatus[EnumQuotationStatus["\u5F85\u5831\u50F9"] = 1] = "\u5F85\u5831\u50F9";
  EnumQuotationStatus[EnumQuotationStatus["\u5DF2\u5831\u50F9"] = 2] = "\u5DF2\u5831\u50F9";
  EnumQuotationStatus[EnumQuotationStatus["\u5DF2\u7C3D\u56DE"] = 3] = "\u5DF2\u7C3D\u56DE";
})(EnumQuotationStatus || (EnumQuotationStatus = {}));

/***/ }),

/***/ 23718:
/*!***********************************************!*\
  !*** ./src/app/shared/enum/enumSignStatus.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EnumSignStatus: () => (/* binding */ EnumSignStatus)
/* harmony export */ });
var EnumSignStatus;
(function (EnumSignStatus) {
  EnumSignStatus[EnumSignStatus["\u5DF2\u7C3D\u56DE"] = 1] = "\u5DF2\u7C3D\u56DE";
  EnumSignStatus[EnumSignStatus["\u672A\u7C3D\u56DE"] = 2] = "\u672A\u7C3D\u56DE"; //not signed back
})(EnumSignStatus || (EnumSignStatus = {}));

/***/ }),

/***/ 96634:
/*!***************************************************!*\
  !*** ./src/assets/template/quotation-template.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QUOTATION_TEMPLATE: () => (/* binding */ QUOTATION_TEMPLATE)
/* harmony export */ });
const QUOTATION_TEMPLATE = `<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <title>報價單列印模板</title>
  <style>
    body {
      font-family: 'Microsoft JhengHei', '微軟正黑體', Arial, sans-serif;
      margin: 20px;
      font-size: 14px;
      line-height: 1.6;
    }

    .header {
      text-align: center;
      margin-bottom: 30px;
    }

    .header h1 {
      margin: 0;
      font-size: 24px;
      color: #333;
      font-weight: bold;
    }

    .info-section {
      margin-bottom: 20px;
      border-bottom: 1px solid #ddd;
      padding-bottom: 15px;
    }

    .info-row {
      display: flex;
      margin-bottom: 8px;
    }

    .info-label {
      font-weight: bold;
      width: 100px;
      flex-shrink: 0;
    }

    .info-value {
      flex: 1;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }

    th {
      background-color: #27ae60;
      color: white;
      border: 1px solid #ddd;
      padding: 10px 8px;
      text-align: center;
      font-weight: bold;
    }

    td {
      border: 1px solid #ddd;
      padding: 8px;
    }

    .text-center {
      text-align: center;
    }

    .text-right {
      text-align: right;
    }

    .total-section {
      text-align: right;
      margin-top: 20px;
      padding-top: 15px;
      border-top: 2px solid #27ae60;
    }

    .subtotal {
      font-size: 14px;
      margin-bottom: 5px;
      color: #666;
    }

    .additional-fee {
      font-size: 14px;
      margin-bottom: 10px;
      color: #666;
    }

    .total-amount {
      font-size: 18px;
      font-weight: bold;
      color: #27ae60;
      border-top: 1px solid #ddd;
      padding-top: 10px;
    }

    .footer {
      margin-top: 40px;
      text-align: center;
      font-size: 12px;
      color: #666;
    }

    .signature-section {
      margin-top: 40px;
      page-break-inside: avoid;
    }

    .signature-box {
      width: 300px;
      margin: 0 auto;
      text-align: center;
    }

    .signature-label {
      font-weight: bold;
      margin-bottom: 40px;
      font-size: 16px;
    }

    .signature-line {
      border-bottom: 2px solid #000;
      height: 60px;
      margin-bottom: 10px;
      position: relative;
    }

    .signature-date {
      font-size: 14px;
      margin-top: 15px;
    }

    .signature-notes {
      margin-top: 30px;
      padding: 15px;
      background-color: #f9f9f9;
      border-left: 4px solid #27ae60;
    }

    .signature-notes p {
      margin: 0 0 10px 0;
      font-weight: bold;
    }

    .signature-notes ul {
      margin: 0;
      padding-left: 20px;
    }

    .signature-notes li {
      margin-bottom: 5px;
      line-height: 1.4;
    }

    @media print {
      body {
        margin: 0;
      }

      .header {
        page-break-inside: avoid;
      }

      .signature-section {
        page-break-inside: avoid;
      }
    }
  </style>
</head>

<body>
  <div class="header">
    <h1>報價單</h1>
  </div>

  <div class="info-section">
    <div class="info-row">
      <span class="info-label">建案名稱：</span>
      <span class="info-value">{{buildCaseName}}</span>
    </div>
    <div class="info-row">
      <span class="info-label">戶別：</span>
      <span class="info-value">{{houseHold}}</span>
    </div>
    <div class="info-row">
      <span class="info-label">樓層：</span>
      <span class="info-value">{{floor}}樓</span>
    </div>
    <div class="info-row">
      <span class="info-label">列印日期：</span>
      <span class="info-value">{{printDate}}</span>
    </div>
  </div>

  <table>
    <thead>
      <tr>
        <th width="8%">序號</th>
        <th width="30%">項目名稱</th>
        <th width="12%">單價 (元)</th>
        <th width="8%">單位</th>
        <th width="8%">數量</th>
        <th width="15%">小計 (元)</th>
        <th width="12%">類型</th>
      </tr>
    </thead>
    <tbody>
      {{itemsHtml}}
    </tbody>
  </table>

  <div class="total-section">
    <div class="subtotal">
      小計：{{subtotalAmount}}
    </div>
    {{additionalFeeHtml}}
    <div class="total-amount">
      總金額：{{totalAmount}}
    </div>
  </div>

  <div class="signature-section">
    <div class="signature-box">
      <div class="signature-label">客戶簽名：</div>
      <div class="signature-line"></div>
      <div class="signature-date">日期：_____年_____月_____日</div>
    </div>

    <div class="signature-notes">
      <p><strong>注意事項：</strong></p>
      <ul>
        <li>此報價單有效期限為30天，逾期需重新報價</li>
        <li>報價內容若有異動，請重新確認</li>
        <li>簽名確認後即視為同意此報價內容</li>
      </ul>
    </div>
  </div>

  <div class="footer">
    此報價單由系統自動產生，列印時間：{{printDateTime}}
  </div>
</body>

</html>`;

/***/ })

}]);
//# sourceMappingURL=src_app_pages_household-management_household-management_module_ts.js.map