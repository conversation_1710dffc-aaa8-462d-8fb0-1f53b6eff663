/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { GetListByHouseIdRequest } from '../../models/get-list-by-house-id-request';
import { SaveDataQuotationResponseBase } from '../../models/save-data-quotation-response-base';

export interface ApiQuotationGetListByHouseIdPost$Json$Params {
      body?: GetListByHouseIdRequest
}

export function apiQuotationGetListByHouseIdPost$Json(http: HttpClient, rootUrl: string, params?: ApiQuotationGetListByHouseIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<SaveDataQuotationResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiQuotationGetListByHouseIdPost$Json.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'text/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<SaveDataQuotationResponseBase>;
    })
  );
}

apiQuotationGetListByHouseIdPost$Json.PATH = '/api/Quotation/GetListByHouseID';
